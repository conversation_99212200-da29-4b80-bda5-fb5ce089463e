# Financial & Manager Dashboard for Laravel Nova

This comprehensive dashboard provides financial and management insights for the iRoom application, featuring multiple charts and metrics across different business areas.

## Dashboard Overview

The **Financial & Manager Dashboard** is designed to provide executives and managers with key performance indicators (KPIs) and trends across four main areas:

1. **Financial Metrics** - Revenue and transaction analysis
2. **User Metrics** - User growth and activity tracking
3. **Google Meet Metrics** - Meeting usage and trends
4. **Licence Metrics** - Licence management and revenue
5. **File Metrics** - File storage and upload analysis
6. **Google Audit Metrics** - Meeting audit and duration tracking

## Available Metrics

### Financial Metrics

#### 1. Total Revenue
- **Type**: Value Metric
- **Description**: Shows total revenue from successful transactions
- **Data Source**: `BankTransaction` model (completed transactions only)
- **Format**: Formatted in تومان (Iranian Toman)
- **Time Ranges**: Today, Week, 30 Days, 60 Days, 365 Days, MTD, QTD, YTD

#### 2. Revenue Trend
- **Type**: Trend Metric
- **Description**: Displays revenue trends over time with daily breakdown
- **Data Source**: `BankTransaction` model (completed transactions only)
- **Time Ranges**: 7 Days, 30 Days, 60 Days, 90 Days, 365 Days

#### 3. Average Transaction Amount
- **Type**: Value Metric
- **Description**: Shows average transaction value
- **Data Source**: `BankTransaction` model (completed transactions only)
- **Format**: Formatted in تومان (Iranian Toman)
- **Time Ranges**: Today, Week, 30 Days, 60 Days, 365 Days, MTD, QTD, YTD

#### 4. Transaction Status Distribution
- **Type**: Partition Metric
- **Description**: Shows distribution of transactions by status
- **Data Source**: `BankTransaction` model
- **Time Ranges**: 7 Days, 30 Days, 60 Days, 365 Days

### User Metrics

#### 1. New Users Trend
- **Type**: Trend Metric
- **Description**: Shows new user registration trends over time
- **Data Source**: `User` model
- **Time Ranges**: 7 Days, 30 Days, 60 Days, 90 Days, 365 Days

#### 2. Active Users Count
- **Type**: Value Metric
- **Description**: Shows count of users active in the last 30 days
- **Data Source**: `User` model (users created in last 30 days)
- **Time Ranges**: 7 Days, 30 Days, 60 Days, 90 Days, 365 Days

#### 3. Users by Role
- **Type**: Partition Metric
- **Description**: Shows user distribution by role/type
- **Data Source**: `User` model (gauth_type field)
- **Time Ranges**: 7 Days, 30 Days, 60 Days, 365 Days

### Google Meet Metrics

#### 1. Total Meetings
- **Type**: Value Metric
- **Description**: Shows total number of meetings created
- **Data Source**: `GoogleMeet` model
- **Time Ranges**: Today, Week, 30 Days, 60 Days, 365 Days, MTD, QTD, YTD

#### 2. Meetings Trend
- **Type**: Trend Metric
- **Description**: Shows meeting creation trends over time
- **Data Source**: `GoogleMeet` model
- **Time Ranges**: 7 Days, 30 Days, 60 Days, 90 Days, 365 Days

#### 3. Meetings by Status
- **Type**: Partition Metric
- **Description**: Shows distribution of meetings by status
- **Data Source**: `GoogleMeet` model
- **Time Ranges**: 7 Days, 30 Days, 60 Days, 365 Days

### Licence Metrics

#### 1. Expired Licences
- **Type**: Value Metric
- **Description**: Shows count of expired licences (end_at < now)
- **Data Source**: `LicenceUser` model
- **Time Ranges**: Today, Week, 30 Days, 60 Days, 365 Days, MTD, QTD, YTD

#### 2. Upcoming Expirations
- **Type**: Value Metric
- **Description**: Shows licences expiring in the next 30 days
- **Data Source**: `LicenceUser` model (end_at between now and 30 days)
- **Time Ranges**: 7 Days, 30 Days, 60 Days, 90 Days

#### 3. Licence Revenue Trend
- **Type**: Trend Metric
- **Description**: Shows revenue trends from licences over time
- **Data Source**: `Licence` model (price field)
- **Format**: Formatted in تومان (Iranian Toman)
- **Time Ranges**: 7 Days, 30 Days, 60 Days, 90 Days, 365 Days

#### 4. Licences by Status
- **Type**: Partition Metric
- **Description**: Shows distribution of licences by status
- **Data Source**: `LicenceUser` model
- **Time Ranges**: 7 Days, 30 Days, 60 Days, 365 Days

### File Metrics

#### 1. Total File Size (GB)
- **Type**: Value Metric
- **Description**: Shows total size of all uploaded files
- **Data Source**: `MeetingFile` model (file_size field)
- **Format**: Formatted in GB (Gigabytes)
- **Time Ranges**: Today, Week, 30 Days, 60 Days, 365 Days, MTD, QTD, YTD

#### 2. Total Files Count
- **Type**: Value Metric
- **Description**: Shows total number of uploaded files
- **Data Source**: `MeetingFile` model
- **Time Ranges**: Today, Week, 30 Days, 60 Days, 365 Days, MTD, QTD, YTD

#### 3. Files Upload Trend
- **Type**: Trend Metric
- **Description**: Shows file upload trends over time
- **Data Source**: `MeetingFile` model
- **Time Ranges**: 7 Days, 30 Days, 60 Days, 90 Days, 365 Days

### Google Audit Metrics

#### 1. Total Audit Reports
- **Type**: Value Metric
- **Description**: Shows total number of audit reports
- **Data Source**: `GoogleAuditEvents` model
- **Time Ranges**: Today, Week, 30 Days, 60 Days, 365 Days, MTD, QTD, YTD

#### 2. Total Duration (Hours)
- **Type**: Value Metric
- **Description**: Shows total duration from all audit events
- **Data Source**: `GoogleAuditReport` model (duration_second field)
- **Format**: Formatted in hours
- **Time Ranges**: Today, Week, 30 Days, 60 Days, 365 Days, MTD, QTD, YTD

#### 3. Audit Reports Trend
- **Type**: Trend Metric
- **Description**: Shows audit report generation trends over time
- **Data Source**: `GoogleAuditEvents` model
- **Time Ranges**: 7 Days, 30 Days, 60 Days, 90 Days, 365 Days

## Dashboard Layout

The dashboard is organized in a responsive grid layout with the following structure:

```
┌─────────────────┬─────────────────┬─────────────────┐
│ Total Revenue   │ Revenue Trend   │ Avg Transaction │
│ (1/3 width)    │ (2/3 width)    │ (1/3 width)    │
├─────────────────┼─────────────────┼─────────────────┤
│ Transaction     │ New Users       │ Active Users    │
│ Status Dist.    │ Trend           │ Count           │
│ (1/3 width)    │ (1/2 width)    │ (1/2 width)    │
├─────────────────┼─────────────────┼─────────────────┤
│ Users by Role   │ Meetings Count  │ Meetings Trend  │
│ (1/3 width)    │ (1/3 width)    │ (1/2 width)    │
├─────────────────┼─────────────────┼─────────────────┤
│ Meetings by     │ Expired         │ Upcoming        │
│ Status          │ Licences        │ Expirations     │
│ (1/3 width)    │ (1/3 width)    │ (1/3 width)    │
├─────────────────┼─────────────────┼─────────────────┤
│ Licence Revenue │ Licences by     │ Total File      │
│ Trend           │ Status          │ Size (GB)       │
│ (1/2 width)    │ (1/3 width)    │ (1/3 width)    │
├─────────────────┼─────────────────┼─────────────────┤
│ Files Count     │ Files Upload    │ Audit Reports   │
│ (1/3 width)    │ Trend           │ Count           │
│                 │ (1/2 width)    │ (1/3 width)    │
├─────────────────┼─────────────────┼─────────────────┤
│ Total Duration  │ Audit Reports   │                 │
│ (Hours)         │ Trend           │                 │
│ (1/3 width)    │ (1/2 width)    │                 │
└─────────────────┴─────────────────┴─────────────────┘
```

## Technical Implementation

### File Structure
```
app/Nova/
├── Dashboards/
│   └── FinancialManager.php
└── Metrics/
    ├── BankTransactionMetrics/
    │   ├── TotalRevenue.php
    │   ├── RevenueTrend.php
    │   ├── TransactionStatusPartition.php
    │   └── AverageTransactionAmount.php
    ├── UserMetrics/
    │   ├── NewUsersTrend.php
    │   ├── ActiveUsersCount.php
    │   └── UsersByRolePartition.php
    ├── GoogleMeetMetrics/
    │   ├── MeetingsCount.php
    │   ├── MeetingsTrend.php
    │   └── MeetingsByStatusPartition.php
    ├── LicenceMetrics/
    │   ├── ExpiredLicencesCount.php
    │   ├── UpcomingExpirations.php
    │   ├── LicenceRevenue.php
    │   └── LicencesByStatusPartition.php
    ├── FileMetrics/
    │   ├── TotalFileSize.php
    │   ├── FilesCount.php
    │   └── FilesTrend.php
    └── GoogleAuditMetrics/
        ├── AuditReportsCount.php
        ├── TotalDuration.php
        └── AuditTrend.php
```

### Key Features

1. **Caching**: All metrics are cached for 5 minutes to improve performance
2. **Responsive Design**: Metrics are arranged in responsive grid layout
3. **Multiple Time Ranges**: Each metric supports various time range options
4. **Data Filtering**: Financial metrics only include completed transactions
5. **Currency Formatting**: Revenue metrics are formatted in تومان
6. **Real-time Updates**: Metrics update automatically based on selected time ranges

### Data Sources

- **BankTransaction**: Financial transaction data
- **User**: User registration and activity data
- **GoogleMeet**: Meeting creation and usage data
- **LicenceUser**: Licence assignment and expiration data
- **Licence**: Licence pricing and configuration data
- **MeetingFile**: File upload and storage data
- **GoogleAuditEvents**: Meeting audit and duration data

## Usage

1. Access the dashboard through Laravel Nova admin panel
2. Select "Financial & Manager Dashboard" from the sidebar
3. Use the time range selectors to filter data
4. Hover over charts for detailed information
5. Click on metrics to drill down into specific data

## Customization

### Adding New Metrics

1. Create a new metric class in the appropriate directory
2. Extend the appropriate Nova metric base class (Value, Trend, or Partition)
3. Implement the required methods (calculate, ranges, cacheFor, etc.)
4. Add the metric to the FinancialManager dashboard cards array

### Modifying Existing Metrics

1. Edit the metric class file
2. Update the calculate method for new logic
3. Modify ranges for different time periods
4. Update the name and format methods as needed

### Styling

The dashboard uses Nova's built-in styling system. Metrics can be customized with:
- `width()` method for responsive sizing
- Custom CSS classes for additional styling
- Nova's theme customization options

## Performance Considerations

- All metrics are cached for 5 minutes
- Database queries are optimized with proper indexing
- Large datasets are handled efficiently through Nova's query optimization
- Consider database indexing on frequently queried fields:
  - `bank_transactions.status`, `bank_transactions.total_amount`
  - `users.created_at`, `users.gauth_type`
  - `google_meets.created_at`, `google_meets.status`
  - `licence_users.end_at`, `licence_users.status`

## Troubleshooting

### Common Issues

1. **Metrics not loading**: Check database connections and table existence
2. **Incorrect data**: Verify enum values and status mappings
3. **Performance issues**: Ensure proper database indexing
4. **Cache issues**: Clear Nova cache with `php artisan nova:publish`

### Debugging

- Check Laravel logs for database query errors
- Verify enum values match database records
- Test individual metrics in isolation
- Use Nova's built-in debugging tools

## Future Enhancements

Potential improvements for the dashboard:

1. **Export functionality** for metric data
2. **Custom date ranges** for more flexible filtering
3. **Drill-down capabilities** for detailed analysis
4. **Alert system** for threshold-based notifications
5. **Comparative analysis** between time periods
6. **Mobile-responsive** chart optimizations
7. **Real-time updates** using WebSockets
8. **Custom metric calculations** for business-specific KPIs 