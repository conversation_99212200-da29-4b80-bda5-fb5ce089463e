@if ($paginator->hasPages())
    <nav>
        <ul class="pagination direction-rtl position-absolute pt-4 end-50 bottom-0" style="transform: translate(50%, 100%);">
            {{-- Previous Page Link --}}
            @if ($paginator->onFirstPage())

                <li class="page-item previous disabled"  aria-disabled="true" ><a href="#" class="page-link bg-transparent bg-hover-light-primary"><i class="next"></i></a></li>
            @else
                <li class="page-item previous"  aria-label="@lang('pagination.previous')" ><a href="{{ $paginator->previousPageUrl() }}" class="page-link bg-transparent bg-hover-light-primary"><i class="next"></i></a></li>

            @endif

            {{-- Pagination Elements --}}
            @foreach ($elements as $element)
                {{-- "Three Dots" Separator --}}
                @if (is_string($element))
                    <li class="disabled" aria-disabled="true"><span>{{ $element }}</span></li>
                @endif

                {{-- Array Of Links --}}
                @if (is_array($element))
                    @foreach ($element as $page => $url)
                        @if ($page == $paginator->currentPage())

                            <li class="page-item active"><a href="#" class="page-link">{{ $page }}</a></li>
                        @else
                            <li class="page-item "><a href="{{ $url }}" class="page-link">{{ $page }}</a></li>
                        @endif
                    @endforeach
                @endif
            @endforeach

            {{-- Next Page Link --}}
            @if ($paginator->hasMorePages())

                <li class="page-item next"  rel="next" aria-label="@lang('pagination.next')">
                    <a href="{{ $paginator->nextPageUrl() }}"  class="page-link bg-transparent bg-hover-light-primary"><i class="previous"></i></a>
                </li>
            @else


                <li class="page-item next disabled" aria-disabled="true" aria-label="@lang('pagination.next')">
                    <a href="#"  class="page-link bg-transparent bg-hover-light-primary"><i class="previous"></i></a>
                </li>

            @endif
        </ul>
    </nav>
@endif
