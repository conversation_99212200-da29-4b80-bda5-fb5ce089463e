@extends('baselayout.public.meeting.meeting-base-layout-minify')
@section('meta')
    <meta property="og:image" content="{{asset('assets/logo_meet_2020q4_color_2x_web_96dp.png')}}">
    <meta property="og:image:secure_url" content="{{asset('assets/logo_meet_2020q4_color_2x_web_96dp.png')}}">
@endsection

@push('style')
    <!-- Page loading styles -->
    <style>
        .row-reverse {
            flex-direction: row-reverse;
        }
        /*! CSS Used from: https://iroom.live/index-assets/css/boxicons.min.css ; media=screen */
        @media screen {
            .bx {
                font-family: boxicons !important;
                font-weight: 400;
                font-style: normal;
                font-variant: normal;
                line-height: 1;
                text-rendering: auto;
                display: inline-block;
                text-transform: none;
                speak: none;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

            .bx-chevron-up:before {
                content: "\ea57";
            }

            .bx-lock:before {
                content: "\eb49";
            }

            .bx-clipboard:before {
                content: "\ea5e";
            }
        }

        /*! CSS Used from: https://iroom.live/index-assets/css/swiper-bundle.min.css ; media=screen */
        @media screen {
            :root {
                --swiper-theme-color: #007aff;
            }

            :root {
                --swiper-navigation-size: 44px;
            }
        }

        /*! CSS Used from: https://iroom.live/index-assets/css/theme.min.css ; media=screen */
        @media screen {
            :root {
                --si-blue: #0d6efd;
                --si-indigo: #6610f2;
                --si-purple: #6f42c1;
                --si-pink: #d63384;
                --si-red: #dc3545;
                --si-orange: #fd7e14;
                --si-yellow: #ffc107;
                --si-green: #198754;
                --si-teal: #20c997;
                --si-cyan: #0dcaf0;
                --si-black: #000;
                --si-white: #fff;
                --si-gray: #9397ad;
                --si-gray-dark: #33354d;
                --si-gray-100: #f3f6ff;
                --si-gray-200: #eff2fc;
                --si-gray-300: #e2e5f1;
                --si-gray-400: #d4d7e5;
                --si-gray-500: #b4b7c9;
                --si-gray-600: #9397ad;
                --si-gray-700: #565973;
                --si-gray-800: #33354d;
                --si-gray-900: #0b0f19;
                --si-primary: #3b49dc;
                --si-secondary: #eff2fc;
                --si-success: #22c55e;
                --si-info: #4c82f7;
                --si-warning: #ffba08;
                --si-danger: #ef4444;
                --si-light: #fff;
                --si-dark: #0b0f19;
                --si-primary-rgb: 59, 73, 220;
                --si-secondary-rgb: 239, 242, 252;
                --si-success-rgb: 34, 197, 94;
                --si-info-rgb: 76, 130, 247;
                --si-warning-rgb: 255, 186, 8;
                --si-danger-rgb: 239, 68, 68;
                --si-light-rgb: 255, 255, 255;
                --si-dark-rgb: 11, 15, 25;
                --si-white-rgb: 255, 255, 255;
                --si-black-rgb: 0, 0, 0;
                --si-body-color-rgb: 86, 89, 115;
                --si-body-bg-rgb: 255, 255, 255;
                --si-font-sans-serif: "yekanbakh", sans-serif;
                --si-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
                --si-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
                --si-body-font-family: var(--si-font-sans-serif);
                --si-body-font-size: 1rem;
                --si-body-font-weight: 400;
                --si-body-line-height: 1.6;
                --si-body-color: #565973;
                --si-body-bg: #fff;
                --si-border-width: 1px;
                --si-border-style: solid;
                --si-border-color: #e2e5f1;
                --si-border-color-translucent: rgba(0, 0, 0, 0.175);
                --si-border-radius: 0.375rem;
                --si-border-radius-sm: 0.25rem;
                --si-border-radius-lg: 0.5rem;
                --si-border-radius-xl: 1rem;
                --si-border-radius-2xl: 2rem;
                --si-border-radius-pill: 50rem;
                --si-link-color: #3b49dc;
                --si-link-hover-color: #3e41ee;
                --si-code-color: #e3116c;
                --si-highlight-bg: #fff3cd;
            }

            *, *::before, *::after {
                box-sizing: border-box;
            }

            body {
                margin: 0;
                font-family: var(--si-body-font-family);
                font-size: var(--si-body-font-size);
                font-weight: var(--si-body-font-weight);
                line-height: var(--si-body-line-height);
                color: var(--si-body-color);
                text-align: var(--si-body-text-align);
                background-color: var(--si-body-bg);
                -webkit-text-size-adjust: 100%;
                -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
            }

            h2, .h2 {
                margin-top: 0;
                margin-bottom: 1rem;
                font-weight: 800;
                line-height: 1.3;
                color: var(--si-gray-900);
            }

            h2, .h2 {
                font-size: calc(1.325rem + 0.9vw);
            }

            @media (min-width: 1200px) {
                h2, .h2 {
                    font-size: 2rem;
                }
            }
            p {
                margin-top: 0;
                margin-bottom: 1.25rem;
            }

            a {
                color: var(--si-link-color);
                text-decoration: underline;
            }

            a:hover {
                color: var(--si-link-hover-color);
                text-decoration: none;
            }

            img, svg {
                vertical-align: middle;
            }

            input {
                margin: 0;
                font-family: inherit;
                font-size: inherit;
                line-height: inherit;
            }

            iframe {
                border: 0;
            }

            .lead {
                font-size: calc(1.275rem + 0.3vw);
                font-weight: 400;
            }

            @media (min-width: 1200px) {
                .lead {
                    font-size: 1.5rem;
                }
            }
            .container {
                --si-gutter-x: 1.5rem;
                --si-gutter-y: 0;
                width: 100%;
                padding-right: calc(var(--si-gutter-x) * .5);
                padding-left: calc(var(--si-gutter-x) * .5);
                margin-right: auto;
                margin-left: auto;
            }

            @media (min-width: 500px) {
                .container {
                    max-width: 97%;
                }

            }
            @media (min-width: 768px) {
                .container {
                    max-width: 95%;
                }
            }
            @media (min-width: 992px) {
                .container {
                    max-width: 95%;
                }
            }
            @media (min-width: 1200px) {
                .container {
                    max-width: 95%;
                }
            }
            @media (min-width: 1400px) {
                .container {
                    max-width: 1320px;
                }
            }
            .row {
                --si-gutter-x: 1.5rem;
                --si-gutter-y: 0;
                display: flex;
                flex-wrap: wrap;
                margin-top: calc(-1 * var(--si-gutter-y));
                margin-right: calc(-0.5 * var(--si-gutter-x));
                margin-left: calc(-0.5 * var(--si-gutter-x));
            }

            .row > * {
                flex-shrink: 0;
                width: 100%;
                max-width: 100%;
                padding-right: calc(var(--si-gutter-x) * .5);
                padding-left: calc(var(--si-gutter-x) * .5);
                margin-top: var(--si-gutter-y);
            }

            @media (min-width: 992px) {
                .col-lg-5 {
                    flex: 0 0 auto;
                    width: 41.66666667%;
                }

                .col-lg-7 {
                    flex: 0 0 auto;
                    width: 58.33333333%;
                }
            }
            @media (min-width: 1200px) {
                .col-xl-6 {
                    flex: 0 0 auto;
                    width: 50%;
                }

                .offset-xl-1 {
                    margin-left: 8.33333333%;
                }
            }
            .form-control {
                display: block;
                width: 100%;
                padding: .625rem 1rem;
                font-size: 0.875rem;
                font-weight: 400;
                line-height: 1.6;
                color: #565973;
                background-color: #fff;
                background-clip: padding-box;
                border: 1px solid #d4d7e5;
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                border-radius: .375rem;
                box-shadow: inset 0 0 0 rgba(0, 0, 0, 0);
                transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
            }

            @media (prefers-reduced-motion: reduce) {
                .form-control {
                    transition: none;
                }
            }
            .form-control:focus {
                color: #565973;
                background-color: #fff;
                border-color: rgba(99, 102, 241, .35);
                outline: 0;
                box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 .5rem 1.125rem -0.5rem rgba(99, 102, 241, .2);
            }

            .form-control::placeholder {
                color: #b4b7c9;
                opacity: 1;
            }

            .form-control:disabled {
                background-color: #f3f6ff;
                opacity: 1;
            }

            .input-group {
                position: relative;
                display: flex;
                flex-wrap: wrap;
                align-items: stretch;
                width: 100%;
            }

            .input-group > .form-control {
                position: relative;
                flex: 1 1 auto;
                width: 1%;
                min-width: 0;
            }

            .input-group > .form-control:focus {
                z-index: 5;
            }

            .input-group-text {
                display: flex;
                align-items: center;
                padding: .625rem 1rem;
                font-size: 0.875rem;
                font-weight: 400;
                line-height: 1.6;
                color: #565973;
                text-align: center;
                white-space: nowrap;
                background-color: #fff;
                border: 1px solid #d4d7e5;
                border-radius: .375rem;
            }

            .input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }

            .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
                margin-left: -1px;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }

            .btn {
                --si-btn-padding-x: 1.75rem;
                --si-btn-padding-y: 0.625rem;
                --si-btn-font-size: 0.875rem;
                --si-btn-font-weight: 600;
                --si-btn-line-height: 1.6;
                --si-btn-color: #565973;
                --si-btn-bg: transparent;
                --si-btn-border-width: 1px;
                --si-btn-border-color: transparent;
                --si-btn-border-radius: 0.375rem;
                --si-btn-hover-border-color: transparent;
                --si-btn-box-shadow: unset;
                --si-btn-disabled-opacity: 0.65;
                --si-btn-focus-box-shadow: 0 0 0 0 rgba(var(--si-btn-focus-shadow-rgb), .5);
                display: inline-block;
                padding: var(--si-btn-padding-y) var(--si-btn-padding-x);
                font-family: var(--si-btn-font-family);
                font-size: var(--si-btn-font-size);
                font-weight: var(--si-btn-font-weight);
                line-height: var(--si-btn-line-height);
                color: var(--si-btn-color);
                text-align: center;
                text-decoration: none;
                white-space: nowrap;
                vertical-align: middle;
                cursor: pointer;
                -webkit-user-select: none;
                -moz-user-select: none;
                user-select: none;
                border: var(--si-btn-border-width) solid var(--si-btn-border-color);
                border-radius: var(--si-btn-border-radius);
                background-color: var(--si-btn-bg);
                box-shadow: var(--si-btn-box-shadow);
                transition: color .2s ease-in-out, background-color .2s ease-in-out, border-color .2s ease-in-out, box-shadow .2s ease-in-out;
            }

            @media (prefers-reduced-motion: reduce) {
                .btn {
                    transition: none;
                }
            }
            .btn:hover {
                color: var(--si-btn-hover-color);
                background-color: var(--si-btn-hover-bg);
                border-color: var(--si-btn-hover-border-color);
            }

            .btn:first-child:active {
                color: var(--si-btn-active-color);
                background-color: var(--si-btn-active-bg);
                border-color: var(--si-btn-active-border-color);
                box-shadow: var(--si-btn-active-shadow);
            }

            .btn:disabled {
                color: var(--si-btn-disabled-color);
                pointer-events: none;
                background-color: var(--si-btn-disabled-bg);
                border-color: var(--si-btn-disabled-border-color);
                opacity: var(--si-btn-disabled-opacity);
                box-shadow: none;
            }

            .btn-primary {
                --si-btn-color: #000;
                --si-btn-bg: #3b49dc;
                --si-btn-border-color: #3b49dc;
                --si-btn-hover-color: #000;
                --si-btn-hover-bg: #7a7df3;
                --si-btn-hover-border-color: #7375f2;
                --si-btn-focus-shadow-rgb: 84, 87, 205;
                --si-btn-active-color: #000;
                --si-btn-active-bg: #8285f4;
                --si-btn-active-border-color: #7375f2;
                --si-btn-active-shadow: unset;
                --si-btn-disabled-color: #000;
                --si-btn-disabled-bg: #3b49dc;
                --si-btn-disabled-border-color: #3b49dc;
            }

            .btn-lg {
                --si-btn-padding-y: 0.785rem;
                --si-btn-padding-x: 2rem;
                --si-btn-font-size: 1rem;
                --si-btn-border-radius: 0.5rem;
            }

            .opacity-60 {
                opacity: .6 !important;
            }

            .overflow-hidden {
                overflow: hidden !important;
            }

            .d-table {
                display: table !important;
            }

            .d-flex {
                display: flex !important;
            }

            .d-none {
                display: none !important;
            }

            .shadow-primary {
                box-shadow: 0 .5rem 1.125rem -0.5rem rgba(99, 102, 241, .9) !important;
            }

            .position-relative {
                position: relative !important;
            }

            .position-absolute {
                position: absolute !important;
            }

            .top-0 {
                top: 0 !important;
            }

            .top-50 {
                top: 50% !important;
            }

            .bottom-0 {
                bottom: 0 !important;
            }

            .start-0 {
                left: 0 !important;
            }

            .end-0 {
                right: 0 !important;
            }

            .translate-middle {
                transform: translate(-50%, -50%) !important;
            }

            .translate-middle-y {
                transform: translateY(-50%) !important;
            }

            .w-100 {
                width: 100% !important;
            }

            .h-100 {
                height: 100% !important;
            }

            .flex-column {
                flex-direction: column !important;
            }

            .flex-shrink-0 {
                flex-shrink: 0 !important;
            }

            .justify-content-center {
                justify-content: center !important;
            }

            .justify-content-between {
                justify-content: space-between !important;
            }

            .align-items-start {
                align-items: flex-start !important;
            }

            .align-items-center {
                align-items: center !important;
            }

            .mx-auto {
                margin-right: auto !important;
                margin-left: auto !important;
            }

            .mt-5 {
                margin-top: 3rem !important;
            }

            .me-2 {
                margin-right: .5rem !important;
            }

            .mb-2 {
                margin-bottom: .5rem !important;
            }

            .mb-3 {
                margin-bottom: 1rem !important;
            }

            .mb-4 {
                margin-bottom: 1.5rem !important;
            }

            .me-n4 {
                margin-right: -1.5rem !important;
            }

            .mb-n2 {
                margin-bottom: -0.5rem !important;
            }

            .p-1 {
                padding: .25rem !important;
            }

            .p-4 {
                padding: 1.5rem !important;
            }

            .px-4 {
                padding-right: 1.5rem !important;
                padding-left: 1.5rem !important;
            }

            .py-5 {
                padding-top: 3rem !important;
                padding-bottom: 3rem !important;
            }

            .pb-3 {
                padding-bottom: 1rem !important;
            }

            .pb-5 {
                padding-bottom: 3rem !important;
            }

            .fs-base {
                font-size: 1rem !important;
            }

            .fs-sm {
                font-size: 0.875rem !important;
            }

            .fw-semibold {
                font-weight: 700 !important;
            }

            .text-center {
                text-align: center !important;
            }

            .text-uppercase {
                text-transform: uppercase !important;
            }

            .text-primary {
                color: #3b49dc !important;
            }

            .text-light {
                color: #fff !important;
            }

            .text-muted {
                color: var(--si-gray-600) !important;
            }

            .bg-light {
                --si-bg-opacity: 1;
                background-color: rgba(var(--si-light-rgb), var(--si-bg-opacity)) !important;
            }

            .bg-white {
                --si-bg-opacity: 1;
                background-color: rgba(var(--si-white-rgb), var(--si-bg-opacity)) !important;
            }

            .rounded-3 {
                border-radius: var(--si-border-radius-lg) !important;
            }

            .rounded-circle {
                border-radius: 50% !important;
            }

            .bg-size-cover {
                background-size: cover !important;
            }

            .bg-position-center-end {
                background-position: center right !important;
            }

            .bg-repeat-0 {
                background-repeat: no-repeat !important;
            }

            .bg-gradient-primary {
                background: linear-gradient(90deg, #3b49dc 0%, #8b5cf6 50%, #d946ef 100%) !important;
            }

            .zindex-5 {
                z-index: 5 !important;
            }

            @media (min-width: 500px) {
                .d-sm-flex {
                    display: flex !important;
                }

                .flex-sm-row {
                    flex-direction: row !important;
                }

                .mx-sm-0 {
                    margin-right: 0 !important;
                    margin-left: 0 !important;
                }

                .ms-sm-3 {
                    margin-left: 1rem !important;
                }

                .p-sm-5 {
                    padding: 3rem !important;
                }
            }
            @media (min-width: 768px) {
                .mb-md-4 {
                    margin-bottom: 1.5rem !important;
                }

                .px-md-4 {
                    padding-right: 1.5rem !important;
                    padding-left: 1.5rem !important;
                }

                .pb-md-3 {
                    padding-bottom: 1rem !important;
                }
            }
            @media (min-width: 992px) {
                .d-lg-block {
                    display: block !important;
                }

                .mt-lg-0 {
                    margin-top: 0 !important;
                }

                .mb-lg-0 {
                    margin-bottom: 0 !important;
                }

                .mb-lg-5 {
                    margin-bottom: 3rem !important;
                }
            }
            @media (min-width: 1200px) {
                .me-xl-4 {
                    margin-right: 1.5rem !important;
                }

                .mb-xl-n5 {
                    margin-bottom: -3rem !important;
                }

                .ms-xl-n4 {
                    margin-left: -1.5rem !important;
                }

                .pt-xl-2 {
                    padding-top: .5rem !important;
                }
            }
            html * {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

            html, body {
                height: 100%;
            }

            @keyframes spinner-border {
                to {
                    transform: rotate(360deg) /* rtl:ignore */
                }
            }
            body {
                display: flex;
                flex-direction: column;
            }

            .page-wrapper {
                flex: 1 0 auto;
            }

            :root {
                --si-user-selection-color: rgba(var(--si-primary-rgb), 0.22);
                --si-heading-link-color: #33354d;
                --si-heading-link-hover-color: #3b49dc;
            }

            a {
                transition: color .2s ease-in-out;
            }

            a:focus {
                outline: none;
            }

            img {
                max-width: 100%;
                height: auto;
                vertical-align: middle;
            }

            svg {
                max-width: 100%;
            }

            iframe {
                width: 100%;
            }

            ::selection {
                background: var(--si-user-selection-color);
            }

            .form-control:disabled {
                cursor: not-allowed;
                box-shadow: none !important;
            }

            .form-control:disabled {
                box-shadow: none !important;
            }

            .form-control:disabled::placeholder {
                color: #9397ad;
            }

            .btn {
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }

            .btn:not([class^=btn-outline-]):not([class*=" btn-outline-"]):not(.btn-secondary):not(.btn-light):not(.btn-link) {
                --si-btn-color: #fff;
            }

            .btn:hover, .btn:disabled {
                box-shadow: none !important;
            }

            .btn-primary {
                --si-btn-hover-color: #fff;
                --si-btn-active-color: #fff;
                --si-btn-hover-bg: #4044ee;
                --si-btn-active-bg: #4044ee;
                --si-btn-hover-border-color: #4044ee;
                --si-btn-active-border-color: #4044ee;
                --si-btn-disabled-color: #fff;
            }

            .btn-scroll-top {
                --si-btn-scroll-top-size: 2.75rem;
                --si-btn-scroll-top-border-radius: 50%;
                --si-btn-scroll-top-color: #fff;
                --si-btn-scroll-top-hover-color: #fff;
                --si-btn-scroll-top-bg: rgba(11, 15, 25, 0.2);
                --si-btn-scroll-top-hover-bg: rgba(11, 15, 25, 0.4);
                --si-btn-scroll-top-icon-size: 1.5rem;
                position: fixed;
                display: flex;
                align-items: center;
                justify-content: center;
                right: 1.25rem;
                bottom: -4.125rem;
                width: var(--si-btn-scroll-top-size);
                height: var(--si-btn-scroll-top-size);
                transition: bottom 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55), opacity .3s, background-color .25s ease-in-out;
                border-radius: var(--si-btn-scroll-top-border-radius);
                background-color: var(--si-btn-scroll-top-bg);
                color: var(--si-btn-scroll-top-color);
                text-decoration: none;
                opacity: 0;
                z-index: 1030;
            }

            .btn-scroll-top > .btn-scroll-top-icon {
                font-size: var(--si-btn-scroll-top-icon-size);
                font-weight: bold;
            }

            .btn-scroll-top .btn-scroll-top-tooltip {
                position: absolute;
                top: 50%;
                right: 100%;
                transform: translateY(-50%);
                transition: opacity .25s ease-in-out, visibility .25s ease-in-out;
                visibility: hidden;
                opacity: 0;
            }

            .btn-scroll-top:hover {
                background-color: var(--si-btn-scroll-top-hover-bg);
                color: var(--si-btn-scroll-top-hover-color);
            }

            .btn-scroll-top:hover .btn-scroll-top-tooltip {
                visibility: visible;
                opacity: 1;
            }

            @media (max-width: 499.98px) {
                .btn-scroll-top {
                    width: calc(var(--si-btn-scroll-top-size) * .8);
                    height: calc(var(--si-btn-scroll-top-size) * .8);
                    right: 1rem;
                }
            }
        }

        /*!  media=screen */
        @media screen {
            body {
                font-family: iransansx, tahoma;
            }

            html {
                font-size: 14px;
            }

            .rtl {
                direction: rtl;
            }

            .ltr {
                direction: ltr;
            }

            .min-w-300px {
                min-width: 300px;
            }

            @media screen and (max-width: 429px) {
                .only-desktop {
                    display: none !important;
                }
            }
        }

        /*! CSS Used fontfaces */
        @font-face {
            font-family: boxicons;
            font-weight: 400;
            font-style: normal;
            src: url(https://iroom.live/index-assets/eot/boxicons.eot);
            src: url(https://iroom.live/index-assets/eot/boxicons.eot) format('embedded-opentype'), url(https://iroom.live/index-assets/woff2/boxicons.woff2) format('woff2'), url(https://iroom.live/index-assets/woff/boxicons.woff) format('woff'), url(https://iroom.live/index-assets/ttf/boxicons.ttf) format('truetype'), url(https://iroom.live/index-assets/svg/boxiconsd41d.svg?#boxicons) format('svg');
        }

        @font-face {
            font-family: iransansx;
            src: url('https://iroom.live/assets/css/fonts/IRANSansX-Regular.woff') format('woff');
        }

        @font-face {
            font-family: iransansx;
            src: url('https://iroom.live/assets/css/fonts/IRANSansX-Bold.woff') format('woff');
            font-weight: 700;
        }

        /*loading*/

        /*! CSS Used from: https://iroom.live/index-assets/css/theme.min.css ; media=screen */
        @media screen {

            svg {
                vertical-align: middle;
            }

            .badge {
                --si-badge-padding-x: 0.6em;
                --si-badge-padding-y: 0.35em;
                --si-badge-font-size: 0.8125em;
                --si-badge-font-weight: 600;
                --si-badge-color: #fff;
                --si-badge-border-radius: 0.25rem;
                display: inline-block;
                padding: var(--si-badge-padding-y) var(--si-badge-padding-x);
                font-size: var(--si-badge-font-size);
                font-weight: var(--si-badge-font-weight);
                line-height: 1;
                color: var(--si-badge-color);
                text-align: center;
                white-space: nowrap;
                vertical-align: baseline;
                border-radius: var(--si-badge-border-radius);
            }

            .badge:empty {
                display: none;
            }

            .spinner-border {
                display: inline-block;
                width: var(--si-spinner-width);
                height: var(--si-spinner-height);
                vertical-align: var(--si-spinner-vertical-align);
                border-radius: 50%;
                animation: var(--si-spinner-animation-speed) linear infinite var(--si-spinner-animation-name);
            }

            .spinner-border {
                --si-spinner-width: 2rem;
                --si-spinner-height: 2rem;
                --si-spinner-vertical-align: -0.125em;
                --si-spinner-border-width: 0.15em;
                --si-spinner-animation-speed: 0.75s;
                --si-spinner-animation-name: spinner-border;
                border: var(--si-spinner-border-width) solid currentcolor;
                border-right-color: rgba(0, 0, 0, 0);
            }

            .spinner-border-sm {
                --si-spinner-width: 1rem;
                --si-spinner-height: 1rem;
                --si-spinner-border-width: 0.1em;
            }

            @media (prefers-reduced-motion: reduce) {
                .spinner-border {
                    --si-spinner-animation-speed: 1.5s;
                }
            }
            .align-middle {
                vertical-align: middle !important;
            }

            .d-flex {
                display: flex;
            }

            .flex-column {
                flex-direction: column !important;
            }

            .justify-content-between {
                justify-content: space-between !important;
            }

            .align-items-center {
                align-items: center !important;
            }

            .my-3 {
                margin-top: 1rem !important;
                margin-bottom: 1rem !important;
            }

            .text-center {
                text-align: center !important;
            }

            .text-nowrap {
                white-space: nowrap !important;
            }

            .bg-primary {
                --si-bg-opacity: 1;
                background-color: rgba(var(--si-primary-rgb), var(--si-bg-opacity)) !important;
            }

            .rounded {
                border-radius: var(--si-border-radius) !important;
            }

            @media (min-width: 500px) {
                .flex-sm-row {
                    flex-direction: row !important;
                }

                .my-sm-0 {
                    margin-top: 0 !important;
                    margin-bottom: 0 !important;
                }
            }
            html * {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

            svg {
                max-width: 100%;
            }

            ::selection {
                background: var(--si-user-selection-color);
            }

            h4, .fs-6 {
                line-height: 1.4;
            }

            .list-group {
                --si-list-group-color: #565973;
                --si-list-group-bg: transparent;
                --si-list-group-border-color: #e2e5f1;
                --si-list-group-border-width: 1px;
                --si-list-group-border-radius: 0.5rem;
                --si-list-group-item-padding-x: 1rem;
                --si-list-group-item-padding-y: 0.75rem;
                --si-list-group-action-color: #33354d;
                --si-list-group-action-hover-color: #3b49dc;
                --si-list-group-action-hover-bg: rgba(99, 102, 241, 0.12);
                --si-list-group-action-active-color: #fff;
                --si-list-group-action-active-bg: #3b49dc;
                --si-list-group-disabled-color: #9397ad;
                --si-list-group-disabled-bg: transparent;
                --si-list-group-active-color: #fff;
                --si-list-group-active-bg: #3b49dc;
                --si-list-group-active-border-color: #3b49dc;
                --si-list-group-active-box-shadow: 0 0.5rem 1.125rem -0.5rem rgba(99, 102, 241, 0.9);
                display: flex;
                flex-direction: column;
                padding-left: 0;
                margin-bottom: 0;
                border-radius: var(--si-list-group-border-radius);
            }

            .list-group-item {
                position: relative;
                display: block;
                padding: var(--si-list-group-item-padding-y) var(--si-list-group-item-padding-x);
                color: var(--si-list-group-color);
                text-decoration: none;
                background-color: var(--si-list-group-bg);
                border: var(--si-list-group-border-width) solid var(--si-list-group-border-color);
            }

            .list-group-item:last-child {
                border-bottom-right-radius: inherit;
                border-bottom-left-radius: inherit;
            }

            .list-group-item:disabled {
                color: var(--si-list-group-disabled-color);
                pointer-events: none;
                background-color: var(--si-list-group-disabled-bg);
            }

            .list-group-item + .list-group-item {
                border-top-width: 0;
            }
        }

        /*! CSS Used from: https://iroom.live/index-assets/css/theme-chanaged.css?ver=1.2 ; media=screen */
        @media screen {
            .pl-0 {
                padding-left: 0px;
            }

            .pr-0 {
                padding-right: 0px;
            }

            .cursor-pointer {
                cursor: pointer;
            }
        }

        /*! CSS Used from: Embedded */
        [wire\:loading] {
            display: none;
        }

        /*end loading*/
        .page-loading {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            -webkit-transition: all .4s .2s ease-in-out;
            transition: all .4s .2s ease-in-out;
            background-color: #fff;
            opacity: 0;
            visibility: hidden;
            z-index: 9999;
        }

        .dark-mode .page-loading {
            background-color: #0b0f19;
        }

        .page-loading.active {
            opacity: 1;
            visibility: visible;
        }

        .page-loading-inner {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            text-align: center;
            -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
            -webkit-transition: opacity .2s ease-in-out;
            transition: opacity .2s ease-in-out;
            opacity: 0;
        }

        .page-loading.active > .page-loading-inner {
            opacity: 1;
        }

        .page-loading-inner > span {
            display: block;
            font-size: 1rem;
            font-weight: normal;
            color: #9397ad;
        }

        .dark-mode .page-loading-inner > span {
            color: #fff;
            opacity: .6;
        }

        .page-spinner {
            display: inline-block;
            width: 2.75rem;
            height: 2.75rem;
            margin-bottom: .75rem;
            vertical-align: text-bottom;
            border: .15em solid #b4b7c9;
            border-right-color: transparent;
            border-radius: 50%;
            -webkit-animation: spinner .75s linear infinite;
            animation: spinner .75s linear infinite;
        }

        .dark-mode .page-spinner {
            border-color: rgba(255, 255, 255, .4);
            border-right-color: transparent;
        }

        @-webkit-keyframes spinner {
            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }

        @keyframes spinner {
            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }
    </style>
@endpush

@section('content')
    <body>
    <main class="page-wrapper">
        <section class="container pb-5 mb-2 mb-md-4 mb-lg-5 mt-5 mt-lg-0"
                 style="min-height: 95dvh;display: flex;align-items: center;justify-content: center;">
            <div class="row pt-xl-2 pb-md-3 row-reverse">
                <div class="col-lg-7 mb-4 mb-lg-0 rtl d-flex flex-column align-center justify-content-center">
                    <h2 class="h2 text-center fw-semibold" style="margin: 0px">{{$detail->title}}</h2>
                    <h4 class="h4 text-center fw-semibold mb-4">{{$detail->summery}}</h4>
                    <p class="text-center">
                        جهت ورود به این رویداد کافیست روی لینک زیر کلیک کنید
                    </p>
                    <div class="d-flex flex-column flex-sm-row justify-content-center justify-content-center">
                        <a onclick="openGoogleMeet('{{$meetingCode}}')"
                           class="btn btn-primary shadow-primary btn-lg ms-sm-3 me-xl-4 mb-3 min-w-300px">ورود</a>
                        {{--                    <a href="#" class="btn btn-outline-primary btn-lg mb-3">--}}
                        {{--                        <i class="bx bx-calendar-check fs-xl ms-2 me-n1"></i>--}}
                        {{--                        اضافه کردن به تقویم من--}}
                        {{--                    </a>--}}
                    </div>
                    <div class="d-flex flex-column align-center  justify-content-center justify-content-center"
                         style="align-items: center;text-align: center;">
                        و یا از کد زیر برای ورود در نرم افزار گوگل میت استفاده نمایید: <br/>
                        <div class="input-group mb-4 ltr" style="max-width: 200px; margin-top:15px;">
                        <span class="input-group-text" onclick="copyText()">
                          <i class="bx bx-clipboard fs-base"></i>
                        </span>
                            <input class="form-control text-center" readonly id="meetingCode" type="text"
                                   value="{{ $meetingCode  }}">
                        </div>
                    </div>


                    @if($detail->download_access)

                        <livewire:public.download-meeting-file :meeting-id="$detail->meeting->meeting_id"
                                                               :meeting-key="meetingKey($detail->meeting->id)"
                                                               :chat-download-access="$detail->chat_download_access"
                        />
                    @endif
                </div>
                <div
                    class="col-xl-5 col-lg-5 position-relative d-flex align-items-center justify-content-center only-desktop">
                    <!-- Ticket card -->
                    <div class="position-relative">
                        <div
                            class="position-relative overflow-hidden bg-gradient-primary rounded-3 zindex-5 py-5 px-4 p-sm-5">
                        <span
                            class="position-absolute top-50 start-0 translate-middle bg-light rounded-circle p-4"></span>
                            <span
                                class="position-absolute top-0 start-0 w-100 h-100 bg-repeat-0 bg-position-center-end bg-size-cover"
                                style="background-image: url({{asset('index-assets/')}}/png/price-card-pattern.png);"></span>
                            <div class="px-md-4 position-relative zindex-5">
                                <div
                                    class="d-sm-flex flex-column align-items-start justify-content-between align-items-center rtl">
                                    <div class="text-center rtl">
                                        <div class="lead fw-semibold text-light text-center text-uppercase mb-2">
                                            جهت
                                            دسترسی
                                            به لینک ورود در موبایل، این کد را اسکن کنید
                                        </div>
                                    </div>
                                    <div class="d-table bg-white rounded-3 p-1 flex-shrink-0 mx-auto mx-sm-0">
                                        <img
                                            src="https://quickchart.io/qr?text={{urlencode($detail->meeting->meeting_link)}}&dark=4044ee&margin=3&size=200"
                                            width="200" alt="QR Code">
                                    </div>
                                </div>
                            </div>
                            <span
                                class="position-absolute top-50 end-0 translate-middle-y bg-light rounded-circle p-4 me-n4"></span>
                        </div>
                        <span class="position-absolute bg-gradient-primary opacity-60 bottom-0 mb-n2 d-dark-mode-none"
                              style="left: 1.5rem; width: calc(100% - 3rem); height: 5rem; filter: blur(.625rem);"></span>
                    </div>

                    <!-- Arrow -->
                    <div
                        class="position-absolute bottom-0 text-primary d-none d-lg-block ms-xl-n4 mb-lg-5 mb-xl-n5 pb-3">
                        <div class="ms-xl-n4">
                            <svg width="95" height="100" viewBox="0 0 95 100" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M78.8361 25.0939C91.1514 40.6379 81.8802 63.3086 61.7212 64.3539C60.7119 64.447 59.5832 64.3477 58.6105 64.2848C58.7669 50.9978 52.4534 36.5276 41.6324 32.847C31.8669 29.5776 26.5235 39.0204 30.5663 47.0383C35.4083 56.5589 43.9198 64.4699 54.2628 67.3808C53.4517 75.7446 49.4008 83.1867 40.4191 85.693C25.2817 89.8859 9.48935 75.832 7.25928 61.4938C7.12064 59.981 4.79 60.0942 4.92864 61.607C5.83171 80.8987 28.9103 96.1621 46.7792 87.3441C53.6867 83.8595 57.3887 76.5003 58.3558 68.173C69.2212 69.5612 79.5859 63.2659 85.1681 54.2081C91.5844 43.7002 88.5763 31.9764 81.257 23.1926C80.1091 21.7727 77.8441 23.7109 78.8361 25.0939ZM39.1221 52.6568C36.2753 49.3596 33.1435 45.1733 32.7276 40.635C32.275 36.2527 38.2211 36.1619 40.7539 36.5897C43.9108 37.163 46.2067 40.0025 47.9151 42.5401C51.7632 47.8805 54.3289 55.8821 54.5172 63.4926C48.5423 61.6026 43.3094 57.2542 39.1221 52.6568Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M75.1096 15.0312C74.0848 19.3973 73.3354 23.9923 73.4392 28.4577C73.5047 30.2821 76.0279 30.0497 76.1186 28.2613C76.2997 24.6849 77.2976 21.1349 78.2588 17.7408C80.2501 18.3708 82.3978 19.0372 84.3528 19.8231C85.8397 20.4997 87.9238 22.1382 89.7035 21.5672C90.5937 21.2818 90.7767 20.5022 90.6474 19.6495C90.3065 17.596 87.0302 16.8302 85.3872 16.1172C82.6885 14.993 80.073 14.2174 77.2645 13.561C76.3289 13.3423 75.3292 14.0956 75.1096 15.0312Z"
                                    fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    @endsection

    @section('footer')
        <script>
            function copyText() {
                // Get the text field
                var copyText = document.getElementById("meetingCode");

                // Select the text field
                copyText.select();
                copyText.setSelectionRange(0, 99999); // For mobile devices

                // Copy the text inside the text field
                navigator.clipboard.writeText(copyText.value);

                // Alert the copied text
                alert("با موفقیت کپی شد");
            }

            function openGoogleMeet(meetingCode) {

// Check if the user is on a mobile device
                var isMobile = /Android|webOS|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

                if (isMobile) {
                    // Open the Google Meet app with the meeting code
                    window.location.href = "intent://meet.google.com/" + meetingCode + "#Intent;scheme=https;package=com.google.android.apps.tachyon;end";
                } else {
                    // Open the Google Meet website with the meeting code
                    window.open("https://meet.google.com/" + meetingCode, "_blank");
                }

            }
        </script>
@endsection
