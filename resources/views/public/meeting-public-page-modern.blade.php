@extends('baselayout.public.meeting.meeting-base-layout-minify')
@section('meta')
    <meta property="og:image" content="{{asset('assets/logo_meet_2020q4_color_2x_web_96dp.png')}}">
    <meta property="og:image:secure_url" content="{{asset('assets/logo_meet_2020q4_color_2x_web_96dp.png')}}">
@endsection

@push('style')
    <style>
        :root {
            --dark-background: #05060f;
            --body-normal: rgba(200, 212, 234, .78);
            --body-loud: #c7d3ea;
            --body-muted: #c7d3eaa3;
            --gradient-background-6: linear-gradient(0deg, rgba(216, 236, 248, .06), rgba(152, 192, 239, .06));
            --gradient-loud-100: linear-gradient(0deg, #d8ecf8, #98c0ef);
            --gradient-subdued-12: linear-gradient(0deg, rgba(216, 236, 248, .12), rgba(152, 192, 239, .12));
            --blue-loud: #232425;
            --blue-6: rgba(186, 214, 247, .06);
            --blue-12: rgba(186, 215, 247, .12);
            --blue-90: rgba(186, 214, 247, .9);
            --blue-24: rgba(186, 214, 247, .24);
        }

        @font-face {
            font-family: iransansx;
            src: url('https://iroom.live/assets/css/fonts/IRANSansX-Regular.woff') format('woff');
        }

        @font-face {
            font-family: iransansx;
            src: url('https://iroom.live/assets/css/fonts/IRANSansX-Bold.woff') format('woff');
            font-weight: 700;
        }

        .theme-modern .btn.btn-light {
            background: #151d2a;
            color: #d1e4fa;
            border: 1px solid rgba(186, 215, 247, .12) !important;!i;!;
            iransansx, tahoma, font-family: sans-serif;
            padding: 8px 16px !important;!i;!;!n;!;
            border-radius: 26px;
        }

        .theme-modern .btn.btn-light span {
            font-family: 'iransansx';
            margin: 0px;
        }

        .flex-column.passbox {
            flex-direction: column;
        }

        .col-form-label {
            font-size: 14px;
        }
        button.btn {
            cursor: pointer;
        }


        input {
            all: unset;
            height: 100%;
            min-height: 40px;
            line-height: 32px;
            font-size: 14px;
            padding-inline: 10px;
            font-weight: 400;
            font-family: inherit;
            border-radius: inherit;
            width: 100%;
            border: 1px solid rgba(186, 215, 247, .12);
            background: rgba(199, 211, 234, .06);
            box-sizing: border-box;
            border-radius: 28px;
        }

        [data-kt-password-meter-control] {
            display: none;
        }

        .__variable_8319ca {
            --font-aeonik-pro: "__aeonikPro_8319ca", "__aeonikPro_Fallback_8319ca";
        }

        .__variable_226f3d {
            --font-dot-digital: "__dotDigital_226f3d", "__dotDigital_Fallback_226f3d";
        }

        body {
            color: #fff;
            background-color: var(--dark-background);
            width: 100vw;
            min-height: 100vh;
            direction: rtl;
            font-family: iransansx, tahoma, sans-serif;
        }

        ::-webkit-scrollbar {
            width: 0;
        }

        :focus {
            outline: none;
        }

        * {
            box-sizing: border-box;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            scroll-behavior: smooth;
        }

        .between-lines_wrapper__QeHD9 {
            position: relative;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            color: rgba(186, 214, 247, .32);
        }

        .between-lines_wrapper__QeHD9:after, .between-lines_wrapper__QeHD9:before {
            content: "";
            height: 1px;
        }

        .between-lines_wrapper__QeHD9:after {
            transform: rotate(180deg);
        }

        .between-lines_wrapper-gradient__abLQd {
            gap: 24px;
        }

        .between-lines_wrapper-gradient__abLQd:after, .between-lines_wrapper-gradient__abLQd:before {
            background: linear-gradient(90deg, rgba(216, 236, 248, 0), rgba(184, 216, 254, .32));
            width: 86px;
        }

        .between-lines_wrapper-solid__XGDPf {
            --line-color: rgba(186, 215, 247, .12);
            gap: 8px;
        }

        .between-lines_wrapper-solid__XGDPf:after, .between-lines_wrapper-solid__XGDPf:before {
            background: var(--line-color);
            max-width: 110px;
            flex-grow: 1;
            min-width: 20px;
        }

        .glowing-button_button__183wI {
            --line-width: 1px;
            --line-color: #c2ccff;
            --line-opacity: 1;
            --duration: 6s;
            --easing: linear;
            all: unset;
            position: relative;
            display: inline-flex;
            box-sizing: border-box;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 400;
            font-family: inherit;
            text-wrap: nowrap;
            white-space: nowrap;
            height: 32px;
            border-radius: 999px;
            transition: all .2s ease-out, outline 0s, outline-offset 0s;
            cursor: pointer;
            box-shadow: inset 0 0 0 1px rgba(186, 215, 247, .12);
            background: radial-gradient(31.2% 40.91% at 50% 151.14%, rgba(186, 214, 247, .08) 0, rgba(186, 214, 247, 0) 100%), rgba(186, 214, 247, .06);
            -webkit-backdrop-filter: blur(4px);
            backdrop-filter: blur(4px);
        }

        .glowing-button_button__183wI:before {
            inset: 2px;
            -webkit-mask-image: radial-gradient(#fff 3%, transparent 0), radial-gradient(hsla(0, 0%, 100%, .5) 3%, transparent 0);
            mask-image: radial-gradient(#fff 3%, transparent 0), radial-gradient(hsla(0, 0%, 100%, .5) 3%, transparent 0);
            -webkit-mask-position: 0 0, 15px 15px;
            mask-position: 0 0, 15px 15px;
            -webkit-mask-size: 30px 30px;
            mask-size: 30px 30px;
            -webkit-mask-repeat: repeat;
            mask-repeat: repeat;
            background: conic-gradient(from var(--angle), transparent 0, var(--line-color) 40%, transparent 40%);
            pointer-events: none;
            animation: glowing-button_line-anim__NqYIB var(--duration, 6s) infinite, glowing-button_sky-anim__nLcFP 2s var(--easing) infinite;
            opacity: .5;
        }

        .glowing-button_button__183wI:after, .glowing-button_button__183wI:before {
            content: "";
            position: absolute;
            border-radius: inherit;
            z-index: 10;
            transition: opacity 1s ease-out;
        }

        .glowing-button_button__183wI:after {
            inset: 0;
            background: radial-gradient(farthest-side at 50% 100%, var(--line-color), transparent);
            opacity: 0;
            mix-blend-mode: hard-light;
        }

        .glowing-button_button__183wI:hover:after {
            opacity: min(calc(var(--line-opacity) * .75), .5);
        }

        .glowing-button_button__183wI:hover:before {
            opacity: var(--line-opacity);
        }

        .glowing-button_text__eHs9e {
            display: inline-flex;
            gap: 6px;
            align-items: center;
            color: #d1e4fa;
            background: linear-gradient(0deg, #d8ecf8, #98c0ef);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .glowing-button_effect__Ol1KB {
            position: absolute;
            inset: 0;
            border-radius: inherit;
            filter: drop-shadow(0 0 5px var(--line-color));
        }

        .glowing-button_effect__Ol1KB:after, .glowing-button_effect__Ol1KB:before {
            content: "";
            position: absolute;
            inset: 0;
            border-radius: inherit;
            padding: var(--line-width);
            background: conic-gradient(from var(--angle), transparent 0, var(--line-color) 40%, transparent 45%);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            animation: glowing-button_line-anim__NqYIB var(--duration, 6s) var(--easing) infinite;
            opacity: var(--line-opacity, 1);
        }

        .glowing-button_effect__Ol1KB:before {
            filter: blur(8px);
            -webkit-mask: none;
            mask: none;
            opacity: max(calc(var(--line-opacity) * .1), .05);
        }

        .glowing-button_button-size-medium__SLv03 {
            --line-opacity: 0.2;
            --duration: 16s;
            height: 36px;
            line-height: 36px;
            font-size: 14px;
            padding-inline: 16px;
        }

        .text_text__diRzx {
            margin: 0;
            font-size: inherit;
            font-weight: inherit;
            color: inherit;
            text-size-adjust: none;
        }

        .text_text-size-p__25oFp {
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
        }

        .text_text-size-h4__pz05h {
            font-size: 20px;
            line-height: 24px;
            font-weight: 400;
            letter-spacing: -.24px;
            text-wrap: balance;
        }

        @media (min-width: 640px) {
            .text_text-size-h4__pz05h {
                font-size: 24px;
                line-height: 28px;
            }
        }

        .text_text-size-h5__a3s9K {
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
            text-wrap: balance;
        }

        .text_text-align-center__cYXHS {
            text-align: center;
        }

        .text_text-bold__DMjUe {
            font-weight: 500;
        }

        .text_text-normal__Srl6t {
            color: inherit;
        }

        .text_text-muted__2odSz {
            color: rgba(199, 211, 234, .64);
        }

        .text_text-gradient__A_dhc {
            color: #d8ecf8;
            background: var(--gradient-loud-100, linear-gradient(0deg, #d8ecf8 0, #98c0ef 100%));
            background-clip: text;
            text-shadow: 0 2px 16px rgba(174, 207, 242, .24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .badge_badge__1BLy8 {
            position: relative;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            white-space: nowrap;
            text-wrap: nowrap;
            flex-shrink: 0;
            color: rgba(186, 214, 247, .8);
        }

        .outline-box_box__k2nYA {
            position: relative;
            padding: 6px;
            display: grid;
            align-items: center;
            justify-self: stretch;
            align-self: stretch;
        }

        .outline-box_box-dots__Purwq:before {
            --dot-color: #d1e4fa;
            --dot-size: 4px;
            content: "";
            position: absolute;
            inset: calc(var(--dot-size) * -.5);
            pointer-events: none;
            filter: drop-shadow(0 0 8px c);
            background-image: radial-gradient(var(--dot-color) 50%, transparent 50%), radial-gradient(var(--dot-color) 50%, transparent 50%), radial-gradient(var(--dot-color) 50%, transparent 50%), radial-gradient(var(--dot-color) 50%, transparent 50%);
            background-size: var(--dot-size) var(--dot-size), var(--dot-size) var(--dot-size), var(--dot-size) var(--dot-size), var(--dot-size) var(--dot-size);
            background-position: 0 0, 100% 0, 0 100%, 100% 100%;
            background-repeat: no-repeat;
        }

        .button_button__XtUFt {
            --focus-color: rgba(187, 215, 247, .3);
            --focus-width: 2px;
            all: unset;
            box-sizing: border-box;
            display: inline-flex;
            gap: 8px;
            align-items: center;
            justify-content: center;
            height: var(--button-height, 32px);
            line-height: var(--button-height, 32px);
            font-size: 14px;
            padding-inline: 16px;
            font-weight: 400;
            font-family: inherit;
            width: 100%;
            font-weight: 500;
            border-radius: var(--brand-radius, 6px);
            cursor: pointer;
        }

        .button_button-outline__AsyrE {
            color: #d1e4fa;
            border: 1px solid rgba(186, 215, 247, .12);
        }

        .button_button-outline__AsyrE:hover {
            transition: all .2s ease-out, button .1s ease-out;
            border-color: rgba(186, 215, 247, .22);
        }

        .card_card__VgpBR {
            --line-width: 1px;
            --line-color: #adbbff;
            --start-angle: 0deg;
            --delay: 0s;
            --easing: linear;
            position: relative;
            perspective: 1000px;
        }

        .card_content__qyjch {
            position: relative;
            height: 100%;
            z-index: 1;
        }

        .card_card-size-large___Y9MM {
            border-radius: 16px;
        }

        .card_card-size-large___Y9MM .card_content__qyjch {
            padding: 42px 24px;
        }

        @media (min-width: 640px) {
            .card_card-size-large___Y9MM .card_content__qyjch {
                padding: 42px 36px;
            }
        }

        .card_card-hero__pjFbO {
            background: linear-gradient(0deg, rgba(186, 207, 247, .04), rgba(186, 207, 247, .04)), rgba(5, 6, 15, .97);
            box-shadow: inset 0 1px 1px 0 rgba(216, 236, 248, .2), inset 0 24px 48px 0 rgba(168, 216, 245, .06), 0 16px 32px rgba(0, 0, 0, .3);
            background-clip: content-box;
        }

        .card_card-hero__pjFbO:after {
            content: "";
            position: absolute;
            inset: 0;
            border-radius: inherit;
            border: 1px solid rgba(186, 207, 247, .12);
        }

        .card_card-hero__pjFbO:before {
            --dot-color: #d1e4fa;
            --dot-size: 4px;
            content: "";
            position: absolute;
            inset: 16px;
            pointer-events: none;
            filter: drop-shadow(0 0 8px rgba(209, 228, 250, 1));
            background-image: radial-gradient(var(--dot-color) 50%, transparent 50%), radial-gradient(var(--dot-color) 50%, transparent 50%), radial-gradient(var(--dot-color) 50%, transparent 50%), radial-gradient(var(--dot-color) 50%, transparent 50%);
            background-size: var(--dot-size) var(--dot-size), var(--dot-size) var(--dot-size), var(--dot-size) var(--dot-size), var(--dot-size) var(--dot-size);
            background-position: 0 0, 100% 0, 0 100%, 100% 100%;
            background-repeat: no-repeat;
        }

        .card_card-animated__5dDBc .card_effect__3CjsY {
            position: absolute;
            inset: 0;
            border-radius: inherit;
            filter: drop-shadow(0 0 10px var(--line-color));
        }

        .card_card-animated-loop__6iL1V .card_effect__3CjsY {
            opacity: .5;
            animation: card_line-anim__iu92O 12s var(--easing) infinite, card_line-opacity-loop__KzXmR 3s var(--easing) infinite;
        }

        .card_card-animated__5dDBc .card_effect__3CjsY:before {
            content: "";
            position: absolute;
            inset: 0;
            border-radius: inherit;
            padding: var(--line-width);
            background: conic-gradient(from calc(var(--angle) + var(--start-angle)), transparent 0, var(--line-color) 20%, transparent 25%);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            animation: inherit;
        }

        .input_wrapper__s8Qw7 {
            --focus-color: rgba(0, 0, 59, .2);
            position: relative;
            display: inline-flex;
            flex-direction: column;
            width: 100%;
            border-radius: var(--brand-radius, 6px);
            cursor: text;
        }

        .input_input__8iWUF {
            all: unset;
            height: 100%;
            min-height: 32px;
            line-height: 32px;
            font-size: 14px;
            padding-inline: 10px;
            font-weight: 400;
            font-family: inherit;
            border-radius: inherit;
            width: 100%;
            box-sizing: border-box;
        }

        .input_input__8iWUF[inputmode=numeric] {
            text-align: center;
        }

        .input_input-solid__kJlIe {
            border: 1px solid rgba(186, 215, 247, .12);
            background: rgba(199, 211, 234, .06);
        }

        .input_input-solid__kJlIe:hover {
            border-color: rgba(186, 215, 247, .17);
        }

        .input_input-solid__kJlIe:focus, .input_input-solid__kJlIe:focus:hover {
            border-color: rgba(186, 215, 247, .17);
            background: rgba(199, 211, 234, .1);
        }

        .input_input-solid__kJlIe::placeholder {
            color: rgba(186, 214, 247, .32);
        }

        .input_input-solid__kJlIe::selection {
            background: rgba(199, 211, 234, .2);
        }

        .link {
            --focus-color: rgba(174, 207, 242, .24);
            color: #d8ecf8;
            font-size: inherit;
            font-family: inherit;
            text-decoration: none;
            font-weight: 600;
            background-image: linear-gradient(0deg, #d8ecf8, #98c0ef);
            background-clip: text;
            text-shadow: 0 2px 16px rgba(174, 207, 242, .24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            transition: filter .2s ease-out;
        }

        .link:hover {
            filter: brightness(110%);
        }

        .powered-by_sr-only__di_Yk:not(:focus):not(:active) {
            clip: rect(0 0 0 0);
            -webkit-clip-path: inset(50%);
            clip-path: inset(50%);
            height: 1px;
            overflow: hidden;
            position: absolute;
            white-space: nowrap;
            width: 1px;
        }

        .powered-by_link__1NGQV {
            color: #bad6f7;
            text-decoration: none;
            display: block;
            opacity: .5;
            transition: opacity .2s;
            position: relative;
        }

        .powered-by_link__1NGQV:hover {
            opacity: .6;
        }

        .powered-by_link__1NGQV:after {
            content: "";
            position: absolute;
            inset: -10px;
        }

        .spotlight_spotlight__6lNkv {
            position: absolute;
            z-index: -10;
            top: 100px;
            left: 50%;
            width: 200px;
            height: 700px;
            background-image: conic-gradient(from 0deg at 50% -5%, transparent 45%, rgba(124, 145, 182, .3) 49%, rgba(124, 145, 182, .5) 50%, rgba(124, 145, 182, .3) 51%, transparent 55%);
            opacity: .5;
            border-radius: 9999px;
            filter: blur(15px);
            pointer-events: none;
            transform-origin: 50% 0;
            animation: spotlight_opacity__JVCgx calc(var(--duration, 5s) * 1.2) linear infinite var(--delay, 0s) alternate, spotlight_scale__o4qjB calc(var(--duration, 5s) * 1.7) infinite var(--delay, 0s) both;
        }

        @media (prefers-reduced-motion: reduce) {
            .spotlight_spotlight__6lNkv {
                animation: none;
                transform: translateX(-50%) rotate(calc(var(--rotate, 0deg) * 1.2));
            }
        }

        .hero {
            --logo-height: 140px;
            position: relative;
            margin-inline: auto;
            overflow: hidden;
            z-index: 1;
            display: grid;
            grid-template-columns:20px 1px 40px 1px 1fr 1px 40px 1px 20px;
            grid-template-rows:1px 0px 1px 40px 1px 50px 1px var(--logo-height) 1px 85px 1px 520px 1px 0px;
            place-items: center;
            grid-template-areas:". . . . . . . . ." ". header header header header header header header ." ". . . . . . . . ." ". . . . . . . . ." ". . . . . . . . ." ". . introducing introducing introducing introducing introducing . ." ". . . . . . . . ." ". . logo logo logo logo logo . ." ". . . . . . . . ." ". . headline headline headline headline headline . ." ". . . . . . . . ." "cards cards cards cards cards cards cards cards cards" ". . . . . . . . ." ". . . . light-switch . . . .";
        }

        @media (min-width: 440px) {
            .hero {
                --logo-height: 120px;
            }
        }

        @media (min-width: 640px) {
            .hero {
                justify-content: center;
                grid-template-columns:1fr 1px 70px 1px 70px 1px 70px 1px 370px 1px 70px 1px 70px 1px 70px 1px 1fr;
                grid-template-rows:1px 0px 1px 70px 1px 70px 1px 135px 1px 85px 1px 520px 1px 0px;
                grid-template-areas:". . . . . . . . . . . . . . . . ." "header header header header header header header header header header header header header header header header header" ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . cross-1 . introducing introducing introducing introducing introducing . cross-2 . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . logo logo logo logo logo . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . headline headline headline headline headline . . . . . ." ". . . . . . . . . . . . . . . . ." ". . cards cards cards cards cards cards cards cards cards cards cards cards cards . ." ". . . . . . . . . . . . . . . . ." ". . light-switch light-switch light-switch light-switch light-switch light-switch light-switch light-switch light-switch light-switch light-switch light-switch light-switch . .";
            }
        }

        .hero__header {
            display: grid;
            grid-template-columns:120px 1fr 120px;
            grid-template-areas:"powered-by logo github";
            align-items: center;
            justify-content: space-between;
            height: 100%;
            width: 100%;
            margin-inline: auto;
            grid-area: header;
        }

        @media (min-width: 640px) {
            .hero__header {
                grid-template-columns:1fr 1px 70px 1px 70px 1px 70px 1px 370px 1px 70px 1px 70px 1px 70px 1px 1fr;
                grid-template-areas:". . . powered-by powered-by . . . logo . . . github github . . .";
            }
        }

        .hero__powered-by {
            grid-area: powered-by;
            justify-self: start;
        }

        @media (min-width: 640px) {
            .hero__powered-by {
                justify-self: end;
                margin-right: -5px;
            }
        }

        @media (min-width: 940px) {
            .hero__powered-by {
                padding-right: 24px;
                margin-right: 0;
            }
        }

        .hero__github {
            grid-area: github;
            justify-self: end;
        }

        @media (min-width: 640px) {
            .hero__github {
                margin-right: 5px;
            }
        }

        @media (min-width: 940px) {
            .hero__github {
                justify-self: start;
                padding-left: 24px;
                margin-right: 0;
            }
        }

        .hero__workos-icon {
            grid-area: logo;
            justify-self: center;
        }

        .hero__spotlights {
            position: absolute;
            inset: 0;
            pointer-events: none;
            z-index: 1;
            -webkit-mask-image: radial-gradient(farthest-side at 50% 0, red 50%, transparent 90%);
            mask-image: radial-gradient(farthest-side at 50% 0, red 50%, transparent 90%);
            opacity: 0;
            transition: opacity 1.5s .5s;
        }

        .hero__cross {
            --line-color: rgba(186, 216, 247, .12);
            display: none;
            width: 40px;
            height: 40px;
            background-image: linear-gradient(45deg, transparent 50%, var(--line-color) 50%, transparent calc(50% + 1px)), linear-gradient(-45deg, transparent 50%, var(--line-color) 50%, transparent calc(50% + 1px));
        }

        @media (min-width: 640px) {
            .hero__cross {
                display: block;
                opacity: 0;
                transform: scale(.5);
                transition: opacity 1.5s .4s, transform 1.5s .4s;
            }
        }

        .hero__lines {
            position: absolute;
            inset: 0;
            place-content: inherit;
            grid-template-rows:inherit;
            grid-template-columns:inherit;
            pointer-events: none;
            opacity: 0;
            transition: opacity 3s 0s;
        }

        .hero__wlines {
            position: absolute;
            inset: 0;
            display: grid;
            place-content: inherit;
            grid-template-rows:inherit;
            grid-template-areas:"." "." "h-line-1" "." "h-line-2" "." "h-line-3" "." "h-line-4" "." "h-line-5" "." "h-line-6" "." ".";
        }

        @media (min-width: 640px) {
            .hero__wlines {
                justify-content: start;
                grid-template-areas:"." "." "h-line-1" "." "h-line-2" "." "h-line-3" "." "h-line-4" "." "h-line-5" "." "h-line-6" "." "h-line-7" "." "h-line-8" "." "h-line-9";
            }
        }

        .hero__vlines {
            position: absolute;
            inset: 0;
            display: grid;
            place-content: inherit;
            -webkit-mask-image: linear-gradient(180deg, #000 80%, transparent);
            mask-image: linear-gradient(180deg, #000 80%, transparent);
            grid-template-rows:inherit;
            grid-template-columns:inherit;
            grid-template-areas:". . . . . . . . ." ". . . . . . . . ." ". v-line-1 . . . . . v-line-2 ." ". . . . . . . . ." ". . . . . . . . ." ". . . . . . . . ." ". . . . . . . . ." ". . . . . . . . ." ". . . . . . . . ." ". . . . . . . . ." ". . . . . . . . ." ". . . v-line-3 . v-line-4 . . .";
        }

        @media (min-width: 640px) {
            .hero__vlines {
                grid-template-areas:". . . v-line-1 . v-line-2 . . . . . v-line-3 . v-line-4 . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . vs-line-1 . vs-line-2 . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . ." ". . . . . . . . . . . . . . . . .";
            }
        }

        .hero__vline {
            min-height: 100vh;
            width: 1px;
            background: linear-gradient(180deg, rgba(186, 215, 247, .12) 80%, transparent);
            align-self: flex-start;
        }

        .hero__hline, .hero__vline {
            position: relative;
            pointer-events: none;
        }

        .hero__hline {
            min-width: 100vw;
            height: 1px;
            background: rgba(186, 215, 247, .12);
        }

        @media (min-width: 640px) {
            .hero__hline {
                background: linear-gradient(90deg, transparent, rgba(186, 215, 247, .12), transparent);
            }
        }

        .hero {
            --intro-delay: 0.1s;
        }

        .hero__header {
            transform: translateY(-10px);
        }

        .hero__header, .hero__introducing {
            opacity: 0;
            transition: opacity 1.5s calc(var(--intro-delay) + .2s), transform 1.5s calc(var(--intro-delay) + .2s);
        }

        .hero__introducing {
            grid-area: introducing;
            transform: translateY(-5px);
        }

        .hero__logo {
            grid-area: logo;
            justify-content: center;
            z-index: -1;
        }

        .hero__logo img {
            height: 100%;
            object-fit: contain;
            pointer-events: none;
            transform: scale(.95);
            width: 100%;
            height: auto;
            transform: scale(1);
            opacity: 0;
            transition: opacity 2s var(--intro-delay);
        }

        .hero--intro .hero__logo img {
            opacity: 1;
        }

        .hero__headline {
            grid-area: headline;
            margin-block: -1px;
        }

        .hero__headline, .hero__headline * {
            opacity: 0;
            transition: opacity 1.5s calc(var(--intro-delay) + .5s), transform 1.5s calc(var(--intro-delay) + .5s);
        }

        .hero__headline * {
            transform: translateY(10px);
        }

        .hero--intro .hero__headline * {
            opacity: 1;
            transform: translateY(0);
        }

        .hero__cards {
            grid-area: cards;
            display: flex;
            align-items: center;
            justify-content: center;
            perspective: 1000px;
            z-index: 2;
        }

        .hero__card {
            width: 320px;
        }

        @media (min-width: 640px) {
            .hero__card {
                width: 392px;
            }
        }

        .hero__light-switch {
            grid-area: light-switch;
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 1.5s calc(var(--intro-delay) + 2s), transform 1.5s calc(var(--intro-delay) + 2s);
        }

        .hero__card-content, .hero__light-switch {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .hero__card-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .hero__video {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 230vw;
            transform: translate3d(-50%, -50%, 0) scale(.5);
            z-index: 100;
            -webkit-mask-image: radial-gradient(closest-side at 50% 50%, red 65%, transparent 95%);
            mask-image: radial-gradient(closest-side at 50% 50%, red 65%, transparent 95%);
            mix-blend-mode: color-dodge;
            pointer-events: none;
            filter: saturate(.7);
        }

        body {
            margin: 0px;
            padding: 0px;
        }

        @media (min-width: 640px) {
            .hero__video {
                width: 1480px;
                display: block;
            }

            .hero--intro .hero__video {
                transform: translate3d(-50%, -50%, 0) scale(.5);
            }
        }

        .hero--intro > * {
            opacity: 1;
            transform: translateY(0) translateX(0) scale(1);
        }

        .hero:after {
            content: "";
            position: absolute;
            z-index: 2;
            inset: 0;
            background: radial-gradient(50% 64.48% at 50% 35.52%, rgba(21, 61, 204, .08) 14.36%, rgba(5, 5, 11, 0) 100%), radial-gradient(50% 67.94% at 50% 32.06%, rgba(216, 236, 248, .04) 0, rgba(152, 192, 239, .01) 50%, rgba(5, 5, 11, 0) 100%);
            background-size: cover;
            pointer-events: none;
        }

        .light-switch {
            position: relative;
            width: 240px;
            height: 36px;
            padding: 4px;
            border-radius: 999px;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        }

        .light-switch__thumb {
            display: block;
            width: 50%;
            height: 100%;
            border-radius: 999px;
            transform-origin: top left;
            transition: transform .6s cubic-bezier(.165, .84, .44, 1);
        }

        .light-switch__icon-dark, .light-switch__icon-light {
            position: absolute;
            top: 50%;
            transform: translate3d(-50%, -50%, 0);
            transition: opacity .2s cubic-bezier(.165, .84, .44, 1);
        }

        .light-switch__icon-light {
            left: 75%;
        }

        .light-switch__icon-dark {
            left: 25%;
        }

        .light-switch--dark {
            color: #c7d3ea;
            box-shadow: inset 0 0 0 1px rgba(186, 214, 247, .06);
            background: var(--blue-6, rgba(186, 214, 247, .06));
            -webkit-backdrop-filter: blur(8px);
            backdrop-filter: blur(8px);
        }

        .light-switch--dark .light-switch__thumb {
            background: rgba(186, 207, 247, .04);
            box-shadow: inset 0 1px 1px 0 rgba(216, 236, 248, .2), inset 0 24px 48px 0 rgba(168, 216, 245, .06), inset 0 0 0 1px rgba(186, 207, 247, .12);
        }

        .light-switch--dark[data-state=unchecked] .light-switch__icon-light {
            opacity: .5;
        }

        .page_page__ZU32B {
            --section-height: 80vh;
            --section-max-height: 900px;
        }

        /*! CSS Used keyframes */
        @keyframes glowing-button_line-anim__NqYIB {
            to {
                --angle: 360deg;
            }
        }

        @keyframes glowing-button_sky-anim__nLcFP {
            to {
                -webkit-mask-position: 0 -15px, 15px -45px;
                mask-position: 0 -15px, 15px -45px;
            }
        }

        @keyframes card_line-anim__iu92O {
            to {
                --angle: 360deg;
            }
        }

        @keyframes card_line-opacity-loop__KzXmR {
            0% {
                opacity: .75;
            }
            50% {
                opacity: 1;
            }
            to {
                opacity: .75;
            }
        }

        @keyframes spotlight_opacity__JVCgx {
            0% {
                opacity: .6;
            }
            50% {
                opacity: .5;
            }
            95% {
                opacity: .6;
            }
        }

        @keyframes spotlight_scale__o4qjB {
            0% {
                transform: translateX(-50%) rotate(var(--rotate, 0deg)) scale(var(--scale, 1));
            }
            50% {
                transform: translateX(-50%) rotate(calc(var(--rotate, 0deg) * 1.2)) scale(calc(var(--scale, 1) * 1.1));
            }
            to {
                transform: translateX(-50%) rotate(var(--rotate, 0deg)) scale(var(--scale, 1));
            }
        }

        /*! CSS Used fontfaces */

        @property --angle {
            syntax: "<angle>";
            inherits: false;
            initial-value: 0deg
        }

        @keyframes card_line-anim__iu92O {
            to {
                --angle: 360deg
            }
        }

        @keyframes card_line-opacity__b1ltN {
            0% {
                opacity: 0
            }

            20%, 70% {
                opacity: 1
            }

            to {
                opacity: 0
            }
        }

        .ds-summary {
            padding: 9px;
            font-size: 20px;
        }

        .text_text-gradient__A_dhc {
            line-height: 55px;
        }

        .text_text-size-h5__a3s91 {
            font-size: 13px;
            color: #d5d5d5;
        }

        .hero__card {
            max-width: 80vw;
        }

        @keyframes card_line-opacity-loop__KzXmR {
            0% {
                opacity: .75
            }

            50% {
                opacity: 1
            }

            to {
                opacity: .75
            }
        }

        .text_text-gradient__A_dhc {
            display: inline-block;
        }


        .between-lines_wrapper__QeHD9 {
            position: relative;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            color: rgba(186, 214, 247, .32);
        }

        .between-lines_wrapper__QeHD9:after, .between-lines_wrapper__QeHD9:before {
            content: "";
            height: 1px;
        }

        .between-lines_wrapper__QeHD9:after {
            transform: rotate(180deg);
        }

        .between-lines_wrapper-gradient__abLQd {
            gap: 24px;
        }

        .between-lines_wrapper-gradient__abLQd:after, .between-lines_wrapper-gradient__abLQd:before {
            background: linear-gradient(90deg, rgba(216, 236, 248, 0), rgba(184, 216, 254, .32));
            width: 86px;
        }

        .text_text__diRzx {
            margin: 0;
            font-size: inherit;
            font-weight: inherit;
            color: inherit;
            text-size-adjust: none;
        }

        .text_text-size-h2__FTjRA {
            font-family: var(--font-aeonik-pro);
            font-size: 40px;
            font-weight: 400;
            text-wrap: balance;
        }

        @media (min-width: 640px) {
            .text_text-size-h2__FTjRA {
                font-size: 44px;
            }
        }

        .text_text-size-h5__a3s9K {
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
            text-wrap: balance;
        }

        .text_text-align-center__cYXHS {
            text-align: center;
        }

        .text_text-bold__DMjUe {
            font-weight: 500;
        }

        .text_text-muted__2odSz {
            color: rgba(199, 211, 234, .64);
        }

        .text_text-gradient__A_dhc {
            color: #d8ecf8;
            background: var(--gradient-loud-100, linear-gradient(0deg, #d8ecf8 0, #98c0ef 100%));
            background-clip: text;
            text-shadow: 0 2px 16px rgba(174, 207, 242, .24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .badge_badge__1BLy8 {
            position: relative;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            white-space: nowrap;
            text-wrap: nowrap;
            flex-shrink: 0;
            color: rgba(186, 214, 247, .8);
        }

        .outline-box_box__k2nYA {
            position: relative;
            padding: 6px;
            display: grid;
            align-items: center;
            justify-self: stretch;
            align-self: stretch;
        }

        .outline-box_box-dots__Purwq:before {
            --dot-color: #d1e4fa;
            --dot-size: 4px;
            content: "";
            position: absolute;
            inset: calc(var(--dot-size) * -.5);
            pointer-events: none;
            filter: drop-shadow(0 0 8px c);
            background-image: radial-gradient(var(--dot-color) 50%, transparent 50%), radial-gradient(var(--dot-color) 50%, transparent 50%), radial-gradient(var(--dot-color) 50%, transparent 50%), radial-gradient(var(--dot-color) 50%, transparent 50%);
            background-size: var(--dot-size) var(--dot-size), var(--dot-size) var(--dot-size), var(--dot-size) var(--dot-size), var(--dot-size) var(--dot-size);
            background-position: 0 0, 100% 0, 0 100%, 100% 100%;
            background-repeat: no-repeat;
        }

        .customui {
            position: relative;
            max-width: 740px;
            margin-inline: auto;
            padding-block: 60px 80px;
            margin-inline: 8px;
        }

        .pe-1 {
            padding-right: 1rem;
        }

        @media (min-width: 940px) {
            .customui {
                margin-inline: auto;
                padding-block: 120px;
            }
        }


        .customui:after, .customui:before {
            content: "";
            position: absolute;
            inset: 0;
            pointer-events: none;
        }

        .customui:after {
            --gradient-spread: 20%;
            background: linear-gradient(to bottom, transparent 0, rgba(186, 215, 247, .12) var(--gradient-spread), rgba(186, 215, 247, .12) calc(100% - var(--gradient-spread)), transparent), linear-gradient(to bottom, transparent 0, rgba(186, 215, 247, .12) var(--gradient-spread), rgba(186, 215, 247, .12) calc(100% - var(--gradient-spread)), transparent);
            background-size: 1px 100%, 1px 100%;
            background-position: 0 0, 100% 0;
            background-repeat: no-repeat;
            z-index: -1;
        }

        .customui__overlay {
            position: absolute;
            inset: 0;
            z-index: 3;
            pointer-events: none;
            background: radial-gradient(50% 50% at 50% 50%, rgba(75, 113, 250, .12) 0, rgba(5, 5, 11, 0) 100%);
        }

        .customui__header {
            position: relative;
            height: 260px;
        }

        .customui__header-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
        }

        .customui__header:after {
            --gradient-spread: 200px;
            content: "";
            position: absolute;
            inset: 0;
            left: calc(var(--gradient-spread) * -.5);
            right: calc(var(--gradient-spread) * -.5);
            pointer-events: none;
            background: linear-gradient(to left, transparent 0, rgba(186, 215, 247, .12) var(--gradient-spread), rgba(186, 215, 247, .12) calc(100% - var(--gradient-spread)), transparent), linear-gradient(to left, transparent 0, rgba(186, 215, 247, .12) var(--gradient-spread), rgba(186, 215, 247, .12) calc(100% - var(--gradient-spread)), transparent);
            background-size: 100% 1px, 100% 1px;
            background-position: 0 0, 0 100%;
            background-repeat: no-repeat;
        }

        .customui__browser {
            margin: 8px;
        }

        @media (min-width: 640px) {
            .customui__browser {
                margin: 24px;
            }
        }

        .customui__browser:after {
            --gradient-spread: 100px;
        }

        @media (min-width: 640px) {
            .customui__browser:after {
                content: "";
                position: absolute;
                inset: 0;
                z-index: -1;
                pointer-events: none;
                background: linear-gradient(to left, transparent 0, rgba(186, 215, 247, .12) var(--gradient-spread), rgba(186, 215, 247, .12) calc(100% - var(--gradient-spread)), transparent), linear-gradient(to left, transparent 0, rgba(186, 215, 247, .12) var(--gradient-spread), rgba(186, 215, 247, .12) calc(100% - var(--gradient-spread)), transparent), linear-gradient(to bottom, transparent 0, rgba(186, 215, 247, .12) var(--gradient-spread), rgba(186, 215, 247, .12) calc(100% - var(--gradient-spread)), transparent), linear-gradient(to bottom, transparent 0, rgba(186, 215, 247, .12) var(--gradient-spread), rgba(186, 215, 247, .12) calc(100% - var(--gradient-spread)), transparent);
                background-size: 100% 1px, 100% 1px, 1px 100%, 1px 100%;
                background-position: 0 calc(50% - 264px), 0 calc(50% + 300px), calc(50% - 217px) 0, calc(50% + 217px) 0;
                background-repeat: no-repeat;
            }
        }

        .browser {
            position: relative;
            display: grid;
            grid-template-rows:36px 1fr;
            border-radius: 12px;
            background: radial-gradient(107.55% 100% at 50% 0, rgba(186, 207, 247, .04) 0, rgba(6, 6, 14, 0) 100%), rgba(6, 6, 14, .4);
            box-shadow: inset 0 1px 1px 0 rgba(216, 236, 248, .2), inset 0 24px 48px 0 rgba(168, 216, 245, .06), inset 0 0 0 1px rgba(199, 211, 234, .08);
            min-height: 300px;
        }

        .browser__header {
            --dot-color: rgba(186, 207, 247, .12);
            --dot-size: 8px;
            border-bottom: 1px solid rgba(186, 207, 247, .08);
            background-image: radial-gradient(var(--dot-size) circle at 16px 50%, var(--dot-color) 50%, transparent 51%), radial-gradient(var(--dot-size) circle at 32px 50%, var(--dot-color) 50%, transparent 51%), radial-gradient(var(--dot-size) circle at 48px 50%, var(--dot-color) 50%, transparent 51%);
            background-repeat: no-repeat;
            background-color: rgba(186, 207, 247, .02);
        }

        @media only screen and (max-width: 470px) {
            .hero.hero--intro {
                /*transform: translateY(-84px);*/
            }

            .qr-box {
                display: none;
            }

            .hero__cards {
                transform: translateY(-65px) translateX(0) scale(1);
            }

            .ds-summary {
                line-height: 28px;
                display: inline-block;
                width: 100%;
                text-align: center;
            }
        }


        .download-box > div {
            max-width: 594px;
            width: 100%;
        }

        .download-box {
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            position: relative;
            z-index: 2;
        }

        .dark-box {
            --tw-text-opacity: 1;
            color: rgb(248 250 252 / var(--tw-text-opacity));
            --tw-bg-opacity: 1;
            background-color: rgb(30 41 59 / var(--tw-bg-opacity));
            padding: 8px 16px;
            border-radius: 5px;
        }

        .dark-list-style {
            box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
            --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
            --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
            margin-bottom: 6px;
            border: 0px !important;
        }

        .dark-list-style svg path {
            fill: #fff;
        }

        ul.list-group {
            list-style: none;
            padding: 0px;
        }

        ul.list-group > li {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .d-flex {
            display: flex;
        }

        .align-items-center {
            align-items: center;
        }

        .text-center {
            text-align: center;
        }

        .mb-4 {
            margin-bottom: 2rem;
        }

        .mt-5 {
            margin-top: 2.5rem;
        }

        .dl-btn {
            --line-opacity: 0.2;
            --duration: 16s;
            height: 36px;
            line-height: 36px;
            font-size: 14px;
            padding-inline: 16px;
            --line-width: 1px;
            --line-color: #c2ccff;
            --line-opacity: 1;
            --duration: 6s;
            --easing: linear;
            all: unset;
            position: relative;
            display: inline-flex;
            box-sizing: border-box;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 400;
            font-family: inherit;
            text-wrap: nowrap;
            white-space: nowrap;
            height: 32px;
            border-radius: 999px;
            transition: all .2s ease-out, outline 0s, outline-offset 0s;
            cursor: pointer;
            box-shadow: inset 0 0 0 1px rgba(186, 215, 247, .12);
            background: radial-gradient(31.2% 40.91% at 50% 151.14%, rgba(186, 214, 247, .08) 0, rgba(186, 214, 247, 0) 100%), rgba(186, 214, 247, .06);
            -webkit-backdrop-filter: blur(4px);
            backdrop-filter: blur(4px);
            padding: 4px 15px;
        }

    </style>
@endpush

@section('content')
    <body class="theme-modern __variable_8319ca __variable_226f3d">
    <main class="page_page__ZU32B">
        <div class="hero hero--intro">
            <div class="hero__spotlights">
                <div style="--rotate:20deg;--scale:1;--duration:5s" class="spotlight_spotlight__6lNkv"></div>
                <div style="--rotate:0deg;--scale:1.02;--duration:8s" class="spotlight_spotlight__6lNkv"></div>
                <div style="--rotate:-20deg;--scale:1;--duration:4s" class="spotlight_spotlight__6lNkv"></div>
            </div>
            <div class="hero__lines">
                <div class="hero__wlines">
                    <div class="hero__hline" style="grid-area:h-line-1;opacity:0.75"></div>
                    <div class="hero__hline" style="grid-area:h-line-2;opacity:1"></div>
                    <div class="hero__hline" style="grid-area:h-line-3;opacity:1"></div>
                    <div class="hero__hline" style="grid-area:h-line-4;opacity:1"></div>
                    <div class="hero__hline" style="grid-area:h-line-5;opacity:1"></div>
                    <div class="hero__hline" style="grid-area:h-line-6;opacity:1"></div>
                </div>
                <div class="hero__vlines">
                    <div class="hero__vline" style="grid-area:v-line-1;opacity:1"></div>
                    <div class="hero__vline" style="grid-area:v-line-2;opacity:1"></div>
                    <div class="hero__vline" style="grid-area:v-line-3;opacity:1"></div>
                    <div class="hero__vline" style="grid-area:v-line-4;opacity:1"></div>
                    <div class="hero__vline" style="grid-area:v-line-5;opacity:0.5"></div>
                    <div class="hero__vline" style="grid-area:v-line-6;opacity:0.5"></div>
                </div>
            </div>
            {{--            <header class="hero__header">--}}
            {{--                <div class="hero__powered-by"><h2 class="powered-by_sr-only__di_Yk">WorkOS</h2><a--}}
            {{--                        href="https://workos.com" target="_blank" rel="noopener noreferrer"--}}
            {{--                        class="powered-by_link__1NGQV">--}}
            {{--                        <svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="72" height="14" fill="none">--}}
            {{--                            <g fill="currentColor" clip-path="url(#a)">--}}
            {{--                                <path--}}
            {{--                                    d="M0 .186h2.79L4.8 8.464c.372 1.562.446 2.493.446 2.493h.037s.13-.911.503-2.493L7.7.186h3.163l2.027 8.278c.39 1.618.484 2.493.484 2.493h.056s.038-.875.409-2.493L15.775.186h2.79L15.03 13.765h-3.107L9.785 5.543c-.446-1.786-.484-2.585-.484-2.585h-.037s-.038.799-.447 2.585l-2.009 8.222H3.61L0 .185ZM18.1 8.832c0-3.107 2.008-5.116 5.041-5.116 3.014 0 5.023 2.009 5.023 5.116 0 3.124-2.01 5.153-5.023 5.153-3.031-.002-5.042-2.028-5.042-5.153Zm7.459 0c0-2.01-.967-3.145-2.418-3.145-1.562 0-2.437 1.266-2.437 3.145 0 2.046.967 3.18 2.437 3.18 1.563 0 2.418-1.265 2.418-3.18ZM29.41 3.868h2.473v1.824h.056c.428-.949 1.45-1.898 3.199-1.898.297 0 .484.038.614.074v2.474h-.074s-.223-.074-.837-.074c-1.916 0-2.958 1.134-2.958 3.254v4.241h-2.474V3.868ZM36.906.186h2.473v4.352c0 2.567-.038 3.032-.038 3.032h.038l3.683-3.701h3.088l-4.314 4.278 5.004 5.618H43.92l-3.61-4.093-.93.91v3.181h-2.473V.187ZM46.876 7.025c0-4.185 2.605-6.975 6.511-6.975 3.907 0 6.511 2.79 6.511 6.975S57.293 14 53.388 14c-3.907 0-6.512-2.79-6.512-6.975Zm10.324 0c0-2.846-1.525-4.763-3.814-4.763-2.29 0-3.813 1.917-3.813 4.763s1.525 4.762 3.813 4.762c2.288 0 3.814-1.916 3.814-4.762ZM60.845 9.245h2.828c0 1.6 1.098 2.493 2.772 2.493 1.414 0 2.382-.707 2.382-1.712 0-1.134-.744-1.433-3.107-1.897-2.158-.428-4.446-1.154-4.446-3.982C61.274 1.786 63.283 0 66.352 0c3.199 0 5.172 1.674 5.172 4.203h-2.828c0-1.227-.949-1.99-2.344-1.99-1.413 0-2.288.687-2.288 1.692 0 1.06.614 1.469 2.511 1.86 2.808.595 5.116.91 5.116 4.035 0 2.476-2.158 4.15-5.34 4.15-3.236 0-5.506-1.879-5.506-4.705Z"></path>--}}
            {{--                            </g>--}}
            {{--                            <defs>--}}
            {{--                                <clipPath id="a">--}}
            {{--                                    <path fill="#fff" d="M0 0h71.691v14H0z"></path>--}}
            {{--                                </clipPath>--}}
            {{--                            </defs>--}}
            {{--                        </svg>--}}
            {{--                    </a></div>--}}
            {{--                <svg xmlns="http://www.w3.org/2000/svg" width="36" viewBox="0 0 55.4 48" class="hero__workos-icon">--}}
            {{--                    <g fill="rgba(186, 214, 247, 0.48)">--}}
            {{--                        <path--}}
            {{--                            d="M0 24c0 1.1.3 2.1.8 3l9.7 16.8c1 1.7 2.5 3.1 4.4 3.7 3.6 1.2 7.5-.3 9.4-3.5l2.3-4.1-9.2-16L27.2 7l2.3-4c.7-1.2 1.6-2.2 2.7-3h-15c-2.6 0-5.1 1.4-6.4 3.7L.8 21c-.5.9-.8 1.9-.8 3z"></path>--}}
            {{--                        <path--}}
            {{--                            d="M55.4 24c0-1.1-.3-2.1-.8-3L44.8 4C42.9.7 39-.7 35.4.5c-1.9.6-3.4 2-4.4 3.7L28.7 8 38 24l-9.8 16.9-2.3 4.1c-.7 1.2-1.6 2.2-2.7 3h15.1c2.6 0 5.1-1.4 6.4-3.7l10-17.3c.4-.9.7-1.9.7-3z"></path>--}}
            {{--                    </g>--}}
            {{--                </svg>--}}
            {{--                <div class="hero__github"><a--}}
            {{--                        class="glowing-button_button__183wI glowing-button_button-size-medium__SLv03"--}}
            {{--                        href="https://github.com/workos/authkit" target="_blank"><span--}}
            {{--                            class="glowing-button_effect__Ol1KB"></span><span class="glowing-button_text__eHs9e">View GitHub</span></a>--}}
            {{--                </div>--}}
            {{--            </header>--}}
            <div class="between-lines_wrapper__QeHD9 between-lines_wrapper-gradient__abLQd hero__introducing">
                <div class="badge_badge__1BLy8"><span
                        class="text_text__diRzx text_text-gradient__A_dhc">
                        رویداد زنده
                    </span></div>
            </div>
            <div class="outline-box_box__k2nYA outline-box_box-dots__Purwq hero__logo">
                <h2
                    class="text_text__diRzx text_text-normal__Srl6t text_text-align-center__cYXHS text_text-size-h4__pz05h">
                    <span class="text_text__diRzx text_text-gradient__A_dhc" style="    font-weight: 600;    font-size: 32px;    padding: 10px;    line-height: normal;">
                     {{$detail->title}}
                    </span>
                </h2>
            </div>
            <div class="outline-box_box__k2nYA outline-box_box-dots__Purwq hero__headline">
                <h4
                    class="text_text__diRzx text_text-normal__Srl6t text_text-align-center__cYXHS text_text-size-h4__pz05h">
                    <span class="text_text__diRzx text_text-gradient__A_dhc ds-summary" style=" line-height: normal; font-size: 14px; ">
                      {{$detail->summery}}
                    </span>
                </h4>
            </div>
            <div class="hero__cards">

                <div data-card-center="true" style="z-index: 1; opacity: 1; transform: none;">
                    <div style="    ">
                        <div
                            class="card_card__VgpBR card_card-hero__pjFbO card_card-size-large___Y9MM card_card-animated__5dDBc card_card-animated-loop__6iL1V hero__card"
                            style="--brand-radius:999px">
                            <div class="card_effect__3CjsY" style="--start-angle: 256deg; --delay: 7s;"></div>
                            <div class="card_content__qyjch">
                                <div class="hero__card-content">
                                    <div class="hero__card-header">
                                        <p class="text_text__diRzx text_text-normal__Srl6t text_text-bold__DMjUe text_text-align-center__cYXHS text_text-size-h5__a3s9K">
                                            جهت ورود به این رویداد کافیست روی لینک زیر کلیک کنید
                                        </p>
                                    </div>

                                    <button onclick="openGoogleMeet('{{$meetingCode}}')"
                                            class="button_button__XtUFt button_button-outline__AsyrE">ورود
                                    </button>
                                    <div style="margin-block:10px">
                                        <div class="between-lines_wrapper__QeHD9 between-lines_wrapper-solid__XGDPf">
                                            یا
                                        </div>
                                    </div>
                                    <p class="text_text__diRzx text_text-normal__Srl6t text_text-bold__DMjUe text_text-align-center__cYXHS text_text-size-h5__a3s91">
                                        از کد زیر برای ورود در نرم افزار گوگل میت استفاده نمایید:
                                    </p>

                                    <button class="button_button__XtUFt button_button-">
                                        <div class="input-group mb-4 ltr" onclick="copyText()">
                                            <input class="input_input__8iWUF input_input-solid__kJlIe" readonly
                                                   id="meetingCode"
                                                   type="text"
                                                   style=" text-align: center; border-radius: 39px; "
                                                   value="{{ $meetingCode  }}">
                                        </div>
                                    </button>

                                    <p class="text_text__diRzx text_text-muted__2odSz text_text-align-center__cYXHS text_text-size-p__25oFp qr-box">
                                        <img
                                            src="https://quickchart.io/qr?text={{urlencode($detail->meeting->meeting_link)}}&light=0f172a&dark=c9c9c9&margin=2&size=200"
                                            width="100" alt="QR Code">
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

            </div>


        </div>
        @if($detail->download_access)
            <div class="customui">
                <div style="">
                    <div class="browser customui__browser">
                        <div class="browser__header"></div>
                        <div class="download-box-wrapper">


                            <div class="download-box">
                                <livewire:public.download-meeting-file :meeting-id="$detail->meeting->meeting_id"
                                                                       :meeting-key="meetingKey($detail->meeting->id)"
                                                                       :chat-download-access="$detail->chat_download_access"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        @endif
    </main>
    </body>
@endsection

@section('footer')

@endsection
