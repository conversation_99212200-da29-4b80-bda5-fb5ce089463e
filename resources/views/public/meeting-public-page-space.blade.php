@extends('baselayout.public.meeting.meeting-base-layout-minify')
@section('meta')
    <meta property="og:image" content="{{asset('assets/logo_meet_2020q4_color_2x_web_96dp.png')}}">
    <meta property="og:image:secure_url" content="{{asset('assets/logo_meet_2020q4_color_2x_web_96dp.png')}}">
@endsection

@push('style')

    <style>
        .antialiased {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .svg-holder {
            width: 100vw;
            height: 100dvh;
            overflow: hidden;
            display: flex;
            position: absolute;
            top: 0px;
            right: 0px;
        }

        .bg-slate-900 {
            background-color: #030014;
        }


        @media screen {
            .bx {
                font-family: boxicons !important;
                font-weight: 400;
                font-style: normal;
                font-variant: normal;
                line-height: 1;
                text-rendering: auto;
                display: inline-block;
                text-transform: none;
                speak: none;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

            .bx-chevron-up:before {
                content: "\ea57";
            }

            .bx-lock:before {
                content: "\eb49";
            }

            .bx-clipboard:before {
                content: "\ea5e";
            }
        }

        @media screen {
            :root {
                --swiper-theme-color: #007aff;
            }

            :root {
                --swiper-navigation-size: 44px;
            }
        }

        @media screen {
            :root {
                --si-blue: #0d6efd;
                --si-indigo: #6610f2;
                --si-purple: #6f42c1;
                --si-pink: #d63384;
                --si-red: #dc3545;
                --si-orange: #fd7e14;
                --si-yellow: #ffc107;
                --si-green: #198754;
                --si-teal: #20c997;
                --si-cyan: #0dcaf0;
                --si-black: #000;
                --si-white: #fff;
                --si-gray: #9397ad;
                --si-gray-dark: #33354d;
                --si-gray-100: #f3f6ff;
                --si-gray-200: #eff2fc;
                --si-gray-300: #e2e5f1;
                --si-gray-400: #d4d7e5;
                --si-gray-500: #b4b7c9;
                --si-gray-600: #9397ad;
                --si-gray-700: #565973;
                --si-gray-800: #33354d;
                --si-gray-900: #0b0f19;
                --si-primary: #3b49dc;
                --si-secondary: #eff2fc;
                --si-success: #22c55e;
                --si-info: #4c82f7;
                --si-warning: #ffba08;
                --si-danger: #ef4444;
                --si-light: #fff;
                --si-dark: #0b0f19;
                --si-primary-rgb: 59, 73, 220;
                --si-secondary-rgb: 239, 242, 252;
                --si-success-rgb: 34, 197, 94;
                --si-info-rgb: 76, 130, 247;
                --si-warning-rgb: 255, 186, 8;
                --si-danger-rgb: 239, 68, 68;
                --si-light-rgb: 255, 255, 255;
                --si-dark-rgb: 11, 15, 25;
                --si-white-rgb: 255, 255, 255;
                --si-black-rgb: 0, 0, 0;
                --si-body-color-rgb: 86, 89, 115;
                --si-body-bg-rgb: 255, 255, 255;
                --si-font-sans-serif: "yekanbakh", sans-serif;
                --si-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
                --si-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
                --si-body-font-family: var(--si-font-sans-serif);
                --si-body-font-size: 1rem;
                --si-body-font-weight: 400;
                --si-body-line-height: 1.6;
                --si-body-color: #efefef;
                --si-body-bg: #030014;
                --si-border-width: 1px;
                --si-border-style: solid;
                --si-border-color: #e2e5f1;
                --si-border-color-translucent: rgba(0, 0, 0, 0.175);
                --si-border-radius: 0.375rem;
                --si-border-radius-sm: 0.25rem;
                --si-border-radius-lg: 0.5rem;
                --si-border-radius-xl: 1rem;
                --si-border-radius-2xl: 2rem;
                --si-border-radius-pill: 50rem;
                --si-link-color: #3b49dc;
                --si-link-hover-color: #3e41ee;
                --si-code-color: #e3116c;
                --si-highlight-bg: #fff3cd;
                --tw-scroll-snap-strictness: proximity;
                --tw-gradient-from-position: ;
                --tw-gradient-via-position: ;
                --tw-gradient-to-position: ;
            }

            *, *::before, *::after {
                box-sizing: border-box;
            }

            body {
                margin: 0;
                font-family: var(--si-body-font-family);
                font-size: var(--si-body-font-size);
                font-weight: var(--si-body-font-weight);
                line-height: var(--si-body-line-height);
                color: var(--si-body-color);
                text-align: var(--si-body-text-align);
                background-color: var(--si-body-bg);
                -webkit-text-size-adjust: 100%;
                -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
            }

            .theme-space .btn.btn-light {
                background: #232537;
            }

            h2, .h2 {
                margin-top: 0;
                margin-bottom: 1rem;
                font-weight: 800;
                line-height: 1.3;
                color: var(--si-gray-900);
            }

            h2, .h2 {
                font-size: calc(1.625rem + 0.9vw);
            }

            @media (min-width: 1200px) {
                h2, .h2 {
                    font-size: 2.7rem;
                }
            }
            p {
                margin-top: 0;
                margin-bottom: 1.25rem;
            }

            a {
                color: var(--si-link-color);
                text-decoration: underline;
            }

            a:hover {
                color: var(--si-link-hover-color);
                text-decoration: none;
            }

            img, svg {
                vertical-align: middle;
            }

            input {
                margin: 0;
                font-family: inherit;
                font-size: inherit;
                line-height: inherit;
            }

            iframe {
                border: 0;
            }

            .lead {
                font-size: calc(1.275rem + 0.3vw);
                font-weight: 400;
            }

            @media (min-width: 1200px) {
                .lead {
                    font-size: 1.1rem;
                }
            }
            .container {
                --si-gutter-x: 1.5rem;
                --si-gutter-y: 0;
                width: 100%;
                padding-right: calc(var(--si-gutter-x) * .5);
                padding-left: calc(var(--si-gutter-x) * .5);
                margin-right: auto;
                margin-left: auto;
            }

            @media (min-width: 500px) {
                .container {
                    max-width: 97%;
                }

            }
            @media (min-width: 768px) {
                .container {
                    max-width: 95%;
                }
            }
            @media (min-width: 992px) {
                .container {
                    max-width: 95%;
                }
            }
            @media (min-width: 1200px) {
                .container {
                    max-width: 95%;
                }
            }
            @media (min-width: 1400px) {
                .container {
                    max-width: 1320px;
                }
            }
            .row {
                --si-gutter-x: 1.5rem;
                --si-gutter-y: 0;
                display: flex;
                flex-wrap: wrap;
                margin-top: calc(-1 * var(--si-gutter-y));
                margin-right: calc(-0.5 * var(--si-gutter-x));
                margin-left: calc(-0.5 * var(--si-gutter-x));
            }

            .row > * {
                flex-shrink: 0;
                width: 100%;
                max-width: 100%;
                padding-right: calc(var(--si-gutter-x) * .5);
                padding-left: calc(var(--si-gutter-x) * .5);
                margin-top: var(--si-gutter-y);
            }

            @media (min-width: 992px) {
                .col-lg-5 {
                    flex: 0 0 auto;
                    width: 41.66666667%;
                }

                .col-lg-7 {
                    flex: 0 0 auto;
                    width: 58.33333333%;
                }
            }
            @media (min-width: 1200px) {
                .col-xl-6 {
                    flex: 0 0 auto;
                    width: 50%;
                }

                .offset-xl-1 {
                    margin-left: 8.33333333%;
                }
            }
            .form-control {
                display: block;
                width: 100%;
                padding: .625rem 1rem;
                font-size: 0.875rem;
                font-weight: 400;
                line-height: 1.6;
                color: #f3f3f3;
                background-color: rgb(30 41 59 / 75%);
                background-clip: padding-box;
                border: 1px solid rgb(51 65 85);
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                border-radius: .575rem;
                box-shadow: inset 0 0 0 rgba(0, 0, 0, 0);
                transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
                box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
                --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px -1px rgba(0, 0, 0, .1);
                --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
            }

            @media (prefers-reduced-motion: reduce) {
                .form-control {
                    transition: none;
                }
            }
            .form-control:focus {
                color: #ffffff;
                background-color: #1b1f2cf5;
                /* border-color: rgba(99, 102, 241, .35); */
                outline: 0;
                /* box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 .5rem 1.125rem -0.5rem rgba(99, 102, 241, .2); */
            }

            .form-control::placeholder {
                color: #b4b7c9;
                opacity: 1;
            }

            .form-control:disabled {
                background-color: #f3f6ff;
                opacity: 1;
            }

            .input-group {
                position: relative;
                display: flex;
                flex-wrap: wrap;
                align-items: stretch;
                width: 100%;
            }

            .input-group > .form-control {
                position: relative;
                flex: 1 1 auto;
                width: 1%;
                min-width: 0;
            }

            .input-group > .form-control:focus {
                z-index: 5;
            }

            .input-group-text {
                display: flex;
                align-items: center;
                padding: .625rem 1rem;
                font-size: 0.875rem;
                font-weight: 400;
                line-height: 1.6;
                color: #fff;
                text-align: center;
                white-space: nowrap;
                background-color: #151d2ad9;
                border: 1px solid rgb(51 65 85);
                border-radius: .575rem;
            }

            .input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }

            .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
                margin-left: -1px;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }

            .btn {
                --si-btn-padding-x: 1.75rem;
                --si-btn-padding-y: 0.625rem;
                --si-btn-font-size: 0.875rem;
                --si-btn-font-weight: 600;
                --si-btn-line-height: 1.6;
                --si-btn-color: #565973;
                --si-btn-bg: transparent;
                --si-btn-border-width: 1px;
                --si-btn-border-color: transparent;
                --si-btn-border-radius: 0.375rem;
                --si-btn-hover-border-color: transparent;
                --si-btn-box-shadow: unset;
                --si-btn-disabled-opacity: 0.65;
                --si-btn-focus-box-shadow: 0 0 0 0 rgba(var(--si-btn-focus-shadow-rgb), .5);
                display: inline-block;
                padding: var(--si-btn-padding-y) var(--si-btn-padding-x);
                font-family: var(--si-btn-font-family);
                font-size: var(--si-btn-font-size);
                font-weight: var(--si-btn-font-weight);
                line-height: var(--si-btn-line-height);
                color: var(--si-btn-color);
                text-align: center;
                text-decoration: none;
                white-space: nowrap;
                vertical-align: middle;
                cursor: pointer;
                -webkit-user-select: none;
                -moz-user-select: none;
                user-select: none;
                border: var(--si-btn-border-width) solid var(--si-btn-border-color);
                border-radius: var(--si-btn-border-radius);
                background-color: var(--si-btn-bg);
                box-shadow: var(--si-btn-box-shadow);
                transition: color .2s ease-in-out, background-color .2s ease-in-out, border-color .2s ease-in-out, box-shadow .2s ease-in-out;
            }

            @media (prefers-reduced-motion: reduce) {
                .btn {
                    transition: none;
                }
            }
            .btn:hover {
                color: var(--si-btn-hover-color);
                background-color: var(--si-btn-hover-bg);
                border-color: var(--si-btn-hover-border-color);
            }

            .btn:first-child:active {
                color: var(--si-btn-active-color);
                background-color: var(--si-btn-active-bg);
                border-color: var(--si-btn-active-border-color);
                box-shadow: var(--si-btn-active-shadow);
            }

            .btn:disabled {
                color: var(--si-btn-disabled-color);
                pointer-events: none;
                background-color: var(--si-btn-disabled-bg);
                border-color: var(--si-btn-disabled-border-color);
                opacity: var(--si-btn-disabled-opacity);
                box-shadow: none;
            }

            .btn-primary {
                --si-btn-color: #000;
                --si-btn-bg: #3b49dc;
                --si-btn-border-color: #3b49dc;
                --si-btn-hover-color: #000;
                --si-btn-hover-bg: #7a7df3;
                --si-btn-hover-border-color: #7375f2;
                --si-btn-focus-shadow-rgb: 84, 87, 205;
                --si-btn-active-color: #000;
                --si-btn-active-bg: #8285f4;
                --si-btn-active-border-color: #7375f2;
                --si-btn-active-shadow: unset;
                --si-btn-disabled-color: #000;
                --si-btn-disabled-bg: #3b49dc;
                --si-btn-disabled-border-color: #3b49dc;
            }

            .btn-lg {
                --si-btn-padding-y: 0.785rem;
                --si-btn-padding-x: 2rem;
                --si-btn-font-size: 1rem;
                --si-btn-border-radius: 0.5rem;
            }

            .opacity-60 {
                opacity: .6 !important;
            }

            .overflow-hidden {
                overflow: hidden !important;
            }

            .d-table {
                display: table !important;
            }

            .d-flex {
                display: flex !important;
            }

            .d-none {
                display: none !important;
            }

            .shadow-primary {
                box-shadow: 0 .5rem 1.125rem -0.5rem rgba(99, 102, 241, .9) !important;
            }

            .position-relative {
                position: relative !important;
            }

            .position-absolute {
                position: absolute !important;
            }

            .top-0 {
                top: 0 !important;
            }

            .top-50 {
                top: 50% !important;
            }

            .bottom-0 {
                bottom: 0 !important;
            }

            .start-0 {
                left: 0 !important;
            }

            .end-0 {
                right: 0 !important;
            }

            .translate-middle {
                transform: translate(-50%, -50%) !important;
            }

            .translate-middle-y {
                transform: translateY(-50%) !important;
            }

            .w-100 {
                width: 100% !important;
            }

            .h-100 {
                height: 100% !important;
            }

            .flex-column {
                flex-direction: column !important;
            }

            .flex-shrink-0 {
                flex-shrink: 0 !important;
            }

            .justify-content-center {
                justify-content: center !important;
            }

            .justify-content-between {
                justify-content: space-between !important;
            }

            .align-items-start {
                align-items: flex-start !important;
            }

            .align-items-center {
                align-items: center !important;
            }

            .mx-auto {
                margin-right: auto !important;
                margin-left: auto !important;
            }

            .mt-5 {
                margin-top: 3rem !important;
            }

            .me-2 {
                margin-right: .5rem !important;
            }

            .mb-2 {
                margin-bottom: .5rem !important;
            }

            .mb-3 {
                margin-bottom: 1rem !important;
            }

            .mb-4 {
                margin-bottom: 1.5rem !important;
            }

            .me-n4 {
                margin-right: -1.5rem !important;
            }

            .mb-n2 {
                margin-bottom: -0.5rem !important;
            }

            .p-1 {
                padding: .25rem !important;
            }

            .p-4 {
                padding: 1.5rem !important;
            }

            .px-4 {
                padding-right: 1.5rem !important;
                padding-left: 1.5rem !important;
            }

            .py-5 {
                padding-top: 3rem !important;
                padding-bottom: 3rem !important;
            }

            .pb-3 {
                padding-bottom: 1rem !important;
            }

            .pb-5 {
                padding-bottom: 3rem !important;
            }

            .fs-base {
                font-size: 1rem !important;
            }

            .fs-sm {
                font-size: 0.875rem !important;
            }

            .fw-semibold {
                font-weight: 700 !important;
            }

            .text-center {
                text-align: center !important;
            }

            .text-uppercase {
                text-transform: uppercase !important;
            }

            .text-primary {
                color: #3b49dc !important;
            }

            .text-light {
                color: #fff !important;
            }

            .text-muted {
                color: var(--si-gray-600) !important;
            }

            .bg-light {
                --si-bg-opacity: 1;
                background-color: rgba(var(--si-light-rgb), var(--si-bg-opacity)) !important;
            }

            .bg-white {
                --si-bg-opacity: 1;
                background-color: rgba(var(--si-white-rgb), var(--si-bg-opacity)) !important;
            }

            .rounded-3 {
                border-radius: var(--si-border-radius-lg) !important;
            }

            .rounded-circle {
                border-radius: 50% !important;
            }

            .bg-size-cover {
                background-size: cover !important;
            }

            .bg-position-center-end {
                background-position: center right !important;
            }

            .bg-repeat-0 {
                background-repeat: no-repeat !important;
            }

            .bg-gradient-primary {
                background: linear-gradient(90deg, #3b49dc 0%, #8b5cf6 50%, #d946ef 100%) !important;
            }

            .zindex-5 {
                z-index: 5 !important;
            }

            @media (min-width: 500px) {
                .d-sm-flex {
                    display: flex !important;
                }

                .flex-sm-row {
                    flex-direction: row !important;
                }

                .mx-sm-0 {
                    margin-right: 0 !important;
                    margin-left: 0 !important;
                }

                .ms-sm-3 {
                    margin-left: 1rem !important;
                }

                .p-sm-5 {
                    padding: 3rem !important;
                }
            }
            @media (min-width: 768px) {
                .mb-md-4 {
                    margin-bottom: 1.5rem !important;
                }

                .px-md-4 {
                    padding-right: 1.5rem !important;
                    padding-left: 1.5rem !important;
                }

                .pb-md-3 {
                    padding-bottom: 1rem !important;
                }
            }
            @media (min-width: 992px) {
                .d-lg-block {
                    display: block !important;
                }

                .mt-lg-0 {
                    margin-top: 0 !important;
                }

                .mb-lg-0 {
                    margin-bottom: 0 !important;
                }

                .mb-lg-5 {
                    margin-bottom: 3rem !important;
                }
            }
            @media (min-width: 1200px) {
                .me-xl-4 {
                    margin-right: 1.5rem !important;
                }

                .mb-xl-n5 {
                    margin-bottom: -3rem !important;
                }

                .ms-xl-n4 {
                    margin-left: -1.5rem !important;
                }

                .pt-xl-2 {
                    padding-top: .5rem !important;
                }
            }
            html * {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

            html, body {
                height: 100%;
            }

            @keyframes spinner-border {
                to {
                    transform: rotate(360deg) /* rtl:ignore */
                }
            }
            body {
                display: flex;
                flex-direction: column;
            }

            .page-wrapper {
                flex: 1 0 auto;
            }

            :root {
                --si-user-selection-color: rgba(var(--si-primary-rgb), 0.22);
                --si-heading-link-color: #33354d;
                --si-heading-link-hover-color: #3b49dc;
            }

            a {
                transition: color .2s ease-in-out;
            }

            a:focus {
                outline: none;
            }

            img {
                max-width: 100%;
                height: auto;
                vertical-align: middle;
            }

            svg {
                max-width: 100%;
            }

            iframe {
                width: 100%;
            }

            ::selection {
                background: var(--si-user-selection-color);
            }

            .form-control:disabled {
                cursor: not-allowed;
                box-shadow: none !important;
            }

            .form-control:disabled {
                box-shadow: none !important;
            }

            .form-control:disabled::placeholder {
                color: #9397ad;
            }

            .btn {
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }

            .btn:not([class^=btn-outline-]):not([class*=" btn-outline-"]):not(.btn-secondary):not(.btn-light):not(.btn-link) {
                --si-btn-color: #fff;
            }

            .btn:hover, .btn:disabled {
                box-shadow: none !important;
            }

            .btn-primary {
                --si-btn-hover-color: #fff;
                --si-btn-active-color: #fff;
                --si-btn-hover-bg: #4044ee;
                --si-btn-active-bg: #4044ee;
                --si-btn-hover-border-color: #4044ee;
                --si-btn-active-border-color: #4044ee;
                --si-btn-disabled-color: #fff;
            }

            .btn-scroll-top {
                --si-btn-scroll-top-size: 2.75rem;
                --si-btn-scroll-top-border-radius: 50%;
                --si-btn-scroll-top-color: #fff;
                --si-btn-scroll-top-hover-color: #fff;
                --si-btn-scroll-top-bg: rgba(11, 15, 25, 0.2);
                --si-btn-scroll-top-hover-bg: rgba(11, 15, 25, 0.4);
                --si-btn-scroll-top-icon-size: 1.5rem;
                position: fixed;
                display: flex;
                align-items: center;
                justify-content: center;
                right: 1.25rem;
                bottom: -4.125rem;
                width: var(--si-btn-scroll-top-size);
                height: var(--si-btn-scroll-top-size);
                transition: bottom 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55), opacity .3s, background-color .25s ease-in-out;
                border-radius: var(--si-btn-scroll-top-border-radius);
                background-color: var(--si-btn-scroll-top-bg);
                color: var(--si-btn-scroll-top-color);
                text-decoration: none;
                opacity: 0;
                z-index: 1030;
            }

            .btn-scroll-top > .btn-scroll-top-icon {
                font-size: var(--si-btn-scroll-top-icon-size);
                font-weight: bold;
            }

            .btn-scroll-top .btn-scroll-top-tooltip {
                position: absolute;
                top: 50%;
                right: 100%;
                transform: translateY(-50%);
                transition: opacity .25s ease-in-out, visibility .25s ease-in-out;
                visibility: hidden;
                opacity: 0;
            }

            .btn-scroll-top:hover {
                background-color: var(--si-btn-scroll-top-hover-bg);
                color: var(--si-btn-scroll-top-hover-color);
            }

            .btn-scroll-top:hover .btn-scroll-top-tooltip {
                visibility: visible;
                opacity: 1;
            }

            @media (max-width: 499.98px) {
                .btn-scroll-top {
                    width: calc(var(--si-btn-scroll-top-size) * .8);
                    height: calc(var(--si-btn-scroll-top-size) * .8);
                    right: 1rem;
                }
            }
        }

        /*!  media=screen */
        @media screen {
            body {
                font-family: iransansx, tahoma;
            }

            html {
                font-size: 14px;
            }

            .rtl {
                direction: rtl;
            }

            .ltr {
                direction: ltr;
            }

            .min-w-300px {
                min-width: 300px;
            }

            @media screen and (max-width: 429px) {
                .only-desktop {
                    display: none !important;
                }
            }
        }

        /*! CSS Used fontfaces */
        @font-face {
            font-family: boxicons;
            font-weight: 400;
            font-style: normal;
            src: url(({{asset('/index-assets/eot/boxicons.eot')}});
            src: url(({{asset('/index-assets/eot/boxicons.eot')}}) format('embedded-opentype'),
        url(({{asset('/index-assets/woff2/boxicons.woff2')}}) format('woff2'),
        url(({{asset('/index-assets/woff/boxicons.woff')}}) format('woff'),
        url(({{asset('/index-assets/ttf/boxicons.ttf')}}) format('truetype'),
        url(({{asset('/index-assets/svg/boxiconsd41d.svg?#boxicons')}}) format('svg');
        }

        @font-face {
            font-family: iransansx;
            src: url({{asset('/assets/css/fonts/IRANSansX-Regular.woff')}}) format('woff');
        }

        @font-face {
            font-family: iransansx;
            src: url({{asset('/assets/css/fonts/IRANSansX-Bold.woff')}}) format('woff');
            font-weight: 700;
        }

        /*loading*/

        @media screen {

            svg {
                vertical-align: middle;
            }

            .badge {
                --si-badge-padding-x: 0.6em;
                --si-badge-padding-y: 0.35em;
                --si-badge-font-size: 0.8125em;
                --si-badge-font-weight: 600;
                --si-badge-color: #fff;
                --si-badge-border-radius: 0.25rem;
                display: inline-block;
                padding: var(--si-badge-padding-y) var(--si-badge-padding-x);
                font-size: var(--si-badge-font-size);
                font-weight: var(--si-badge-font-weight);
                line-height: 1;
                color: var(--si-badge-color);
                text-align: center;
                white-space: nowrap;
                vertical-align: baseline;
                border-radius: var(--si-badge-border-radius);
            }

            .badge:empty {
                display: none;
            }

            .spinner-border {
                display: inline-block;
                width: var(--si-spinner-width);
                height: var(--si-spinner-height);
                vertical-align: var(--si-spinner-vertical-align);
                border-radius: 50%;
                animation: var(--si-spinner-animation-speed) linear infinite var(--si-spinner-animation-name);
            }

            .spinner-border {
                --si-spinner-width: 2rem;
                --si-spinner-height: 2rem;
                --si-spinner-vertical-align: -0.125em;
                --si-spinner-border-width: 0.15em;
                --si-spinner-animation-speed: 0.75s;
                --si-spinner-animation-name: spinner-border;
                border: var(--si-spinner-border-width) solid currentcolor;
                border-right-color: rgba(0, 0, 0, 0);
            }

            .spinner-border-sm {
                --si-spinner-width: 1rem;
                --si-spinner-height: 1rem;
                --si-spinner-border-width: 0.1em;
            }

            @media (prefers-reduced-motion: reduce) {
                .spinner-border {
                    --si-spinner-animation-speed: 1.5s;
                }
            }
            .align-middle {
                vertical-align: middle !important;
            }

            .d-flex {
                display: flex;
            }

            .flex-column {
                flex-direction: column !important;
            }

            .justify-content-between {
                justify-content: space-between !important;
            }

            .align-items-center {
                align-items: center !important;
            }

            .my-3 {
                margin-top: 1rem !important;
                margin-bottom: 1rem !important;
            }

            .text-center {
                text-align: center !important;
            }

            .text-nowrap {
                white-space: nowrap !important;
            }

            .bg-primary {
                --si-bg-opacity: 1;
                background-color: rgba(var(--si-primary-rgb), var(--si-bg-opacity)) !important;
            }

            .rounded {
                border-radius: var(--si-border-radius) !important;
            }

            @media (min-width: 500px) {
                .flex-sm-row {
                    flex-direction: row !important;
                }

                .my-sm-0 {
                    margin-top: 0 !important;
                    margin-bottom: 0 !important;
                }
            }
            html * {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

            svg {
                max-width: 100%;
            }

            ::selection {
                background: var(--si-user-selection-color);
            }

            h4, .fs-6 {
                line-height: 1.4;
            }

            .list-group {
                --si-list-group-color: #565973;
                --si-list-group-bg: transparent;
                --si-list-group-border-color: #e2e5f1;
                --si-list-group-border-width: 1px;
                --si-list-group-border-radius: 0.5rem;
                --si-list-group-item-padding-x: 1rem;
                --si-list-group-item-padding-y: 0.75rem;
                --si-list-group-action-color: #33354d;
                --si-list-group-action-hover-color: #3b49dc;
                --si-list-group-action-hover-bg: rgba(99, 102, 241, 0.12);
                --si-list-group-action-active-color: #fff;
                --si-list-group-action-active-bg: #3b49dc;
                --si-list-group-disabled-color: #9397ad;
                --si-list-group-disabled-bg: transparent;
                --si-list-group-active-color: #fff;
                --si-list-group-active-bg: #3b49dc;
                --si-list-group-active-border-color: #3b49dc;
                --si-list-group-active-box-shadow: 0 0.5rem 1.125rem -0.5rem rgba(99, 102, 241, 0.9);
                display: flex;
                flex-direction: column;
                padding-left: 0;
                margin-bottom: 0;
                border-radius: var(--si-list-group-border-radius);
            }

            .list-group-item {
                position: relative;
                display: block;
                padding: var(--si-list-group-item-padding-y) var(--si-list-group-item-padding-x);
                color: var(--si-list-group-color);
                text-decoration: none;
                background-color: var(--si-list-group-bg);
                border: var(--si-list-group-border-width) solid var(--si-list-group-border-color);
            }

            .list-group-item:last-child {
                border-bottom-right-radius: inherit;
                border-bottom-left-radius: inherit;
            }

            .list-group-item:disabled {
                color: var(--si-list-group-disabled-color);
                pointer-events: none;
                background-color: var(--si-list-group-disabled-bg);
            }

            .list-group-item + .list-group-item {
                border-top-width: 0;
            }
        }

        @media screen {
            .pl-0 {
                padding-left: 0px;
            }

            .pr-0 {
                padding-right: 0px;
            }

            .cursor-pointer {
                cursor: pointer;
            }
        }

        /*! CSS Used from: Embedded */
        [wire\:loading] {
            display: none;
        }

        /*end loading*/
        .page-loading {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            -webkit-transition: all .4s .2s ease-in-out;
            transition: all .4s .2s ease-in-out;
            background-color: #fff;
            opacity: 0;
            visibility: hidden;
            z-index: 9999;
        }

        .dark-mode .page-loading {
            background-color: #0b0f19;
        }

        .page-loading.active {
            opacity: 1;
            visibility: visible;
        }

        .page-loading-inner {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            text-align: center;
            -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
            -webkit-transition: opacity .2s ease-in-out;
            transition: opacity .2s ease-in-out;
            opacity: 0;
        }

        .page-loading.active > .page-loading-inner {
            opacity: 1;
        }

        .page-loading-inner > span {
            display: block;
            font-size: 1rem;
            font-weight: normal;
            color: #9397ad;
        }

        .dark-mode .page-loading-inner > span {
            color: #fff;
            opacity: .6;
        }

        .page-spinner {
            display: inline-block;
            width: 2.75rem;
            height: 2.75rem;
            margin-bottom: .75rem;
            vertical-align: text-bottom;
            border: .15em solid #b4b7c9;
            border-right-color: transparent;
            border-radius: 50%;
            -webkit-animation: spinner .75s linear infinite;
            animation: spinner .75s linear infinite;
        }

        .dark-mode .page-spinner {
            border-color: rgba(255, 255, 255, .4);
            border-right-color: transparent;
        }

        @-webkit-keyframes spinner {
            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }

        @keyframes spinner {
            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }

        .download-box .list-group {
            padding: 10px;
        }

        .dark-list-style {
            -webkit-backdrop-filter: blur(12px);
            backdrop-filter: blur(12px);
            background: radial-gradient(57.89% 132% at 65.79% -35%, rgba(120, 123, 255, .06) 0%, rgba(120, 123, 255, 0) 100%), linear-gradient(180deg, rgba(255, 255, 255, 0) 54.17%, rgba(255, 255, 255, .04) 100%), rgba(255, 255, 255, .01);
            border-radius: 16px;
            position: relative;
            border: initial;
            margin-bottom: 16px;
        }

        .dark-list-style svg path {
            fill: #fff;
        }

        .fs-sm {
            padding: 7px 16px 4px 16px;
            direction: ltr;
            border-radius: 10000px;
            background-color: rgba(22, 163, 74, .1);
            --tw-text-opacity: 1;
            color: rgb(22 163 74 / var(--tw-text-opacity));
        }

        .dark-list-style svg {
            position: relative;
            right: -4px;
        }

        .\[mask-image\:radial-gradient\(100\%_100\%_at_top_right\2c white\2c transparent\)\] {
            -webkit-mask-image: radial-gradient(100% 100% at top right, #fff, transparent);
            mask-image: radial-gradient(100% 100% at top right, #fff, transparent);
        }

        .-z-10 {
            z-index: -10;
        }

        .inset-0 {
            inset: 0;
        }

        .absolute {
            position: absolute;
        }

        .meeting-title {
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: 700;
            --tw-gradient-to: rgba(229, 231, 235, 1) var(--tw-gradient-to-position);
            --tw-gradient-to: rgba(229, 231, 235, 0.7) var(--tw-gradient-to-position);
            --tw-gradient-stops: var(--tw-gradient-from), #e5e7eb var(--tw-gradient-via-position), var(--tw-gradient-to);
            --tw-gradient-from: rgba(229, 231, 235, 1) var(--tw-gradient-from-position);
            --tw-gradient-to: rgba(229, 231, 235, 0.7) var(--tw-gradient-to-position);
            --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
            background-image: linear-gradient(to right, var(--tw-gradient-stops));
        }

        .right-box {
            background-color: rgb(30 41 59);
            color: rgb(203 213 225);
        }

        .row-reverse {
            flex-direction: row-reverse;
        }

        /*! CSS Used from: Embedded */
        ::selection {
            background: rgba(147, 130, 255, .5);
            color: #fff;
            -webkit-text-fill-color: #fff;
        }

        ::-webkit-scrollbar {
            width: 5px;
        }

        ::-webkit-scrollbar-track {
            background: inherit;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, .05);
            border-radius: 99px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, .1);
        }

        img {
            border: 0;
            display: block;
            -o-object-fit: cover;
            object-fit: cover;
            pointer-events: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            user-select: none;
        }

        button {
            -webkit-appearance: button;
            -moz-appearance: button;
            appearance: button;
            background-color: transparent;
            border: 0;
            cursor: pointer;
            text-transform: none;
        }

        button {
            font-family: inherit;
            font-size: 100%;
            line-height: normal;
            margin: 0;
            vertical-align: baseline;
        }

        button[disabled] {
            cursor: default;
        }

        * {
            box-sizing: border-box;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
        }

        /*! CSS Used from: Embedded */
        .section-header-title-h2 {
            font-weight: 500;
        }

        .section-header-title-h2 {
            font-size: 56px;
            line-height: 64px;
        }

        .section-header-badge, .section-header-description {
            font-feature-settings: "ss01" on, "cv10" on, "calt" off, "liga" off;
        }

        .section-header-description {
            font-size: 16px;
            line-height: 24px;
        }

        .section-header-badge {
            font-size: 14px;
            line-height: 20px;
        }

        .section-header-badge {
            font-weight: 500;
        }

        @media (max-width: 1248px) {
            .section-header-title-h2 {
                font-size: 40px;
                line-height: 48px;
            }
        }

        .section-header {
            position: relative;
            z-index: 3;
        }

        .section-header-badge {
            isolation: isolate;
            overflow: hidden;
            align-items: center;
            -webkit-backdrop-filter: blur(6px);
            backdrop-filter: blur(6px);
            border-radius: 32px;
            box-shadow: inset 0 -7px 11px #a48fff1f;
            display: flex;
            margin: 0 auto 12px;
            padding: 6px 14px 6px 15px;
            position: relative;
            width: -moz-max-content;
            width: max-content;
        }

        .section-header-badge:after {
            background: linear-gradient(90.01deg, rgba(229, 156, 255, .24) .01%, rgba(186, 156, 255, .24) 50.01%, rgba(156, 178, 255, .24) 100%);
            border-radius: inherit;
            content: "";
            inset: 0;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            padding: 1px;
            pointer-events: none;
            position: absolute;
        }

        .section-header-badge-text {
            background: linear-gradient(0deg, rgba(255, 255, 255, .4), rgba(255, 255, 255, .4)), linear-gradient(90.01deg, #e59cff .01%, #ba9cff 50.01%, #9cb2ff 100%);
            background-blend-mode: normal, screen;
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .section-header-title {
            text-align: center;
        }

        .section-header-title span {
            font-weight: 700;
            background: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, .7) 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            -webkit-text-fill-color: transparent;
            display: block;
        }

        .section-header-title-mobile {
            display: none;
        }

        .section-header-description {
            color: #efedfdb3;
            margin: 12px auto 0;
            max-width: 455px;
            text-align: center;
        }

        @media (max-width: 1248px) {
            .section-header br, .section-header-title-desktop {
                display: none;
            }

            .section-header-title-mobile {
                display: block;
            }
        }

        /*! CSS Used from: Embedded */
        .ai {
            padding-bottom: 110px;
            padding-top: 116px;
            position: relative;
        }

        .ai .section-header {
            margin-bottom: 38px;
        }

        .ai .section-header-description {
            max-width: 744px;
        }

        .ai-background {
            margin: -150px auto 0;
            position: relative;
            width: 1440px;
            min-width: 1440px;
        }

        .ai-background-wrapper {
            display: flex;
            justify-content: center;
        }

        .ai-background img {
            height: 100%;
            width: 100%;
        }

        .ai-background .rising-stars {
            height: 300px;
            left: 50%;
            -webkit-mask-image: linear-gradient(to top, #fff, rgba(255, 255, 255, 0));
            mask-image: linear-gradient(to top, #fff, rgba(255, 255, 255, 0));
            -webkit-mask-size: cover;
            mask-size: cover;
            overflow: hidden;
            position: absolute;
            top: -100px;
            transform: translate(-50%);
            width: 282px;
            z-index: 1;
        }

        @media (max-width: 1248px) {
            .ai {
                padding-bottom: 56px;
            }

            .ai:before {
                content: unset;
            }

            .ai .section-header-description {
                max-width: 295px;
            }
        }

        /*! CSS Used from: Embedded */
        .rising-stars {
            opacity: .7;
            pointer-events: none;
        }

        .rising-stars div {
            background: transparent;
            border-radius: 50%;
        }

        .rising-stars div:nth-child(1) {
            animation-duration: 100s !important;
            box-shadow: 1051px 13065px #fff, 1040px 349px #fff, 860px 775px #fff, 904px 1676px #fff, 1017px 399px #fff, 109px 701px #fff, 711px 1634px #fff, 1130px 1271px #fff, 586px 918px #fff, 1190px 1327px #fff, 909px 346px #fff, 1779px 1280px #fff, 367px 1682px #fff, 1253px 1735px #fff, 642px 594px #fff, 985px 1889px #fff, 526px 837px #fff, 285px 212px #fff, 1439px 1273px #fff, 1169px 49px #fff, 1260px 1860px #fff, 1388px 1254px #fff, 692px 676px #fff, 946px 1708px #fff, 1189px 1838px #fff, 869px 741px #fff, 595px 1573px #fff, 1311px 144px #fff, 239px 606px #fff, 1630px 761px #fff, 1291px 1331px #fff, 37px 1082px #fff, 60px 1578px #fff, 834px 44px #fff, 1747px 1258px #fff, 1126px 1131px #fff, 1999px 1557px #fff, 1847px 467px #fff, 1054px 365px #fff, 1204px 520px #fff, 1113px 758px #fff, 824px 70px #fff, 626px 1338px #fff, 954px 1684px #fff, 1977px 164px #fff, 1328px 1929px #fff, 1635px 777px #fff, 1945px 1781px #fff, 1023px 1613px #fff, 1651px 935px #fff, 777px 1713px #fff, 1981px 1922px #fff, 794px 424px #fff, 1903px 1186px #fff, 1356px 1589px #fff, 125px 1017px #fff, 720px 335px #fff, 1851px 522px #fff, 930px 839px #fff, 337px 998px #fff, 37px 485px #fff, 76px 1657px #fff, 1093px 1038px #fff, 423px 242px #fff, 1563px 1299px #fff, 1594px 232px #fff, 959px 1465px #fff, 1891px 1453px #fff, 1447px 441px #fff, 1812px 781px #fff, 753px 365px #fff, 692px 1082px #fff, 49px 584px #fff, 47px 1239px #fff, 327px 1646px #fff, 1510px 50px #fff, 170px 1088px #fff, 835px 668px #fff, 1988px 1974px #fff, 1405px 999px #fff, 156px 1566px #fff, 348px 443px #fff, 932px 990px #fff, 660px 908px #fff, 1482px 9px #fff, 138px 136px #fff, 1795px 1771px #fff, 1270px 1073px #fff, 1293px 1161px #fff, 355px 376px #fff, 425px 827px #fff, 102px 1333px #fff, 330px 923px #fff, 106px 105px #fff, 828px 331px #fff, 354px 1365px #fff, 1959px 1553px #fff, 1407px 354px #fff, 886px 232px #fff, 1900px 23px #fff, 146px 1934px #fff, 412px 101px #fff, 1882px 1101px #fff, 207px 493px #fff, 540px 1825px #fff, 928px 1584px #fff, 843px 562px #fff, 1607px 98px #fff, 705px 447px #fff, 1515px 193px #fff, 556px 1629px #fff, 356px 68px #fff, 1495px 126px #fff, 40px 1500px #fff, 899px 413px #fff, 267px 604px #fff, 110px 1699px #fff, 1982px 1233px #fff, 1185px 531px #fff, 1849px 518px #fff, 1899px 1834px #fff, 1377px 160px #fff, 1792px 1920px #fff, 1806px 244px #fff, 1717px 824px #fff, 1675px 111px #fff, 360px 678px #fff, 880px 1041px #fff, 1290px 1478px #fff, 579px 1178px #fff, 105px 1442px #fff, 1191px 1283px #fff, 113px 936px #fff, 683px 1779px #fff, 1929px 668px #fff, 693px 1789px #fff, 536px 1494px #fff, 1871px 531px #fff, 1763px 71px #fff, 1584px 437px #fff, 648px 771px #fff, 1611px 999px #fff, 185px 1885px #fff, 1573px 1832px #fff, 1972px 403px #fff, 1646px 511px #fff, 720px 1379px #fff, 312px 1999px #fff, 282px 1252px #fff, 7px 1544px #fff, 1810px 1069px #fff, 102px 1484px #fff, 1597px 258px #fff, 1051px 1883px #fff, 1423px 650px #fff, 1583px 1054px #fff, 1923px 1213px #fff, 1717px 103px #fff, 1023px 1284px #fff, 1290px 953px #fff, 1433px 132px #fff, 606px 650px #fff, 179px 1506px #fff, 512px 1538px #fff, 1390px 1822px #fff, 1224px 1975px #fff, 1318px 980px #fff, 1492px 1105px #fff, 1022px 554px #fff, 1155px 355px #fff, 1761px 1091px #fff, 1353px 256px #fff, 228px 1657px #fff, 10px 1985px #fff, 667px 1322px #fff, 158px 773px #fff, 1005px 1737px #fff, 1528px 1231px #fff, 1995px 1301px #fff, 863px 829px #fff, 783px 1578px #fff, 971px 764px #fff, 1388px 1103px #fff, 1153px 1357px #fff, 330px 1854px #fff, 1579px 1928px #fff, 819px 462px #fff, 368px 245px #fff, 1635px 292px #fff, 1523px 1663px #fff, 1230px 907px #fff, 752px 1375px #fff, 849px 519px #fff, 1728px 23px #fff, 1618px 426px #fff, 149px 675px #fff, 248px 1978px #fff, 1949px 638px #fff, 559px 1595px #fff, 1794px 324px #fff, 266px 144px #fff, 10px 1281px #fff, 1627px 1836px #fff, 1536px 1405px #fff, 22px 1842px #fff, 416px 1688px #fff, 1849px 1244px #fff, 1777px 448px #fff, 393px 783px #fff, 1625px 1197px #fff, 630px 33px #fff, 1539px 1856px #fff, 1019px 643px #fff, 1802px 836px #fff, 254px 501px #fff, 573px 616px #fff, 788px 146px #fff, 1469px 577px #fff, 430px 1167px #fff, 672px 903px #fff, 1228px 1760px #fff, 645px 789px #fff, 1557px 1559px #fff, 877px 565px #fff, 563px 1577px #fff, 730px 1436px #fff, 820px 1899px #fff, 1394px 1991px #fff, 1021px 1283px #fff, 84px 190px #fff, 845px 1923px #fff, 884px 1356px #fff, 536px 1634px #fff, 953px 1615px #fff, 688px 1958px #fff, 1800px 78px #fff, 1982px 845px #fff, 564px 70px #fff, 254px 1280px #fff, 1px 337px #fff, 80px 124px #fff, 1197px 1436px #fff, 1102px 488px #fff, 1251px 1860px #fff, 30px 1624px #fff, 1682px 198px #fff, 844px 1318px #fff, 291px 780px #fff, 502px 1902px #fff, 522px 1150px #fff, 1889px 942px #fff, 1034px 1903px #fff, 865px 142px #fff, 1934px 1335px #fff, 624px 982px #fff, 298px 1547px #fff, 1916px 250px #fff, 365px 1597px #fff, 1400px 220px #fff, 1949px 1320px #fff, 1573px 1479px #fff, 572px 364px #fff, 440px 1423px #fff, 1563px 72px #fff, 708px 869px #fff, 861px 679px #fff, 1033px 578px #fff, 1294px 79px #fff, 578px 1399px #fff, 700px 765px #fff, 1395px 539px #fff, 1901px 892px #fff, 1203px 732px #fff, 201px 1175px #fff, 422px 165px #fff, 1754px 1315px #fff, 128px 284px #fff, 623px 1480px #fff, 459px 41px #fff, 584px 1232px #fff, 522px 291px #fff, 1684px 1815px #fff, 1478px 968px #fff, 450px 1523px #fff, 1187px 551px #fff, 336px 1519px #fff, 1230px 826px #fff, 1782px 1937px #fff, 67px 1035px #fff, 1655px 76px #fff, 1600px 1808px #fff, 1857px 1827px #fff, 591px 884px #fff, 646px 642px #fff, 142px 1440px #fff, 579px 988px #fff, 152px 1210px #fff, 736px 228px #fff, 1096px 315px #fff, 1931px 1160px #fff, 1556px 1464px #fff, 901px 406px #fff, 616px 1303px #fff, 782px 91px #fff, 1395px 491px #fff, 1799px 886px #fff, 1279px 354px #fff, 977px 1488px #fff, 532px 1528px #fff, 1854px 1005px #fff, 349px 587px #fff, 657px 702px #fff, 1174px 1955px #fff, 1576px 717px #fff, 347px 440px #fff, 1360px 1083px #fff, 740px 1675px #fff, 535px 1670px #fff, 1612px 16px #fff, 1263px 389px #fff, 536px 620px #fff, 27px 1224px #fff, 1636px 60px #fff, 1318px 1034px #fff, 368px 1154px #fff, 1697px 65px #fff, 1357px 501px #fff, 936px 376px #fff, 1643px 643px #fff, 1805px 1633px #fff, 1654px 995px #fff, 1573px 57px #fff, 618px 1150px #fff, 217px 337px #fff, 1691px 1225px #fff, 1889px 259px #fff, 38px 1052px #fff, 908px 190px #fff, 712px 1539px #fff, 1577px 712px #fff, 683px 315px #fff, 841px 836px #fff, 440px 89px #fff, 546px 670px #fff, 1423px 1103px #fff, 1838px 561px #fff, 949px 1246px #fff, 1972px 652px #fff, 1803px 805px #fff, 1675px 412px #fff, 1607px 1392px #fff, 812px 1737px #fff, 277px 1234px #fff, 224px 1002px #fff, 343px 438px #fff, 980px 54px #fff, 1034px 594px #fff, 1364px 1196px #fff, 121px 710px #fff, 262px 163px #fff, 1046px 147px #fff, 151px 467px #fff, 1075px 908px #fff, 12px 1785px #fff, 900px 806px #fff, 296px 778px #fff, 67px 1505px #fff, 403px 1825px #fff, 251px 23px #fff, 1648px 951px #fff, 852px 1818px #fff, 463px 505px #fff, 1722px 1608px #fff, 1309px 760px #fff, 725px 162px #fff, 399px 1127px #fff, 100px 1088px #fff, 675px 1526px #fff, 286px 1739px #fff, 1377px 867px #fff, 528px 514px #fff, 1103px 1681px #fff, 461px 1248px #fff, 2000px 1545px #fff, 789px 1378px #fff, 342px 1022px #fff, 26px 1452px #fff, 848px 1378px #fff, 824px 1734px #fff, 1664px 355px #fff, 1343px 466px #fff, 1397px 377px #fff, 1282px 247px #fff, 1634px 1610px #fff, 829px 1443px #fff, 479px 1773px #fff, 1500px 113px #fff, 1894px 358px #fff, 992px 482px #fff, 1660px 1008px #fff, 1990px 1906px #fff, 1914px 1130px #fff, 780px 963px #fff, 1007px 531px #fff, 122px 1903px #fff, 1577px 1500px #fff, 1161px 690px #fff, 1581px 116px #fff, 1038px 121px #fff, 210px 1620px #fff, 1562px 546px #fff, 865px 1564px #fff, 1973px 91px #fff, 1414px 108px #fff, 177px 1094px #fff, 806px 1732px #fff, 248px 768px #fff, 676px 92px #fff, 1588px 394px #fff, 700px 363px #fff, 1392px 700px #fff, 1660px 809px #fff, 1569px 1119px #fff, 303px 1784px #fff, 156px 958px #fff, 367px 1899px #fff, 1908px 529px #fff, 1838px 1136px #fff, 200px 1465px #fff, 1151px 1271px #fff, 272px 1042px #fff, 1876px 1557px #fff, 1842px 312px #fff, 837px 47px #fff, 1132px 1641px #fff, 1907px 806px #fff, 1540px 29px #fff, 1594px 1948px #fff, 1864px 525px #fff, 432px 582px #fff, 1990px 1569px #fff, 1748px 1278px #fff, 996px 1567px #fff, 1318px 827px #fff, 456px 1149px #fff, 581px 997px #fff, 1265px 214px #fff, 697px 1174px #fff, 1467px 1087px #fff, 1694px 1528px #fff, 1534px 773px #fff, 1342px 1295px #fff, 813px 1291px #fff, 346px 1875px #fff, 626px 535px #fff, 1168px 955px #fff, 1159px 352px #fff, 1627px 288px #fff, 899px 446px #fff, 919px 105px #fff, 699px 619px #fff, 1077px 620px #fff, 1523px 1184px #fff, 330px 1387px #fff, 718px 1776px #fff, 853px 982px #fff, 1910px 375px #fff, 1950px 104px #fff, 478px 336px #fff, 1443px 820px #fff, 1386px 911px #fff, 1085px 465px #fff, 1617px 1421px #fff, 1890px 443px #fff, 1683px 1735px #fff, 1451px 1878px #fff, 1093px 638px #fff, 411px 627px #fff, 1903px 1064px #fff, 806px 211px #fff, 1495px 1863px #fff, 140px 1842px #fff, 1857px 1279px #fff, 915px 1607px #fff, 872px 1343px #fff, 342px 317px #fff, 227px 1763px #fff, 1223px 1473px #fff, 1015px 600px #fff, 1912px 1075px #fff, 629px 628px #fff, 1352px 89px #fff, 817px 291px #fff, 1694px 1555px #fff, 1244px 13px #fff, 930px 1348px #fff, 501px 576px #fff, 1720px 589px #fff, 1718px 236px #fff, 1620px 667px #fff;
        }

        .rising-stars div:nth-child(2) {
            animation-duration: 150s !important;
            box-shadow: 386px 4589px #fff, 1341px 676px #fff, 1780px 132px #fff, 523px 970px #fff, 1010px 341px #fff, 250px 1333px #fff, 1715px 1586px #fff, 1899px 1513px #fff, 935px 550px #fff, 728px 969px #fff, 640px 1599px #fff, 1439px 541px #fff, 582px 822px #fff, 1532px 313px #fff, 184px 667px #fff, 694px 143px #fff, 555px 990px #fff, 1021px 141px #fff, 237px 1828px #fff, 1468px 1647px #fff, 1068px 491px #fff, 1998px 93px #fff, 1285px 921px #fff, 1437px 1434px #fff, 28px 1741px #fff, 1557px 1303px #fff, 193px 18px #fff, 26px 75px #fff, 489px 342px #fff, 1488px 1721px #fff, 1213px 1217px #fff, 765px 1926px #fff, 1760px 1008px #fff, 155px 140px #fff, 1394px 74px #fff, 279px 677px #fff, 874px 622px #fff, 1761px 729px #fff, 64px 1339px #fff, 1048px 1740px #fff, 414px 394px #fff, 558px 1280px #fff, 149px 321px #fff, 1610px 45px #fff, 1979px 63px #fff, 1681px 1617px #fff, 1934px 1192px #fff, 1409px 800px #fff, 1300px 12px #fff, 341px 758px #fff, 1892px 1792px #fff, 1716px 615px #fff, 1430px 1500px #fff, 708px 315px #fff, 1128px 683px #fff, 394px 78px #fff, 591px 1457px #fff, 717px 1533px #fff, 549px 538px #fff, 140px 1655px #fff, 811px 229px #fff, 1952px 210px #fff, 1538px 1411px #fff, 305px 1040px #fff, 1446px 1084px #fff, 1751px 923px #fff, 366px 1287px #fff, 368px 1376px #fff, 1340px 1859px #fff, 1638px 1529px #fff, 536px 301px #fff, 352px 885px #fff, 395px 321px #fff, 646px 941px #fff, 895px 215px #fff, 1284px 1779px #fff, 908px 1281px #fff, 394px 124px #fff, 1776px 976px #fff, 1766px 1378px #fff, 1793px 419px #fff, 879px 644px #fff, 1077px 1512px #fff, 658px 366px #fff, 1573px 1002px #fff, 1768px 110px #fff, 1159px 1497px #fff, 194px 1900px #fff, 1459px 1966px #fff, 1175px 344px #fff, 1449px 1931px #fff, 1103px 1234px #fff, 1592px 1860px #fff, 377px 1250px #fff, 1029px 1805px #fff, 1002px 1766px #fff, 783px 534px #fff, 1188px 580px #fff, 1575px 490px #fff, 995px 1382px #fff, 1020px 1377px #fff, 193px 174px #fff, 945px 911px #fff, 621px 571px #fff, 1976px 408px #fff, 277px 213px #fff, 1585px 460px #fff, 1905px 1557px #fff, 1289px 35px #fff, 1667px 1107px #fff, 403px 1252px #fff, 1527px 1710px #fff, 1651px 242px #fff, 807px 1253px #fff, 1350px 1497px #fff, 504px 1796px #fff, 974px 1832px #fff, 1982px 918px #fff, 93px 1615px #fff, 1421px 855px #fff, 1055px 154px #fff, 331px 1685px #fff, 1345px 677px #fff, 124px 1490px #fff, 1111px 1427px #fff, 37px 1236px #fff, 1295px 1443px #fff, 1939px 792px #fff, 821px 1342px #fff, 1854px 1578px #fff, 1449px 1497px #fff, 765px 1685px #fff, 1564px 451px #fff, 785px 967px #fff, 1122px 275px #fff, 1781px 1930px #fff, 1645px 215px #fff, 496px 1116px #fff, 423px 458px #fff, 1576px 85px #fff, 1877px 1956px #fff, 1621px 1296px #fff, 1577px 631px #fff, 1240px 1648px #fff, 1868px 932px #fff, 761px 1882px #fff, 1527px 1707px #fff, 186px 1965px #fff, 1572px 1618px #fff, 429px 1156px #fff, 1950px 1495px #fff, 449px 1965px #fff, 1995px 1408px #fff, 160px 150px #fff, 1485px 1539px #fff, 582px 635px #fff, 327px 812px #fff, 1902px 1762px #fff, 1911px 782px #fff, 599px 315px #fff, 300px 85px #fff, 44px 1485px #fff, 796px 1023px #fff, 977px 1919px #fff, 740px 650px #fff, 578px 1348px #fff, 615px 1718px #fff, 1372px 510px #fff, 57px 364px #fff, 1091px 887px #fff, 693px 1098px #fff, 1766px 1313px #fff, 1721px 144px #fff, 1509px 706px #fff, 1076px 155px #fff, 1990px 1768px #fff, 109px 1416px #fff, 1607px 1192px #fff, 957px 1574px #fff, 567px 1193px #fff, 802px 537px #fff, 142px 1267px #fff, 1151px 1800px #fff, 953px 709px #fff, 1120px 1001px #fff, 1722px 877px #fff, 861px 1772px #fff, 1475px 551px #fff, 1417px 1695px #fff, 1066px 1105px #fff, 332px 621px #fff, 386px 860px #fff, 1436px 654px #fff, 1213px 1862px #fff, 282px 1661px #fff, 1179px 1797px #fff, 876px 949px #fff, 1880px 94px #fff, 1197px 1894px #fff, 1491px 1046px #fff, 1758px 679px #fff, 974px 1658px #fff, 1325px 623px #fff, 378px 97px #fff, 306px 523px #fff, 1975px 1402px #fff, 1273px 565px #fff, 1653px 853px #fff, 842px 1880px #fff, 251px 1552px #fff, 327px 900px #fff, 948px 1887px #fff, 1031px 1080px #fff, 476px 1042px #fff, 46px 1709px #fff, 477px 1438px #fff, 399px 62px #fff, 500px 1017px #fff, 133px 1896px #fff, 929px 1297px #fff, 754px 1110px #fff, 1640px 644px #fff, 1282px 1195px #fff, 1888px 990px #fff, 1576px 1876px #fff, 1902px 1136px #fff, 906px 718px #fff, 586px 1265px #fff, 253px 1817px #fff, 547px 835px #fff, 1135px 1568px #fff, 996px 637px #fff, 286px 581px #fff, 228px 765px #fff, 279px 1626px #fff, 1215px 389px #fff, 1815px 264px #fff, 270px 539px #fff, 247px 719px #fff, 995px 848px #fff, 889px 1941px #fff, 1963px 1935px #fff, 476px 1066px #fff, 124px 141px #fff, 1847px 1000px #fff, 430px 156px #fff, 1527px 142px #fff, 1181px 1957px #fff, 950px 1443px #fff, 1433px 1001px #fff, 471px 185px #fff, 567px 471px #fff, 260px 141px #fff, 966px 1949px #fff, 1820px 474px #fff, 900px 363px #fff, 240px 179px #fff, 1439px 1928px #fff, 664px 93px #fff, 456px 545px #fff, 163px 1413px #fff, 1643px 1111px #fff, 1807px 1456px #fff, 66px 1005px #fff, 1410px 1646px #fff, 1206px 1685px #fff, 537px 917px #fff, 313px 1458px #fff, 411px 555px #fff, 1851px 253px #fff, 562px 390px #fff, 1750px 1390px #fff, 3px 233px #fff, 1025px 433px #fff, 1735px 1901px #fff, 1147px 1639px #fff, 335px 21px #fff, 472px 204px #fff, 1428px 114px #fff, 673px 1862px #fff, 526px 1306px #fff, 1877px 353px #fff, 1563px 1645px #fff, 1043px 454px #fff, 1498px 1912px #fff, 1909px 1918px #fff, 1258px 161px #fff, 373px 606px #fff, 563px 1026px #fff, 473px 390px #fff, 838px 340px #fff, 1898px 1856px #fff, 1278px 908px #fff, 251px 1502px #fff, 555px 3px #fff, 1378px 1303px #fff, 1109px 1644px #fff, 1361px 662px #fff, 1654px 1797px #fff, 1209px 640px #fff, 1974px 1809px #fff, 1422px 279px #fff, 9px 1660px #fff, 211px 222px #fff, 651px 993px #fff, 978px 730px #fff, 282px 351px #fff, 1122px 251px #fff, 151px 1053px #fff, 1344px 385px #fff, 1288px 8px #fff, 677px 1711px #fff, 1352px 138px #fff, 113px 285px #fff, 1509px 426px #fff, 26px 1912px #fff, 1894px 514px #fff, 817px 175px #fff, 1581px 1341px #fff, 1424px 512px #fff, 1627px 1605px #fff, 351px 539px #fff, 1807px 268px #fff, 323px 788px #fff, 53px 1187px #fff, 776px 908px #fff, 870px 793px #fff, 924px 714px #fff, 409px 1366px #fff, 1232px 811px #fff, 1098px 1662px #fff, 704px 1965px #fff, 1008px 906px #fff, 1956px 1076px #fff, 1076px 1588px #fff, 1807px 863px #fff, 838px 729px #fff, 1081px 1907px #fff, 66px 917px #fff, 34px 1538px #fff, 1509px 179px #fff, 764px 1091px #fff, 822px 823px #fff, 1766px 1277px #fff, 1763px 1637px #fff, 873px 1394px #fff, 298px 1366px #fff, 930px 679px #fff, 382px 1694px #fff, 745px 492px #fff, 972px 1032px #fff, 1284px 1854px #fff, 657px 1509px #fff, 1641px 1278px #fff, 1197px 1553px #fff, 560px 1370px #fff, 1304px 224px #fff, 1353px 1828px #fff, 1090px 9px #fff, 1704px 250px #fff, 361px 609px #fff, 1797px 768px #fff, 1804px 1060px #fff, 114px 1835px #fff, 1646px 649px #fff, 540px 765px #fff, 847px 1262px #fff, 39px 98px #fff, 1817px 838px #fff, 1691px 698px #fff, 15px 1546px #fff, 690px 1349px #fff, 934px 89px #fff, 809px 502px #fff, 1474px 1375px #fff, 1007px 1356px #fff, 471px 975px #fff, 99px 1223px #fff, 896px 500px #fff, 1030px 1733px #fff, 328px 59px #fff, 9px 1507px #fff, 792px 1445px #fff, 1625px 939px #fff, 961px 334px #fff, 1064px 788px #fff, 1560px 1117px #fff, 1568px 1851px #fff, 178px 745px #fff, 1199px 1225px #fff, 754px 1417px #fff, 838px 354px #fff, 316px 1067px #fff, 183px 1302px #fff, 1713px 1587px #fff, 1041px 57px #fff, 496px 1623px #fff, 213px 1257px #fff, 51px 154px #fff, 379px 898px #fff, 961px 696px #fff, 1881px 802px #fff, 1332px 952px #fff, 706px 1847px #fff, 639px 12px #fff, 1112px 1505px #fff, 868px 1053px #fff, 949px 1313px #fff, 559px 870px #fff, 129px 184px #fff, 951px 1138px #fff, 514px 850px #fff, 739px 195px #fff, 818px 397px #fff, 13px 810px #fff, 1934px 884px #fff, 1634px 912px #fff, 1047px 1449px #fff, 421px 120px #fff, 408px 865px #fff, 1361px 982px #fff, 975px 1686px #fff, 1927px 583px #fff, 1125px 1281px #fff, 654px 432px #fff, 996px 301px #fff, 1327px 1504px #fff, 1048px 1152px #fff, 519px 1253px #fff, 1084px 48px #fff, 1583px 1938px #fff, 893px 1456px #fff, 892px 1244px #fff, 902px 133px #fff, 710px 330px #fff, 656px 1834px #fff, 631px 128px #fff, 1216px 861px #fff, 196px 286px #fff, 1747px 39px #fff, 1576px 643px #fff, 1766px 516px #fff, 117px 408px #fff, 1550px 1744px #fff, 1981px 1282px #fff, 602px 1444px #fff, 267px 1940px #fff, 288px 232px #fff, 449px 199px #fff, 658px 875px #fff, 1913px 377px #fff, 428px 1556px #fff, 520px 627px #fff, 1454px 1255px #fff, 555px 1890px #fff, 1817px 1089px #fff, 665px 834px #fff, 1003px 701px #fff, 1895px 1859px #fff, 1420px 1976px #fff, 264px 1984px #fff, 831px 1809px #fff, 269px 774px #fff, 1168px 905px #fff, 976px 1976px #fff, 114px 1377px #fff, 159px 1171px #fff, 1079px 840px #fff, 362px 1925px #fff, 1072px 1631px #fff, 1937px 61px #fff, 1872px 1316px #fff, 32px 525px #fff, 1705px 1284px #fff, 1024px 1231px #fff, 903px 1602px #fff, 1605px 1636px #fff, 1854px 1375px #fff, 1581px 1529px #fff, 818px 903px #fff, 1074px 672px #fff, 730px 1020px #fff, 162px 762px #fff, 1966px 1157px #fff, 909px 1349px #fff, 423px 1101px #fff, 1417px 1150px #fff, 1500px 1618px #fff, 1932px 354px #fff, 58px 791px #fff, 864px 545px #fff, 772px 200px #fff, 613px 1190px #fff, 1828px 329px #fff, 1181px 1332px #fff, 1013px 1960px #fff, 1972px 1576px #fff, 495px 1347px #fff, 223px 235px #fff, 1684px 1352px #fff;
            opacity: .5;
        }

        .rising-stars div:nth-child(3) {
            animation-duration: 200s !important;
            box-shadow: 998px 10055px #fff, 1738px 925px #fff, 1783px 1839px #fff, 1539px 911px #fff, 1024px 355px #fff, 1037px 1250px #fff, 1905px 809px #fff, 1716px 95px #fff, 1970px 1238px #fff, 29px 644px #fff, 434px 1321px #fff, 453px 1341px #fff, 206px 830px #fff, 1256px 1509px #fff, 1649px 1344px #fff, 476px 115px #fff, 1892px 957px #fff, 1853px 1673px #fff, 487px 795px #fff, 1522px 628px #fff, 1374px 1161px #fff, 1px 1166px #fff, 775px 1094px #fff, 667px 398px #fff, 1243px 805px #fff, 863px 1116px #fff, 1208px 665px #fff, 1940px 1494px #fff, 1061px 496px #fff, 1724px 657px #fff, 95px 803px #fff, 1706px 892px #fff, 1571px 1448px #fff, 1965px 87px #fff, 30px 705px #fff, 465px 508px #fff, 432px 1136px #fff, 73px 1996px #fff, 1202px 467px #fff, 1901px 1007px #fff, 1910px 136px #fff, 1400px 952px #fff, 102px 1555px #fff, 1847px 1075px #fff, 455px 1932px #fff, 1462px 1532px #fff, 43px 1483px #fff, 1399px 1246px #fff, 1245px 354px #fff, 1592px 747px #fff, 1064px 1833px #fff, 789px 697px #fff, 1781px 871px #fff, 728px 272px #fff, 1198px 185px #fff, 959px 798px #fff, 1218px 1369px #fff, 1091px 140px #fff, 1709px 1171px #fff, 1852px 438px #fff, 1180px 1891px #fff, 227px 1256px #fff, 856px 1543px #fff, 1623px 1802px #fff, 1974px 707px #fff, 1117px 1150px #fff, 1045px 1047px #fff, 681px 1880px #fff, 1747px 1362px #fff, 1260px 965px #fff, 1729px 1508px #fff, 669px 1178px #fff, 96px 918px #fff, 1160px 225px #fff, 1491px 588px #fff, 589px 1029px #fff, 974px 1416px #fff, 555px 1386px #fff, 55px 114px #fff, 280px 1115px #fff, 310px 1395px #fff, 390px 888px #fff, 861px 950px #fff, 1699px 542px #fff, 199px 1652px #fff, 654px 77px #fff, 1101px 1435px #fff, 1097px 331px #fff, 1934px 1969px #fff, 146px 717px #fff, 505px 531px #fff, 1194px 126px #fff, 1536px 1756px #fff, 85px 1957px #fff, 1992px 1055px #fff, 1339px 146px #fff, 768px 1073px #fff, 249px 1032px #fff, 323px 562px #fff, 1849px 1110px #fff, 1004px 842px #fff, 683px 16px #fff, 1654px 791px #fff, 1717px 700px #fff, 1147px 188px #fff, 1111px 690px #fff, 334px 1205px #fff, 590px 973px #fff, 1503px 1905px #fff, 332px 1098px #fff, 181px 796px #fff, 1302px 356px #fff, 1774px 1650px #fff, 1599px 1615px #fff, 387px 1993px #fff, 54px 1017px #fff, 671px 1870px #fff, 1473px 1023px #fff, 1569px 641px #fff, 1333px 1092px #fff, 985px 140px #fff, 1205px 166px #fff, 979px 877px #fff, 1421px 891px #fff, 16px 698px #fff, 1442px 1403px #fff, 1883px 672px #fff, 1125px 783px #fff, 455px 1864px #fff, 1757px 1712px #fff, 1778px 926px #fff, 538px 1203px #fff, 1399px 169px #fff, 404px 118px #fff, 1423px 917px #fff, 921px 286px #fff, 1501px 416px #fff, 555px 994px #fff, 781px 1585px #fff, 1758px 811px #fff, 1335px 1484px #fff, 1790px 1799px #fff, 1002px 954px #fff, 195px 1015px #fff, 1687px 1528px #fff, 902px 974px #fff, 1932px 1748px #fff, 695px 1310px #fff, 405px 640px #fff, 792px 1538px #fff, 1681px 1299px #fff, 1084px 1334px #fff, 950px 1156px #fff, 360px 1798px #fff, 1779px 1419px #fff, 1227px 1657px #fff, 6px 801px #fff, 1342px 1142px #fff, 678px 1700px #fff, 537px 998px #fff, 866px 1957px #fff, 805px 1487px #fff, 832px 546px #fff, 1146px 967px #fff, 1281px 1401px #fff, 1774px 105px #fff, 1777px 1827px #fff, 232px 170px #fff, 403px 828px #fff, 1869px 1338px #fff, 1540px 920px #fff, 865px 1261px #fff, 1971px 1512px #fff, 1493px 1855px #fff, 1958px 224px #fff, 1726px 520px #fff, 766px 1721px #fff, 1843px 1915px #fff, 312px 847px #fff, 1334px 1990px #fff, 897px 1477px #fff, 1439px 1527px #fff, 1007px 850px #fff, 433px 1005px #fff, 1662px 289px #fff, 1058px 465px #fff, 797px 410px #fff, 1490px 477px #fff, 1770px 1187px #fff, 1065px 1254px #fff, 1997px 701px #fff, 255px 322px #fff, 113px 1414px #fff, 939px 1243px #fff, 571px 549px #fff, 1041px 503px #fff, 229px 1829px #fff, 765px 598px #fff, 1266px 1295px #fff, 1245px 697px #fff, 855px 132px #fff, 1339px 58px #fff, 1860px 1199px #fff, 1010px 1526px #fff, 1266px 1540px #fff, 310px 305px #fff, 148px 264px #fff, 469px 1994px #fff, 1557px 1210px #fff, 1671px 913px #fff, 1491px 1997px #fff, 606px 1286px #fff, 678px 1454px #fff, 1362px 1267px #fff, 152px 383px #fff, 531px 1698px #fff, 740px 849px #fff, 796px 279px #fff, 341px 1619px #fff, 32px 1674px #fff, 655px 383px #fff, 400px 1188px #fff, 1806px 944px #fff, 142px 111px #fff, 993px 422px #fff, 523px 1784px #fff, 1270px 1090px #fff, 1443px 1995px #fff, 268px 1110px #fff, 369px 771px #fff, 689px 590px #fff, 1054px 1091px #fff, 1703px 159px #fff, 1537px 637px #fff, 805px 725px #fff, 817px 1796px #fff, 879px 1507px #fff, 1153px 1844px #fff, 1552px 1194px #fff, 427px 894px #fff, 538px 836px #fff, 221px 823px #fff, 1858px 5px #fff, 189px 585px #fff, 1418px 1320px #fff, 1191px 1126px #fff, 393px 1201px #fff, 743px 340px #fff, 1799px 11px #fff, 1225px 1075px #fff, 277px 922px #fff, 512px 1952px #fff, 1777px 443px #fff, 1683px 1570px #fff, 1312px 1534px #fff, 1544px 346px #fff, 786px 1160px #fff, 653px 1943px #fff, 662px 908px #fff, 1289px 180px #fff, 552px 569px #fff, 404px 1838px #fff, 32px 491px #fff, 1667px 1421px #fff, 1077px 251px #fff, 330px 1755px #fff, 232px 1519px #fff, 811px 761px #fff, 904px 954px #fff, 1718px 116px #fff, 962px 143px #fff, 941px 536px #fff, 1608px 164px #fff, 1845px 1950px #fff, 653px 1882px #fff, 752px 588px #fff, 825px 871px #fff, 89px 635px #fff, 69px 945px #fff, 1205px 1432px #fff, 1173px 1088px #fff, 296px 514px #fff, 1261px 1198px #fff, 274px 846px #fff, 442px 1412px #fff, 764px 1281px #fff, 502px 1844px #fff, 313px 1330px #fff, 1515px 754px #fff, 1412px 1323px #fff, 1361px 787px #fff, 1542px 1976px #fff, 1018px 1752px #fff, 1746px 465px #fff, 421px 475px #fff, 697px 217px #fff, 692px 263px #fff, 1820px 1402px #fff, 755px 1480px #fff, 606px 1851px #fff, 13px 314px #fff, 1067px 1182px #fff, 1496px 16px #fff, 1580px 1667px #fff, 329px 1172px #fff, 259px 1148px #fff, 1728px 946px #fff, 1437px 453px #fff, 1161px 624px #fff, 609px 1000px #fff, 471px 178px #fff, 980px 226px #fff, 698px 326px #fff, 1635px 333px #fff, 514px 1799px #fff, 674px 187px #fff, 1152px 901px #fff, 1084px 1360px #fff, 162px 756px #fff, 428px 72px #fff, 841px 703px #fff, 890px 136px #fff, 970px 206px #fff, 34px 734px #fff, 1288px 1476px #fff, 1609px 1064px #fff, 1532px 331px #fff, 595px 415px #fff, 1818px 765px #fff, 714px 931px #fff, 1201px 862px #fff, 942px 715px #fff, 1810px 789px #fff, 1669px 1931px #fff, 1358px 1199px #fff, 1801px 915px #fff, 1892px 1781px #fff, 1811px 393px #fff, 212px 1386px #fff, 326px 134px #fff, 1516px 519px #fff, 143px 1428px #fff, 477px 903px #fff, 1588px 1257px #fff, 236px 1572px #fff, 1565px 1397px #fff, 1098px 1992px #fff, 599px 1130px #fff, 1689px 250px #fff, 706px 977px #fff, 1313px 744px #fff, 303px 1649px #fff, 1518px 1869px #fff, 1884px 24px #fff, 1887px 1009px #fff, 150px 1986px #fff, 537px 559px #fff, 55px 1453px #fff, 1531px 1279px #fff, 201px 796px #fff, 934px 372px #fff, 1331px 116px #fff, 1651px 22px #fff, 12px 1766px #fff, 1735px 1740px #fff, 786px 576px #fff, 1341px 530px #fff, 975px 1871px #fff, 1252px 595px #fff, 14px 107px #fff, 1973px 1754px #fff, 1766px 53px #fff, 1756px 809px #fff, 1898px 910px #fff, 860px 1748px #fff, 791px 270px #fff, 1921px 1527px #fff, 1527px 1694px #fff, 1734px 1966px #fff, 1080px 67px #fff, 182px 578px #fff, 462px 929px #fff, 1523px 1495px #fff, 1586px 1081px #fff, 1435px 1904px #fff, 1396px 1048px #fff, 1256px 173px #fff, 1462px 101px #fff, 1354px 729px #fff, 683px 221px #fff, 1077px 1711px #fff, 1291px 1647px #fff, 1333px 340px #fff, 1368px 1122px #fff, 185px 1885px #fff, 1792px 711px #fff, 429px 744px #fff, 1313px 816px #fff, 906px 1363px #fff, 770px 1946px #fff, 479px 1296px #fff, 1508px 1113px #fff, 1212px 822px #fff, 1813px 1182px #fff, 783px 1800px #fff, 1041px 1522px #fff, 157px 863px #fff, 1666px 439px #fff, 219px 1994px #fff, 1446px 1671px #fff, 1267px 1275px #fff, 132px 406px #fff, 1545px 1696px #fff, 1646px 298px #fff, 828px 856px #fff, 337px 1763px #fff, 1715px 1783px #fff, 890px 308px #fff, 452px 725px #fff, 749px 38px #fff, 1431px 1165px #fff, 807px 1867px #fff, 1221px 511px #fff, 998px 101px #fff, 1587px 1670px #fff, 762px 1352px #fff, 834px 1256px #fff, 32px 1577px #fff, 592px 1221px #fff, 520px 1316px #fff, 1317px 102px #fff, 783px 1591px #fff, 246px 1855px #fff, 677px 714px #fff, 559px 1314px #fff, 1205px 1970px #fff, 1384px 1836px #fff, 954px 1987px #fff, 325px 1046px #fff, 1888px 1041px #fff, 764px 1644px #fff, 313px 1223px #fff, 31px 1978px #fff, 1442px 1395px #fff, 1406px 423px #fff, 1016px 1178px #fff, 1238px 1146px #fff, 926px 1748px #fff, 1869px 1138px #fff, 1395px 1006px #fff, 819px 1150px #fff, 1837px 956px #fff, 768px 1933px #fff, 1016px 190px #fff, 496px 197px #fff, 1351px 639px #fff, 79px 90px #fff, 945px 706px #fff, 1575px 1440px #fff, 1363px 1361px #fff, 1529px 214px #fff, 1775px 996px #fff, 1083px 1560px #fff, 1413px 1289px #fff, 1866px 1376px #fff, 1062px 987px #fff, 296px 1662px #fff, 62px 68px #fff, 207px 936px #fff, 1638px 132px #fff, 1098px 1946px #fff, 1589px 478px #fff, 974px 755px #fff, 443px 518px #fff, 1389px 164px #fff, 1036px 772px #fff, 617px 776px #fff, 1829px 549px #fff, 1024px 45px #fff, 1818px 1364px #fff, 465px 998px #fff, 1561px 58px #fff, 1251px 673px #fff, 535px 488px #fff, 844px 1684px #fff, 1437px 1805px #fff, 1424px 1025px #fff, 1443px 1563px #fff, 1618px 567px #fff, 1400px 700px #fff, 20px 103px #fff, 1926px 770px #fff, 1033px 1169px #fff, 536px 1878px #fff, 1239px 377px #fff, 1583px 1437px #fff, 1760px 23px #fff, 126px 1872px #fff, 126px 189px #fff, 1947px 487px #fff;
            opacity: .3;
        }

        /*! CSS Used from: Embedded */
        .lazy-image {
            display: block;
            transition: 1s cubic-bezier(.6, .6, 0, 1) opacity;
        }

        .ai-background-wrapper {
            overflow: hidden;
            padding-top: 190px;
            margin-top: -231px;
        }

        /*! CSS Used from: Embedded */
        .ai-showcase {
            background: rgba(255, 255, 255, .01);
            border: 1px solid rgba(255, 255, 255, .1);
            border-radius: 24px;
            box-shadow: inset 0 0 0 8px #ffffff08;
            left: 50%;
            padding: 8px;
            position: absolute;
            z-index: 2;
            top: 256px;
            transform: translate(-50%);
            transition: .45s cubic-bezier(.6, .6, 0, 1) background-color, .45s cubic-bezier(.6, .6, 0, 1) border-color;
        }

        .ai-showcase:hover {
            background: rgba(255, 255, 255, .05);
        }

        .ai-showcase:hover, .ai-showcase:hover .ai-showcase-inner {
            border-color: #ffffff29;
        }

        .ai-showcase-text a {
            cursor: pointer;
        }

        .ai-showcase-text a:hover {
            color: #e9e9e9;
        }

        .ai-showcase-inner {
            -webkit-backdrop-filter: blur(15px);
            backdrop-filter: blur(15px);
            background: radial-gradient(71.86% 50% at 50% 0%, rgba(168, 127, 255, .04) 0%, rgba(168, 127, 255, 0) 100%), rgba(4, 1, 21, .5);
            border: 1px solid rgba(255, 255, 255, .1);
            border-radius: 16px;
            height: 100%;
            padding: 24px 32px;
            position: relative;
            transition: .45s cubic-bezier(.6, .6, 0, 1) border-color;
            width: 560px;
        }

        .ai-showcase-inner-starlight {
            height: 8px;
            -webkit-mask-image: linear-gradient(to right, rgba(217, 217, 217, 0) 0%, #d9d9d9 25%, #d9d9d9 75%, rgba(217, 217, 217, 0) 100%);
            mask-image: linear-gradient(to right, rgba(217, 217, 217, 0) 0%, #d9d9d9 25%, #d9d9d9 75%, rgba(217, 217, 217, 0) 100%);
            opacity: .5;
            position: absolute;
            width: 180px;
        }

        .ai-showcase-inner-starlight:before {
            animation: 5s cubic-bezier(.6, .6, 0, 1) infinite;
            background: linear-gradient(to right, rgba(201, 177, 255, 0), #c9b1ff, rgba(201, 177, 255, 0));
            content: "";
            display: block;
            height: 1px;
            width: 100%;
        }

        .ai-showcase-inner-starlight:nth-child(1) {
            right: 56px;
            top: -1px;
        }

        .ai-showcase-inner-starlight:nth-child(1):before {
            animation-name: aiShowcaseStarlight1;
        }

        .ai-showcase-inner-starlight:nth-child(2) {
            bottom: -8px;
            left: 56px;
        }

        .ai-showcase-inner-starlight:nth-child(2):before {
            animation-name: aiShowcaseStarlight2;
        }

        .ai-showcase-title {
            text-align: center;
            font-size: 15px;
            font-weight: 500;
            line-height: 24px;
            margin-bottom: 24px;
        }

        .ai-showcase-text {
            display: flex;
            font-size: 13px;
            font-weight: 400;
            line-height: 24px;
        }

        .ai-showcase-text .dot {
            background: rgba(255, 255, 255, .24);
            border-radius: 50%;
            display: inline-block;
            height: 4px;
            margin-right: 16px;
            margin-top: 10px;
            width: 4px;
        }

        .ai-showcase-button {
            align-items: center;
            animation: hue-rotate 2s infinite linear;
            color: transparent;
            display: flex;
            position: absolute;
            right: 32px;
            top: 24px;
            transition: .3s cubic-bezier(.6, .6, 0, 1) opacity, .3s cubic-bezier(.6, .6, 0, 1) transform;
        }

        .ai-showcase-button-text {
            background: linear-gradient(to right, #FC72FF, #8F68FF, #487BFF, #2CD9FF, #2CFFCC);
            -webkit-background-clip: text;
            background-clip: text;
            background-size: 200% 100%;
            font-size: 13px;
            font-weight: 400;
            line-height: 24px;
            -webkit-text-fill-color: transparent;
        }

        .ai-showcase-outer-starlight {
            height: 356px;
            -webkit-mask-image: linear-gradient(to bottom, rgba(217, 217, 217, 0) 0%, #d9d9d9 25%, #d9d9d9 75%, rgba(217, 217, 217, 0) 100%);
            mask-image: linear-gradient(to bottom, rgba(217, 217, 217, 0) 0%, #d9d9d9 25%, #d9d9d9 75%, rgba(217, 217, 217, 0) 100%);
            opacity: .25;
            position: absolute;
            width: 1px;
        }

        .ai-showcase-outer-starlight:before {
            animation: aiShowcaseStarlightVertical 5s cubic-bezier(.6, .6, 0, 1) infinite;
            animation-delay: .5s;
            background: linear-gradient(to bottom, rgba(201, 177, 255, 0), #c9b1ff, rgba(201, 177, 255, 0));
            content: "";
            display: block;
            height: 100%;
            transform: translateY(100%);
            width: 1px;
        }

        .ai-showcase-outer-starlight:nth-child(1) {
            left: -76px;
            top: -100px;
        }

        .ai-showcase-outer-starlight:nth-child(2) {
            left: 492px;
            top: -133px;
        }

        .ai-showcase-outer-starlight:nth-child(3) {
            left: 288px;
            top: -50px;
        }

        .ai-showcase-outer-starlight:nth-child(3):before {
            animation-delay: .7s;
        }

        .ai-showcase-outer-starlight:nth-child(4) {
            left: 200px;
            top: -50px;
        }

        .ai-showcase-outer-starlight:nth-child(4):before {
            animation-delay: .8s;
        }

        .ai-showcase-outer-starlight:nth-child(5) {
            left: 376px;
            top: -50px;
        }

        .ai-showcase-outer-starlight:nth-child(5):before {
            animation-delay: .9s;
        }

        @media (max-width: 1248px) {


            .ai-showcase-title {
                margin-bottom: 16px;
            }

            .ai-showcase-text {
                max-width: 265px;
            }

            .ai-showcase-inner {
                width: 334px;
                padding: 24px;
            }

            .ai-showcase-button {
                background: rgba(255, 255, 255, .04);
                border: 1px solid rgba(255, 255, 255, .1);
                box-shadow: inset 0 0 12px #ffffff14;
                -webkit-backdrop-filter: blur(12px);
                backdrop-filter: blur(12px);
                border-radius: 6px;
                bottom: 12px;
                top: unset;
                right: 12px;
            }

            .ai-showcase-button-text {
                display: none;
            }
        }

        /*! CSS Used from: Embedded */
        .ai-showcase-animation {
            pointer-events: none;
        }

        /*! CSS Used from: Embedded */
        .ai-showcase-animation-text-selection {
            background: rgba(147, 130, 255, .32);
            border: 1px solid rgba(255, 255, 255, .1);
            border-radius: 4px;
            display: none;
            height: 24px;
            left: 55px;
            position: absolute;
            top: 81px;
            width: 0;
        }

        @media (max-width: 1248px) {
            .ai-showcase-animation-text-selection {
                height: 48px;
                top: 73px;
                left: 47px;
            }
        }

        /*! CSS Used from: Embedded */
        .ai-showcase-animation-tooltip {
            -webkit-backdrop-filter: blur(12px);
            backdrop-filter: blur(12px);
            background: rgba(255, 255, 255, .04);
            border: 1px solid rgba(255, 255, 255, .1);
            border-radius: 6px;
            box-shadow: inset 0 0 12px #ffffff14;
            display: flex;
            opacity: 0;
            position: absolute;
            right: 40px;
            top: 32px;
            transform: translateY(25px) scale(.4);
        }

        .ai-showcase-animation-tooltip-icon {
            padding: 4px 8px;
        }

        .ai-showcase-animation-tooltip-icon:not(:last-child) {
            border-right: 1px solid rgba(255, 255, 255, .1);
        }

        /*! CSS Used from: Embedded */
        .ai-showcase-animation-menu {
            -webkit-backdrop-filter: blur(22px);
            backdrop-filter: blur(22px);
            background: rgba(3, 0, 20, .2);
            border: 1px solid rgba(255, 255, 255, .1);
            border-radius: 12px;
            box-shadow: inset 0 0 12px #ffffff14;
            font-size: 13px;
            font-weight: 400;
            left: 128px;
            line-height: 24px;
            opacity: 0;
            padding: 6px 0 12px;
            position: absolute;
            top: 112px;
            transform: translate(64px) scale(.8);
            width: 320px;
        }

        .ai-showcase-animation-menu-item {
            padding: 6px 14px;
        }

        .ai-showcase-animation-menu-input {
            border: 1px solid rgba(255, 255, 255, .1);
            border-radius: 4px;
            color: #efedfd99;
            padding: 4px 12px;
        }

        .ai-showcase-animation-menu-category {
            color: #efedfd99;
        }

        @media (max-width: 1248px) {
            .ai-showcase-animation-menu {
                width: 320px;
                left: 15px;
                transform: translateY(24px) scale(.8);
            }
        }

        /*! CSS Used from: Embedded */
        .ai-showcase-animation-answer {
            -webkit-backdrop-filter: blur(22px);
            backdrop-filter: blur(22px);
            background: rgba(3, 0, 20, .2);
            border: 1px solid rgba(255, 255, 255, .1);
            border-radius: 12px;
            box-shadow: inset 0 0 12px #ffffff14;
            font-size: 13px;
            font-weight: 400;
            left: 78px;
            line-height: 24px;
            opacity: 0;
            position: absolute;
            top: 112px;
            transform: translate(384px) scale(.8);
            width: 420px;
        }

        .ai-showcase-animation-answer-heading {
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, .1);
            display: flex;
            justify-content: space-between;
            padding: 12px 14px;
        }

        .ai-showcase-animation-answer-body {
            padding: 8px;
        }

        .ai-showcase-animation-answer-body-inner {
            background: rgba(255, 255, 255, .04);
            border-radius: 6px;
            padding: 12px 14px;
        }

        .ai-showcase-animation-answer-footer {
            display: flex;
            justify-content: space-between;
            padding: 0 8px 8px;
        }

        .ai-showcase-animation-answer-footer div {
            display: flex;
        }

        .ai-showcase-animation-answer-button {
            align-items: center;
            background: #5046e4;
            border: 1px solid rgba(196, 187, 255, .1);
            border-radius: 6px;
            color: #fff;
            display: flex;
            height: 32px;
            white-space: nowrap;
            padding: 0 6px 0 12px;
        }

        .ai-showcase-animation-answer-button:not(:last-child) {
            margin-right: 8px;
        }

        .ai-showcase-animation-answer-button:disabled {
            background: rgba(84, 57, 255, .16);
            border: 1px solid rgba(196, 187, 255, .1);
            color: #ffffff4d;
        }

        .ai-showcase-animation-answer-button:disabled .ai-showcase-animation-answer-button-shortcut {
            background: rgba(84, 57, 255, .16);
            border-color: #c4bbff1a;
            color: #fff3;
        }

        .ai-showcase-animation-answer-button-shortcut {
            background: rgba(255, 255, 255, .05);
            border: 1px solid rgba(255, 255, 255, .1);
            border-radius: 4px;
            font-size: 12px;
            font-weight: 400;
            line-height: 18px;
            margin-left: 8px;
            padding: 0 5px;
        }

        @media (max-width: 1248px) {
            .ai-showcase-animation-answer {
                width: 310px;
                left: 20px;
            }

            .ai-showcase-animation-answer-button-copy {
                display: none;
            }
        }

        /*! CSS Used keyframes */
        @keyframes aiShowcaseStarlight1 {
            0% {
                transform: translate(-100%);
            }
            50%, to {
                transform: translate(100%);
            }
        }

        @keyframes aiShowcaseStarlight2 {
            0% {
                transform: translate(100%);
            }
            50%, to {
                transform: translate(-100%);
            }
        }

        @keyframes hue-rotate {
            0% {
                filter: hue-rotate(0deg);
            }
            to {
                filter: hue-rotate(360deg);
            }
        }

        @keyframes aiShowcaseStarlightVertical {
            0% {
                transform: translateY(100%);
            }
            50%, to {
                transform: translateY(-100%);
            }
        }

        .ai-showcase-text {
            flex-direction: column;
            text-align: center;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .ai-showcase-text a {
            min-width: 200px;
            font-size: 15px;
            font-weight: 600;
            text-decoration: none;
        }

        .button {
            font-feature-settings: "ss01" on, "cv10" on, "calt" off, "liga" off;
        }

        .button {
            font-size: 14px;
            line-height: 20px;
        }

        .button {
            font-weight: 500;
        }

        .button {
            -webkit-backdrop-filter: blur(8px);
            backdrop-filter: blur(8px);
            border-radius: 8px;
            color: #f4f0ff;
            display: block;
            padding: 8px 16px;
            position: relative;
            width: -moz-max-content;
            width: max-content;
        }

        .button:before, .button:after, .button-border {
            border-radius: inherit;
            height: 100%;
            left: 0;
            position: absolute;
            top: 0;
            width: 100%;
            z-index: -1;
        }

        .button:before, .button:after {
            content: "";
            display: block;
            transition: .2s opacity cubic-bezier(.6, .6, 0, 1);
        }

        .button:after {
            opacity: 0;
        }

        .button:hover:before {
            opacity: 0;
        }

        .button:hover:after {
            opacity: 1;
        }

        .rising-stars-visible div {
            animation: risingStarsAnination linear infinite;
        }

        .rising-stars div {
            background: transparent;
            border-radius: 50%;
        }

        @keyframes risingStarsAnination {
            0% {
                transform: translateZ(0) translateY(0)
            }

            to {
                transform: translateZ(0) translateY(-2000px)
            }
        }

        .button-primary .button-border:before {
            background: linear-gradient(180deg, rgba(207, 184, 255, .24) 0%, rgba(207, 184, 255, 0) 100%), linear-gradient(0deg, rgba(207, 184, 255, .32), rgba(207, 184, 255, .32));
            border-radius: inherit;
            content: "";
            inset: 0;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            padding: 1px;
            pointer-events: none;
            position: absolute;
        }

        .button-primary:before {
            background: linear-gradient(180deg, rgba(60, 8, 126, 0) 0%, rgba(60, 8, 126, .32) 100%), rgba(113, 47, 255, .12);
            box-shadow: inset 0 0 12px #bf97ff3d;
        }

        .button-primary:after {
            background: linear-gradient(180deg, rgba(60, 8, 126, 0) 0%, rgba(60, 8, 126, .42) 100%), rgba(113, 47, 255, .24);
            box-shadow: inset 0 0 12px #bf97ff70;
        }

        .mt-3 {
            margin-top: 2rem;
        }

        .scan-box img {
            border-radius: 7px;
        }

        .code-box {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 14px;
        }

        .scan-box {
            margin-left: 12px;
        }

        .scan-box img {
            width: 122px;
        }

        .section-header-title-desktop {
            direction: rtl;
        }

        p.section-header-description {
            direction: rtl;
        }

        .download-box > div {
            max-width: 594px;
            width: 100%;
            background-image: url({{asset('index-assets/png/bg-mesh.png')}});
        }

        .download-box {
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            margin-top: -129px;
            position: relative;
            z-index: 2;
        }
    </style>

@endpush

@section('content')
    <body class="theme-space bg-slate-900 antialiased">
    <main class="page-wrapper">
        <section id="ai" class="ai">
            <div class="section-header" q:id="2x">
                <div class="section-header-badge">
                    <div class="section-header-badge-text">
                        رویداد زنده
                    </div>
                </div>
                <h2 class="section-header-title section-header-title-h2">
                    <div class="section-header-title-desktop">
                        <span>
                           {{$detail->title}}
                        </span>
                    </div>
                    <div class="section-header-title-mobile">
                        <span>{{$detail->title}}</span>
                    </div>
                </h2>
                <p class="section-header-description" q:id="2z">
                    {{$detail->summery}}
                </p></div>
            <div class="ai-background-wrapper">
                <div class="ai-background">
                    <div class="rising-stars rising-stars-visible">
                        <div style="height:1px;width:1px"></div>
                        <div style="height:2px;width:2px"></div>
                        <div style="height:1px;width:1px"></div>
                    </div>
                    <img height="1" width="1" alt=""
                         src="{{asset('index-assets/png/bg-colored.png')}}"
                         class="lazy-image lazy-image-loaded"
                         on:load="q-9e06af5e.js#s_Deti9nTnz3Q[0]"
                         on:qvisible="q-9e06af5e.js#_hW[1]"
                         q:id="33">
                    <div class="ai-showcase" on:click="q-395e709c.js#s_AuKWvY7P940[0]" q:id="35">
                        <div class="ai-showcase-outer-starlights">
                            <div class="ai-showcase-outer-starlight"></div>
                            <div class="ai-showcase-outer-starlight"></div>
                            <div class="ai-showcase-outer-starlight"></div>
                            <div class="ai-showcase-outer-starlight"></div>
                            <div class="ai-showcase-outer-starlight"></div>
                        </div>
                        <div class="ai-showcase-inner">
                            <div class="ai-showcase-inner-starlight"></div>
                            <div class="ai-showcase-inner-starlight"></div>
                            <div class="ai-showcase-title">
                                جهت ورود به این رویداد کافیست روی لینک زیر کلیک کنید
                            </div>
                            <div class="ai-showcase-text">
                                <a onclick="openGoogleMeet('{{$meetingCode}}')" class="button button-primary">
                                    ورود
                                    <div class="button-border"></div>
                                </a>
                                <div
                                    class="d-flex flex-column align-center  justify-content-center justify-content-center mt-3"
                                    style="align-items: center;text-align: center;">
                                    و یا از کد زیر برای ورود در نرم افزار گوگل میت استفاده نمایید: <br/>
                                    <div class="code-box">
                                        <div class="input-group mb-4 ltr" style="max-width: 200px; margin-top:15px;">
                                        <span class="input-group-text" onclick="copyText()">
                                          <i class="bx bx-clipboard fs-base"></i>
                                        </span>
                                            <input class="form-control text-center" readonly id="meetingCode"
                                                   type="text"
                                                   value="{{ $meetingCode  }}">
                                        </div>
                                        <div class="scan-box">
                                            <img
                                                src="https://quickchart.io/qr?text={{urlencode($detail->meeting->meeting_link)}}&light=0f172a&dark=c9c9c9&margin=2&size=200"
                                                width="100" alt="QR Code">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            @if($detail->download_access)
                <div class="download-box">
                    <livewire:public.download-meeting-file :meeting-id="$detail->meeting->meeting_id"
                                                           :meeting-key="meetingKey($detail->meeting->id)"
                                                           :chat-download-access="$detail->chat_download_access"
                    />
                </div>
            @endif

        </section>

    </main>

@endsection

@section('footer')

@endsection
