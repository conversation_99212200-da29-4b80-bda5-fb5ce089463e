@extends('emails.layout')

@section('body')
        <div style="border-bottom:1px solid #efefef; padding-bottom:10px;">
            <span style="color:#aeaeae; font-size:12px"> {{ config('mail.fetch.replyAboveLine') }}</span><br><br>
            <span style="font-size:12px">{{ $title }}</span>
        </div>

        <div style="border-bottom:1px solid #efefef; padding-bottom:10px; margin-left:20px; margin-top:20px;">
            @if(isset($comment) )
                <b> {{ $comment->author()->name }}</b><br>
                <span style="color:gray">{{ $comment->created_at->toDateTimeString() }}</span><br>
                <p>
                    {!! nl2br( strip_tags($comment->body)) !!}
                </p>
            @else
                <b> </b><br>
                <span style="color:gray">{{ $ticket->created_at->toDateTimeString() }}</span><br>
                <p>
                    {!! nl2br( strip_tags($ticket->summary)) !!}
                </p>
            @endif
        </div>

        <div style="margin-top:40px">
            میتوانید تیکت خود را در پنل خود مشاهده کنید و یا پاسخی ارسال کنید <a href="{{$url}}">برای مشاهده کلیک کنید</a>
        </div>

        <span style="color:white">شماره تیکت:{{$ticket->id}}.</span>

@endsection
