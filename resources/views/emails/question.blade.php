@extends('emails.layout')

@section('body')
        <div style="border-bottom:1px solid #efefef; padding-bottom:10px;">
            <span style="color:#aeaeae; font-size:12px"> {{ config('mail.fetch.replyAboveLine') }}</span><br><br>
            <span style="font-size:12px">{{ $title }}</span>
        </div>

        <div style="border-bottom:1px solid #efefef; padding-bottom:10px; margin-left:20px; margin-top:20px;">  <h1>پاسخ داده شده</h1>
            @if(isset($answer) )
                <b> {{ $answer->user->first_name }}</b><br>
                <span style="color:gray">{{ $answer->created_at->toDateTimeString() }}</span><br>
                <p>
                    {!! nl2br( strip_tags($answer->content)) !!}
                </p>
            @else

            @endif
        </div>
        <div style="border-bottom:1px solid #efefef; padding-bottom:10px; margin-left:20px; margin-top:20px;">
            @if(isset($question) )
                <b> سوال شما: </b><br>
                <span style="color:gray">{{ $question->created_at->toDateTimeString() }}</span><br>
                <p>
                    {!! nl2br( strip_tags($question->content)) !!}
                </p>
            @else

            @endif
        </div>

        <div style="margin-top:40px">
            میتوانید اطلاعات بیشتر را در لینک زیر مشاهده نمایید. <br /> <a href="{{$url}}">برای مشاهده کلیک کنید</a>
        </div>

        <span style="color:white">شماره سوال:{{$question->id}}.</span>

@endsection
