<div>
    {{-- Stop trying to control. --}}
    @include('livewire.admin.users.update')
    @if (session()->has('message'))
        <div class="alert alert-success" style="margin-top:30px;">x
            {{ session('message') }}
        </div>
    @endif

    <table class="table table-rounded table-striped border gy-7 gs-7 bg-white amanj-datatable direction-rtl">
        <thead>
        <tr class="fw-bold fs-6 text-gray-800 border-bottom border-gray-200">
            <th>ردیف</th>
            <th>نام کاربری</th>
            <th>نام</th>
            <th>نام خانوادگی</th>
            <th>ایمیل</th>
            <th>اقدامات</th>
        </tr>
        </thead>
        <tbody>
        @foreach($users as $user)
            <tr>
                <td>
                    {{$user->id}}
                </td>
                <td>
                    {{$user->user_name}}
                </td>
                <td>
                    {{$user->first_name}}
                </td>
                <td>
                    {{$user->last_name}}
                </td>
                <td>
                    {{$user->email}}
                </td>
                <td>
                    &nbsp;<a class="btn btn-icon btn-sm me-2" data-bs-toggle="modal" data-bs-target="#edit_users"  wire:click="edit({{ $user->id }})" >
                        <div class="edit-button svg-icon svg-icon-muted svg-icon-2x"></div>
                    </a>
                </td>
            </tr>

        @endforeach
        </tbody>
    </table>

</div>
