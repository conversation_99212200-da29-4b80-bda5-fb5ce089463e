<div>
    <header
        class="backdrop-blur-xl bg-white/20 border-gray-200/60 border-b sticky top-0 z-50 transition-all duration-300">
        <div class="max-w-8xl mx-auto px-8 py-6">
            <div class="flex flex-row items-center justify-between sm-flex-col gap-2">
                <div class="flex items-center space-x-6">
                    <div class="flex items-center space-x-4 flex-row">
                        <div class="relative group">
                            <div
                                class="w-12 h-12 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-2xl group-hover:shadow-3xl transition-all duration-300">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                     fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                     stroke-linejoin="round" class="lucide lucide-mic w-6 h-6 text-white">
                                    <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
                                    <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                                    <line x1="12" x2="12" y1="19" y2="22"></line>
                                </svg>
                            </div>
                        </div>
                        <div><h1 class="text-l font-bold text-gray-900 tracking-tight">رونویسی جلسات هوش مصنوعی</h1>
                            <p class="text-sm text-gray-600 font-medium">رونویسی زنده با تحلیل هوش مصنوعی</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center space-x-4 flex-row">
                    @if (in_array($selectedBotStatus, \App\Enums\AIBot\MeetingBotStatusEnum::endedStatus()))
                        <div
                            class="flex items-center space-x-3 bg-gradient-to-r from-green-500 border-green-200 border px-5 py-3 rounded-2xl backdrop-blur-sm">
                            <span class="text-green-700 text-sm font-semibold tracking-wide direction-rtl">
                                 {{\App\Enums\AIBot\MeetingBotStatusEnum::tryFrom($selectedBotStatus)->label() ?? 'نا مشخص'}}
                        </span>
                        </div>
                    @else
                        <div
                            class="flex items-center space-x-3 bg-gradient-to-r from-red-500/10 to-pink-500/10 border-red-200 border px-5 py-3 rounded-2xl backdrop-blur-sm">
                            <div class="relative">
                                <div class="w-3 h-3 bg-gradient-to-r from-red-500 to-pink-500 rounded-full"></div>
                                <div
                                    class="absolute inset-0 w-3 h-3 bg-gradient-to-r from-red-500 to-pink-500 rounded-full animate-ping opacity-75"></div>
                            </div>
                            <span class="text-red-700 text-sm font-semibold tracking-wide">
                                 {{\App\Enums\AIBot\MeetingBotStatusEnum::tryFrom($selectedBotStatus)->label() ?? 'نا مشخص'}}
                        </span>
                        </div>
                    @endif

                    <div
                        class="flex items-center space-x-2 bg-white/70 border-gray-200/60 border px-4 py-2 rounded-xl backdrop-blur-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                             fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                             stroke-linejoin="round" class="lucide lucide-clock w-4 h-4 text-gray-600">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        <span
                            class="text-sm font-medium text-gray-700">{{\Morilog\Jalali\Jalalian::fromCarbon($selectedBotStartTime)->format('Y/m/d ساعت H:i:s')}}</span>
                    </div>
                    {{--                    <button--}}
                    {{--                        class="p-3 hover:bg-gray-100/50 border-gray-200/60 border rounded-xl transition-all duration-200 backdrop-blur-sm group margin-0">--}}
                    {{--                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"--}}
                    {{--                             fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"--}}
                    {{--                             stroke-linejoin="round"--}}
                    {{--                             class="lucide lucide-moon w-5 h-5 text-gray-600 group-hover:text-blue-500 transition-colors duration-200">--}}
                    {{--                            <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>--}}
                    {{--                        </svg>--}}
                    {{--                    </button>--}}

                </div>
            </div>
        </div>
    </header>
    <div class="max-w-8xl mx-auto px-8 py-8">
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8 h-[calc(100vh-12rem)]">
            <div class="xl:col-span-2">
                <div
                    class="bg-white/70 border-gray-200/60 border rounded-3xl shadow-2xl backdrop-blur-xl h-full flex flex-col overflow-hidden transition-all duration-300">
                    <div
                        class="px-8 py-6 border-gray-200/60 border-b bg-gradient-to-r from-gray-50 to-white backdrop-blur-sm">
                        <div class="flex items-center flex-row justify-between sm-flex-col sm-gap-1">
                            <div class="flex items-center space-x-4 flex-row">
                                <div
                                    class="w-10 h-10 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                         fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                         stroke-linejoin="round" class="lucide lucide-file-text w-5 h-5 text-white">
                                        <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                                        <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                        <path d="M10 9H8"></path>
                                        <path d="M16 13H8"></path>
                                        <path d="M16 17H8"></path>
                                    </svg>
                                </div>
                                <div><h2 class="text-lg font-bold text-gray-900 tracking-tight">رونویسی زنده</h2>
                                    <p class="text-sm text-gray-600 font-medium">رونویسی زنده با تحلیل هوش مصنوعی</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="relative">
                                    <div class="input-icon">
                                        <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                xmlns="http://www.w3.org/2000/svg">
<rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1" transform="rotate(45 17.0365 15.1223)"
      fill="currentColor"/>
<path
    d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
    fill="currentColor"/>
</svg>
</span>
                                    </div>
                                    <input type="text" placeholder="جستجو در رونویسی..."
                                           wire:model.live.debounce="searchTerm"
                                           class="pl-10 pr-4 py-2 border border-gray-200/60 rounded-xl text-sm bg-white/50 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/30 transition-all duration-200 w-64">
                                </div>
                                {{--                                <button class="p-2 hover:bg-gray-100/50 rounded-xl transition-all duration-200">--}}
                                {{--                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"--}}
                                {{--                                         fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"--}}
                                {{--                                         stroke-linejoin="round" class="lucide lucide-download w-4 h-4 text-gray-600">--}}
                                {{--                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>--}}
                                {{--                                        <polyline points="7 10 12 15 17 10"></polyline>--}}
                                {{--                                        <line x1="12" x2="12" y1="15" y2="3"></line>--}}
                                {{--                                    </svg>--}}
                                {{--                                </button>--}}
                            </div>
                        </div>
                    </div>
                    <div class="flex-1 p-8">
                        <div class="space-y-8" @if($chatActive) wire:poll.visible.60s="refreshMessages" @endif>

                            @if(empty($messages) || $messages->count() == 0)
                                <img src="{{asset('assets/media/waiting_room.avif')}}"
                                     style="max-width: 440px;margin: 0px auto;" class="w-50 m-auto d-block"/>
                                <p class="text-center">هنوز پیغامی نداریم</p>
                                <br/>
                                <br/>
                                <br/>
                            @endif
                            @foreach($messages as $message)

                                <!-- chat -->
                                <div class="group relative">
                                    <div
                                        class="absolute left-7 top-20 w-0.5 h-12 bg-gradient-to-b from-gray-300 to-transparent sm-hide"></div>
                                    <div class="flex space-x-6 flex-row sm-flex-col sm-gap-1
                                     @if(chatAlignment($message->participate_name) ==='right')
                                     chat-end
                                     @else
                                     chat-start
                                     @endif
                                    ">
                                        <div class="flex-shrink-0 relative full-flex-no-center gap-2">
                                            <div
                                                class="w-14 h-14 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 rounded-2xl flex items-center justify-center text-white text-sm font-bold shadow-xl transition-all duration-300 group-hover:shadow-2xl">
                                                {{strtoupper(Str::limit($message->participate_name, 2,''))}}
                                            </div>
                                            <div class="flex flex-col">
                                                <p class="text-lg font-bold text-gray-900">{{$message->participate_name}}</p>
                                                <p class="text-sm text-gray-500 font-mono bg-gray-500/10 px-3 py-1 rounded-lg">
                                                    {{$message->time->ago()}} <br/>
                                                    ({{\Morilog\Jalali\Jalalian::fromCarbon($message->time)->format('Y/m/d H:i:s')}}
                                                    )
                                                </p>

                                            </div>
                                        </div>
                                        <div class="flex-1 min-w-0 sm-margin-0">

                                            <div
                                                class="bg-white/70 border-gray-200/60 border rounded-2xl px-8 py-6 backdrop-blur-sm group-hover:shadow-xl transition-all duration-300 bg-eff0f1 chat-box"
                                            >
                                                {{-- اگر سرچ داشتیم و اسنیپت‌ها پر بودن --}}
                                                @if(!empty($this->searchTerm) && !empty($message->snippets))
                                                    @foreach($message->snippets as $snippet)
                                                        <p class="text-gray-900 leading-relaxed text-base mb-2">
                                                            {!! nl2br($snippet) !!}
                                                        </p>
                                                    @endforeach
                                                @else
                                                    {{-- در حالت عادی کل متن رو نشون بده --}}
                                                    <p class="text-gray-900 leading-relaxed text-base">
                                                        {!! nl2br(e($message->transcript)) !!}
                                                    </p>
                                                @endif
                                                <div class="flex flex-wrap gap-2 mt-4">
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <!-- chat -->

                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <div class="xl:col-span-1 sm-row-0">
                <div
                    class="bg-white/70 border-gray-200/60 border rounded-3xl shadow-2xl backdrop-blur-xl h-full flex flex-col overflow-hidden transition-all duration-500">
                    <div
                        class="px-8 py-6 border-gray-200/60 border-b bg-gradient-to-r from-gray-50 to-white backdrop-blur-sm">
                        <div class="flex items-center space-x-4 flex-row">
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                     fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                     stroke-linejoin="round" class="lucide lucide-brain w-5 h-5 text-white">
                                    <path
                                        d="M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"></path>
                                    <path
                                        d="M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"></path>
                                    <path d="M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></path>
                                    <path d="M17.599 6.5a3 3 0 0 0 .399-1.375"></path>
                                    <path d="M6.003 5.125A3 3 0 0 0 6.401 6.5"></path>
                                    <path d="M3.477 10.896a4 4 0 0 1 .585-.396"></path>
                                    <path d="M19.938 10.5a4 4 0 0 1 .585.396"></path>
                                    <path d="M6 18a4 4 0 0 1-1.967-.516"></path>
                                    <path d="M19.967 17.484A4 4 0 0 1 18 18"></path>
                                </svg>
                            </div>
                            <div><h2 class="text-lg font-bold text-gray-900 tracking-tight">هوش مصنوعی</h2>
                                @if(isset($postProcessResult) && is_array($postProcessResult) && !empty($postProcessResult))
                                    <p class="text-sm text-gray-600 font-medium">پردازش شده</p></div>
                            @else
                                <p class="text-sm text-gray-600 font-medium">در حال تحلیل زنده...</p></div>
                        @endif
                    </div>
                </div>
                <div class="flex-1 overflow-y-auto p-6">

                    @if(isset($postProcessResult) && is_array($postProcessResult)  && !empty($postProcessResult))
                        @foreach($postProcessResult as $result)
                            <div class="card card-bordered  rounded-4 my-4">
                                <div
                                    class="card-header bg-gradient-primary text-white text-center py-3 rounded-top-4">
                                    <h4 class="mb-0 fw-bold">
                                        <i class="bi bi-robot me-2"></i> نتیجه
                                        پردازش هوش مصنوعی
                                    </h4>
                                </div>
                                <div class="card-body p-4">

                                    {{-- Section: Title --}}
                                    <div
                                        class="alert alert-secondary text-center fw-bold fs-5 rounded-3 border-0 py-3 mb-4">
                                        عنوان پیشنهادی:
                                        {{ $result['title'] ?? 'Title: Unknown' }}
                                    </div>

                                    <div
                                        class="alert alert-secondary  fw-bold fs-5 rounded-3 border-0 py-3 mb-4">
                                        خلاصه جلسه:
                                        {{ $result['summary'] ?? 'summary: Unknown' }}
                                    </div>


                                    {{-- Section: Hot Topics --}}
                                    <h5 class="fw-bold text-dark mb-3"><i
                                            class="bi bi-fire text-danger me-2"></i>موضوعات مهم مطرح شده
                                    </h5>
                                    <ul class="list-group list-group-flush mb-4">
                                        @foreach($result['hot_topics'] as $topic)
                                            <li class="list-group-item d-flex align-items-center">
                                                  <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24"
                                                                                                          height="24"
                                                                                                          viewBox="0 0 24 24"
                                                                                                          fill="none"
                                                                                                          xmlns="http://www.w3.org/2000/svg">
<path
    d="M9.89557 13.4982L7.79487 11.2651C7.26967 10.7068 6.38251 10.7068 5.85731 11.2651C5.37559 11.7772 5.37559 12.5757 5.85731 13.0878L9.74989 17.2257C10.1448 17.6455 10.8118 17.6455 11.2066 17.2257L18.1427 9.85252C18.6244 9.34044 18.6244 8.54191 18.1427 8.02984C17.6175 7.47154 16.7303 7.47154 16.2051 8.02984L11.061 13.4982C10.7451 13.834 10.2115 13.834 9.89557 13.4982Z"
    fill="currentColor"/>
</svg>
</span>
                                                <span class="text-black">{{ $topic }}</span>
                                            </li>
                                        @endforeach
                                    </ul>

                                    {{-- Section: Key People --}}
                                    <h5 class="fw-bold text-dark mb-3"><i
                                            class="bi bi-person-bounding-box text-success me-2"></i>افراد
                                        موثر
                                    </h5>
                                    <div class="row row-cols-1 row-cols-md-2 g-3 mb-4">
                                        @foreach($result['key_people'] as $person)
                                            <div class="col">
                                                <div
                                                    class="p-3 bg-light rounded-3 d-flex align-items-center">
                                                       <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg
                                                               width="18" height="18" viewBox="0 0 18 18" fill="none"
                                                               xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3"
      d="M16.5 9C16.5 13.125 13.125 16.5 9 16.5C4.875 16.5 1.5 13.125 1.5 9C1.5 4.875 4.875 1.5 9 1.5C13.125 1.5 16.5 4.875 16.5 9Z"
      fill="currentColor"/>
<path
    d="M9 16.5C10.95 16.5 12.75 15.75 14.025 14.55C13.425 12.675 11.4 11.25 9 11.25C6.6 11.25 4.57499 12.675 3.97499 14.55C5.24999 15.75 7.05 16.5 9 16.5Z"
    fill="currentColor"/>
<rect x="7" y="6" width="4" height="4" rx="2" fill="currentColor"/>
</svg>
</span>
                                                    <span
                                                        class="fw-medium text-dark">{{ $person['name'] }}</span>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>

                                    {{-- Section: Actions Needed --}}
                                    <h5 class="fw-bold text-dark mb-3"><i
                                            class="bi bi-bell-fill text-warning me-2"></i>اقدامات مورد نیاز
                                    </h5>
                                    <ul class="list-group list-group-flush mb-4">
                                        @foreach($result['actions_needed'] as $action)
                                            <li class="list-group-item d-flex align-items-center">
                                                    <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24"
                                                                                                            height="24"
                                                                                                            viewBox="0 0 24 24"
                                                                                                            fill="none"
                                                                                                            xmlns="http://www.w3.org/2000/svg">
<path
    d="M11.2929 2.70711C11.6834 2.31658 12.3166 2.31658 12.7071 2.70711L15.2929 5.29289C15.6834 5.68342 15.6834 6.31658 15.2929 6.70711L12.7071 9.29289C12.3166 9.68342 11.6834 9.68342 11.2929 9.29289L8.70711 6.70711C8.31658 6.31658 8.31658 5.68342 8.70711 5.29289L11.2929 2.70711Z"
    fill="currentColor"/>
<path
    d="M11.2929 14.7071C11.6834 14.3166 12.3166 14.3166 12.7071 14.7071L15.2929 17.2929C15.6834 17.6834 15.6834 18.3166 15.2929 18.7071L12.7071 21.2929C12.3166 21.6834 11.6834 21.6834 11.2929 21.2929L8.70711 18.7071C8.31658 18.3166 8.31658 17.6834 8.70711 17.2929L11.2929 14.7071Z"
    fill="currentColor"/>
<path opacity="0.3"
      d="M5.29289 8.70711C5.68342 8.31658 6.31658 8.31658 6.70711 8.70711L9.29289 11.2929C9.68342 11.6834 9.68342 12.3166 9.29289 12.7071L6.70711 15.2929C6.31658 15.6834 5.68342 15.6834 5.29289 15.2929L2.70711 12.7071C2.31658 12.3166 2.31658 11.6834 2.70711 11.2929L5.29289 8.70711Z"
      fill="currentColor"/>
<path opacity="0.3"
      d="M17.2929 8.70711C17.6834 8.31658 18.3166 8.31658 18.7071 8.70711L21.2929 11.2929C21.6834 11.6834 21.6834 12.3166 21.2929 12.7071L18.7071 15.2929C18.3166 15.6834 17.6834 15.6834 17.2929 15.2929L14.7071 12.7071C14.3166 12.3166 14.3166 11.6834 14.7071 11.2929L17.2929 8.70711Z"
      fill="currentColor"/>
</svg>
</span>
                                                <span class="text-dark">{{ $action }}</span>
                                            </li>
                                        @endforeach
                                    </ul>

                                    {{-- Section: Key Decisions --}}
                                    <h5 class="fw-bold text-dark mb-3"><i
                                            class="bi bi-clipboard-check-fill text-primary me-2"></i>تصمیمات
                                        کلیدی و اجرایی</h5>
                                    <ul class="list-group list-group-flush">
                                        @foreach($result['key_decisions_and_action_items'] as $item)
                                            <li class="list-group-item d-flex align-items-center">
                                                    <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24"
                                                                                                            height="24"
                                                                                                            viewBox="0 0 24 24"
                                                                                                            fill="none"
                                                                                                            xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3"
      d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z"
      fill="currentColor"/>
<path
    d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z"
    fill="currentColor"/>
</svg>
</span>
                                                <span class="text-dark">{{ $item }}</span>
                                            </li>
                                        @endforeach
                                    </ul>

                                </div>
                                <div class="card-footer bg-light text-center py-3 rounded-bottom-4">
                                    <small class="text-muted"> 🤖
                                        نتیجه پردازش هوش مصنوعی ممکن است با خطا همراه باشد
                                    </small>
                                </div>
                            </div>
                            <br/>
                            <br/>
                            <br/>

                        @endforeach

                        {{-- Custom CSS for a gradient header --}}
                        <style>
                            .bg-gradient-primary {
                                align-items: center !important;
                                justify-content: center !important;
                                background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                            }
                        </style>

                    @else
                    @endif

                    @if(empty($postProcessResult))
                        <div class="text-center mt-20">
                            <div
                                class="w-20 h-20 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                     fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                     stroke-linejoin="round" class="lucide lucide-eye w-10 h-10 text-white">
                                    <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                    <circle cx="12" cy="12" r="3"></circle>
                                </svg>
                            </div>


                            @if (in_array($selectedBotStatus, \App\Enums\AIBot\MeetingBotStatusEnum::endedStatus()))
                                <h3 class="text-lg font-semibold text-gray-900 mb-3">در انتظار پردازش</h3>
                                <p class="text-sm text-gray-600 leading-relaxed max-w-sm mx-auto">
                                    در انتظار پردازش نتیجه توسط سازنده
                                </p>
                            @else
                                <h3 class="text-lg font-semibold text-gray-900 mb-3"> در حالت آماده‌باش</h3>
                                <p class="text-sm text-gray-600 leading-relaxed max-w-sm mx-auto">هوش مصنوعی به طور فعال
                                    جلسه شما را نظارت می‌کند و پس از پایان جلسه تحلیل جامعی ارائه خواهد داد.</p>
                            @endif

                            <div class="flex items-center justify-center space-x-2 mt-6">
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                                <div class="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
                                     style="animation-delay: 0.1s;"></div>
                                <div class="w-2 h-2 bg-indigo-500 rounded-full animate-bounce"
                                     style="animation-delay: 0.2s;"></div>
                            </div>
                        </div>
                    @endif


                    <div class="space-y-6"></div>
                    <div></div>
                </div>
            </div>
        </div>
    </div>
</div>

