<div class="content fs-6 d-flex flex-column-fluid" id="kt_content">
    <!--begin::Container-->
    <div class="container p-0">
        <!--begin::Layout-->
        <div class="d-flex flex-column flex-lg-row">
            <!--begin::Sidebar-->
            <div class="flex-column flex-lg-row-auto w-100 w-lg-300px w-xl-400px mb-10 mb-lg-0">
                <!--begin::Contacts-->
                <div class="card card-flush">
                    <div class="card-body px-0 pb-0">
                        <!--begin::Title-->
                        <div class="d-flex flex-stack px-9 mt-3 mb-10">
                            <div class="d-flex align-items-center">
                                <span class="fs-1 fw-bold me-3">جلسات</span>
                                <span
                                    class="badge badge badge-light text-muted fs-7 fw-bold rounded py-2 px-3">{{$meetingList->count()}}</span>
                            </div>
                            <div class="btn btn-light-primary fs-6 fw-bold py-2 px-4" data-bs-toggle="modal"
                                 data-bs-target="#new-meeting-bot">
                                جلسه جدید
                            </div>

                            <div class="modal fade" tabindex="-1" id="new-meeting-bot" wire:ignore>
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h3 class="modal-title">جلسه جدید</h3>

                                            <!--begin::Close-->
                                            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2"
                                                 data-bs-dismiss="modal" aria-label="Close">
                                                <span class="svg-icon svg-icon-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24"
                                 height="24" viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)"
      fill="black"></rect>
<rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
</svg></span>
                                            </div>
                                            <!--end::Close-->
                                        </div>

                                        <div class="modal-body">
                                            <div class="card-body bg-light px-12 py-1 text-center">
                                                <img src="{{asset('assets/media/google-meet.png')}}"
                                                     style="max-height: 240px; margin:0px auto"/>
                                            </div>

                                            <div class="mt-10 mb-10">
                                                <label for="link" class="required form-label">لینک جلسه</label>
                                                <div class="input-group mb-5  direction-ltr">
                                                    <span class="input-group-text">  <span
                                                            class="svg-icon svg-icon-muted"><svg width="24" height="24"
                                                                                                 viewBox="0 0 24 24"
                                                                                                 fill="none"
                                                                                                 xmlns="http://www.w3.org/2000/svg">
<path
    d="M19.4 13.9411L10.7 5.24112C10.4 4.94112 10 4.84104 9.60001 5.04104C9.20001 5.24104 9 5.54107 9 5.94107V18.2411C9 18.6411 9.20001 18.941 9.60001 19.141C9.70001 19.241 9.9 19.2411 10 19.2411C10.2 19.2411 10.4 19.141 10.6 19.041C11.4 18.441 12.1 17.941 12.9 17.541L14.4 21.041C14.6 21.641 15.2 21.9411 15.8 21.9411C16 21.9411 16.2 21.9411 16.4 21.8411C17.2 21.5411 17.5 20.6411 17.2 19.8411L15.7 16.2411C16.7 15.9411 17.7 15.741 18.8 15.541C19.2 15.541 19.5 15.2411 19.6 14.8411C19.8 14.6411 19.7 14.2411 19.4 13.9411Z"
    fill="currentColor"/>
<path opacity="0.3"
      d="M15 6.941C14.8 6.941 14.7 6.94102 14.6 6.84102C14.1 6.64102 13.9 6.04097 14.2 5.54097L15.2 3.54097C15.4 3.04097 16 2.84095 16.5 3.14095C17 3.34095 17.2 3.941 16.9 4.441L15.9 6.441C15.7 6.741 15.4 6.941 15 6.941ZM18.4 9.84102L20.4 8.84102C20.9 8.64102 21.1 8.04097 20.8 7.54097C20.6 7.04097 20 6.84095 19.5 7.14095L17.5 8.14095C17 8.34095 16.8 8.941 17.1 9.441C17.3 9.841 17.6 10.041 18 10.041C18.2 9.94097 18.3 9.94102 18.4 9.84102ZM7.10001 10.941C7.10001 10.341 6.70001 9.941 6.10001 9.941H4C3.4 9.941 3 10.341 3 10.941C3 11.541 3.4 11.941 4 11.941H6.10001C6.70001 11.941 7.10001 11.541 7.10001 10.941ZM4.89999 17.1409L6.89999 16.1409C7.39999 15.9409 7.59999 15.341 7.29999 14.841C7.09999 14.341 6.5 14.141 6 14.441L4 15.441C3.5 15.641 3.30001 16.241 3.60001 16.741C3.80001 17.141 4.1 17.341 4.5 17.341C4.6 17.241 4.79999 17.2409 4.89999 17.1409Z"
      fill="currentColor"/>
</svg>
</span></span>
                                                    <input id="link" type="text"
                                                           wire:model="meetingLink"
                                                           class="form-control"
                                                           placeholder="https://meet.google.com/har-haem-wky">
                                                </div>
                                            </div>

                                            <div class="mt-10 mb-10">
                                                <label for="link" class="required form-label">زبانی که در جلسه صحبت
                                                    میشود</label>
                                                <div class="input-group mb-5  direction-ltr">
                                                    <span class="input-group-text">
                                                        <span class="svg-icon svg-icon-muted"><svg width="24"
                                                                                                   height="24"
                                                                                                   viewBox="0 0 24 24"
                                                                                                   fill="none"
                                                                                                   xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3" d="M18.4 5.59998C21.9 9.09998 21.9 14.8 18.4 18.3C14.9 21.8 9.2 21.8 5.7 18.3L18.4 5.59998Z"
      fill="currentColor"/>
<path
    d="M12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2ZM19.9 11H13V8.8999C14.9 8.6999 16.7 8.00005 18.1 6.80005C19.1 8.00005 19.7 9.4 19.9 11ZM11 19.8999C9.7 19.6999 8.39999 19.2 7.39999 18.5C8.49999 17.7 9.7 17.2001 11 17.1001V19.8999ZM5.89999 6.90002C7.39999 8.10002 9.2 8.8 11 9V11.1001H4.10001C4.30001 9.4001 4.89999 8.00002 5.89999 6.90002ZM7.39999 5.5C8.49999 4.7 9.7 4.19998 11 4.09998V7C9.7 6.8 8.39999 6.3 7.39999 5.5ZM13 17.1001C14.3 17.3001 15.6 17.8 16.6 18.5C15.5 19.3 14.3 19.7999 13 19.8999V17.1001ZM13 4.09998C14.3 4.29998 15.6 4.8 16.6 5.5C15.5 6.3 14.3 6.80002 13 6.90002V4.09998ZM4.10001 13H11V15.1001C9.1 15.3001 7.29999 16 5.89999 17.2C4.89999 16 4.30001 14.6 4.10001 13ZM18.1 17.1001C16.6 15.9001 14.8 15.2 13 15V12.8999H19.9C19.7 14.5999 19.1 16.0001 18.1 17.1001Z"
    fill="currentColor"/>
</svg>
</span>
                                                    </span>
                                                    <select wire:model="meetingLang" class="form-select">
                                                        <option value="fa-IR">فارسی</option>
                                                        <option value="en-US">انگلیسی</option>
                                                    </select>
                                                </div>
                                            </div>

                                        </div>

                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                                                انصراف
                                            </button>

                                            <button wire:click="createMeeting()" class="btn btn-primary"
                                                    wire:loading.class="disabled" wire:target="createMeeting"
                                            >

                                                ثبت و ارسال ربات
                                                <div wire:loading wire:target="createMeeting">
                                                    <span
                                                        class="spinner-border spinner-border-sm align-middle m-1"></span>
                                                </div>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--end::Title-->

                        <!--begin::Inbox Aside-->
                        <div class="overflow-auto">
                            <div data-action="list" class="mh-300px mh-lg-600px">

                                @foreach($meetingList as $meeting)
                                    <!--begin::Item-->
                                    <div class="bg-state-light px-9 py-8 d-flex" data-action="list"
                                         wire:click="selectMeeting('{{$meeting->attendee_service_bot_id}}')"
                                         wire:target="selectMeeting('{{$meeting->attendee_service_bot_id}}')"
                                         wire:loading.class="disabled">
                                        <div class="w-15px h-15px me-3">
                                            <span class="bullet bullet-dot w-6px h-6px bg-primary"></span>
                                        </div>
                                        <div class="d-flex flex-row-fluid flex-column">
                                            <div class="fs-6 fw-bold text-gray-900 mb-3">{{$meeting->title}}</div>
                                            <div class="fw-semibold fs-7 text-gray-600 mb-3">

                                                @if(isset($meeting->artificialIntelligence?->message_json['title']))
                                                    {{$meeting->artificialIntelligence?->message_json['title']}}
                                                @endif
                                                <span class="spinner-border spinner-border-sm align-middle ms-2"
                                                      wire:target="selectMeeting('{{$meeting->attendee_service_bot_id}}')"
                                                      wire:loading="selectMeeting('{{$meeting->attendee_service_bot_id}}')"></span>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="text-muted fw-bold">
                                                    {{\App\Enums\AIBot\MeetingBotStatusEnum::tryFrom($meeting->status)->label() ?? 'نا مشخص'}}
                                                </div>
                                                <div class="text-gray-500 fs-7 fw-bold text-end">
                                                    {{$meeting->created_at->ago()}}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--end::Item-->
                                @endforeach


                            </div>
                        </div>
                        <!--end::Inbox Aside-->
                    </div>
                </div>
                <!--end::Contacts-->
            </div>
            <!--end::Sidebar-->

            <!--begin::Content-->
            <div class="flex-lg-row-fluid ms-lg-7 ms-xl-10">
                @if(empty($selectedBot))
                    <!--begin::Messenger-->
                    <div class="card" id="kt_chat_messenger">
                        <!--begin::Card header-->
                        <div class="card-header" id="kt_chat_messenger_header">
                            <!--begin::Title-->
                            <div class="card-title">
                                <!--begin::User-->
                                <div class="d-flex justify-content-center flex-column me-3">
                                    جلسه ای انتخاب نشده است
                                    <!--end::Info-->
                                </div>
                                <!--end::User-->
                            </div>

                            <!--end::Title-->

                            <!--begin::Card toolbar-->
                            <div class="card-toolbar">
                                <!--begin::Menu-->
                                <div class="me-n3">
                                    <button class="btn btn-sm btn-icon btn-active-light-primary"
                                            data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                                        <span class="svg-icon svg-icon-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                                         viewBox="0 0 24 24">
                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                         <rect x="5" y="5" width="5" height="5" rx="1" fill="#000000"></rect>
                                            <rect x="14" y="5" width="5" height="5" rx="1" fill="#000000"
                                                  opacity="0.3"></rect>
                                            <rect x="5" y="14" width="5" height="5" rx="1" fill="#000000"
                                                  opacity="0.3"></rect>
                                            <rect x="14" y="14" width="5" height="5" rx="1" fill="#000000"
                                                  opacity="0.3"></rect>
                                        </g>
                                    </svg>
                                </span>
                                    </button>

                                    <div class="modal fade" tabindex="-1" id="kt_modal_export_for_ai">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h3 class="modal-title">خروجی مناسب هوش مصنوعی</h3>

                                                    <!--begin::Close-->
                                                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2"
                                                         data-bs-dismiss="modal" aria-label="Close">
                                                        <i class="ki-duotone ki-cross fs-1"><span
                                                                class="path1"></span><span class="path2"></span></i>
                                                    </div>
                                                    <!--end::Close-->
                                                </div>

                                                <div class="modal-body">
                                                    <p>شما میتوانید متن زیر را در سرویس هوش مصنوعی دلخواه خود پردازش
                                                        کنید.</p>
                                                    <p class="d-flex align-items-center rounded py-5 px-4 bg-light-info ">
                                                        {{$readyForAI}}
                                                    </p>
                                                </div>

                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                                                        بستن
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!--begin::Menu 3-->
                                    <div
                                        class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px py-3"
                                        data-kt-menu="true" style="" wire:ignore.self>
                                        <!--begin::Heading-->
                                        <div class="menu-item px-3">
                                            <div class="menu-content text-muted pb-2 px-3 fs-7 text-uppercase">
                                                تنظیمات
                                            </div>
                                        </div>
                                        <!--end::Heading-->

                                        <!--begin::Menu item-->
                                        <div class="menu-item px-3">
                                            <a href="#" class="menu-link px-3">
                                                جلسه ای انتخاب نشده است
                                            </a>
                                        </div>
                                        <!--end::Menu item-->

                                        <!--end::Menu item-->

                                        <!--end::Menu item-->
                                    </div>
                                </div>
                                <!--end::Menu-->
                            </div>
                            <!--end::Card toolbar-->
                        </div>
                        <!--end::Card header-->

                        <!--begin::Card body-->
                        <div class="card-body text-center" id="">
                            <img src="{{asset('assets/media/google-cat.png')}}"
                                 style="width: 100%; max-width: 300px"/><br/><br/>
                            <p class="font-weight-700 fs-3 fw-bold">
                                لطفا یک جلسه را انتخاب نمایید و یا جلسه جدیدی بسازید
                            </p>
                        </div>
                        <!--end::Card body-->

                        <!--begin::Card footer-->
                        <div class="card-footer pt-4" id="kt_chat_messenger_footer">

                        </div>
                        <!--end::Card footer-->
                    </div>
                    <!--end::Messenger-->
                @else
                    <!--begin::Messenger-->
                    <div class="card" id="kt_chat_messenger">
                        <!--begin::Card header-->
                        <div class="card-header" id="kt_chat_messenger_header">
                            <!--begin::Title-->
                            <div class="card-title">
                                <!--begin::User-->
                                <div class="d-flex justify-content-center flex-column me-3">
                                    <a href="#" class="fs-4 fw-bold text-gray-900 text-hover-primary me-1 mb-2 lh-1">
                                        {{$selectedBotTitle}}
                                    </a>

                                    <!--begin::Info-->
                                    <div class="mb-0 lh-1">
                                        <span class="badge badge-success badge-circle w-10px h-10px me-1"></span>
                                        <span class="fs-7 fw-semibold text-muted">
                                              {{\App\Enums\AIBot\MeetingBotStatusEnum::tryFrom($selectedBotStatus)->label() ?? 'نا مشخص'}}
                                        </span>
                                    </div>
                                    <!--end::Info-->
                                </div>
                                <!--end::User-->
                            </div>
                            <!--end::Title-->

                            <!--begin::Card toolbar-->
                            <div class="card-toolbar">
                                <!--begin::Menu-->
                                <div class="me-n3">
                                    <button class="btn btn-sm btn-icon btn-active-light-primary"
                                            data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                                        <span class="svg-icon svg-icon-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                                         viewBox="0 0 24 24">
                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                         <rect x="5" y="5" width="5" height="5" rx="1" fill="#000000"></rect>
                                            <rect x="14" y="5" width="5" height="5" rx="1" fill="#000000"
                                                  opacity="0.3"></rect>
                                            <rect x="5" y="14" width="5" height="5" rx="1" fill="#000000"
                                                  opacity="0.3"></rect>
                                            <rect x="14" y="14" width="5" height="5" rx="1" fill="#000000"
                                                  opacity="0.3"></rect>
                                        </g>
                                    </svg>
                                </span>
                                    </button>

                                    <div class="modal fade" tabindex="-1" id="kt_modal_export_for_ai">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h3 class="modal-title">خروجی مناسب هوش مصنوعی</h3>

                                                    <!--begin::Close-->
                                                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2"
                                                         data-bs-dismiss="modal" aria-label="Close">
                                                        <i class="ki-duotone ki-cross fs-1"><span
                                                                class="path1"></span><span class="path2"></span></i>
                                                    </div>
                                                    <!--end::Close-->
                                                </div>

                                                <div class="modal-body">
                                                    <p>شما میتوانید متن زیر را در سرویس هوش مصنوعی دلخواه خود پردازش
                                                        کنید.</p>
                                                    <p class="d-flex align-items-center rounded py-5 px-4 bg-light-info ">
                                                        {{$readyForAI}}
                                                    </p>
                                                </div>

                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                                                        بستن
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!--begin::Menu 3-->
                                    <div
                                        class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px py-3"
                                        data-kt-menu="true" style="" wire:ignore.self>
                                        <!--begin::Heading-->
                                        <div class="menu-item px-3">
                                            <div class="menu-content text-muted pb-2 px-3 fs-7 text-uppercase">
                                                تنظیمات
                                            </div>
                                        </div>
                                        <!--end::Heading-->

                                        <!--begin::Menu item-->
                                        <div class="menu-item px-3">
                                            <a href="#" class="menu-link px-3" data-bs-toggle="modal"
                                               data-bs-target="#kt_modal_export_for_ai">
                                                خروجی مناسب هوش مصنوعی
                                            </a>
                                        </div>
                                        <!--end::Menu item-->

                                        <!--begin::Menu item-->
                                        <div class="menu-item px-3">
                                            <a href="{{$selectedBotPublicLink}}" class="menu-link flex-stack px-3"
                                               target="_blank">
                                                لینک مشاهده آنلاین

                                                <span class="ms-2" data-bs-toggle="tooltip"
                                                      aria-label="Specify a contact email to send an invitation"
                                                      data-bs-original-title="Specify a contact email to send an invitation"
                                                      data-kt-initialized="1">
                <i class="ki-duotone ki-information fs-7"><span class="path1"></span><span class="path2"></span><span
                        class="path3"></span></i>            </span>
                                            </a>
                                        </div>
                                        <!--end::Menu item-->

                                        <!--end::Menu item-->
                                    </div>
                                </div>
                                <!--end::Menu-->
                            </div>
                            <!--end::Card toolbar-->
                        </div>
                        <!--end::Card header-->

                        <!--begin::Card body-->
                        <div class="card-body" id="kt_chat_messenger_body">
                            <div id="new-message-banner"
                                 wire:ignore.self
                                 class="hidden absolute bottom-16 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white px-4 py-2 rounded cursor-pointer shadow">
                                آخرین چت ها ↑
                            </div>
                            <!--begin::Messages-->
                            <div class="scroll-y me-n5 pe-5 h-300px h-lg-auto"
                                 @if($chatActive) wire:poll.visible.30s="refreshMessages" @endif
                                 id="chat-box"
                                 style="max-height: 644px;">
                                <!--end::Menu 3-->
                                @if(isset($postProcessResult) && is_array($postProcessResult))
                                    @foreach($postProcessResult as $result)
                                        <div class="card card-bordered  rounded-4 my-4">
                                            <div
                                                class="card-header bg-gradient-primary text-white text-center py-3 rounded-top-4">
                                                <h4 class="mb-0 fw-bold">
                                                    <i class="bi bi-robot me-2"></i> نتیجه
                                                    پردازش هوش مصنوعی
                                                </h4>
                                            </div>
                                            <div class="card-body p-4">

                                                {{-- Section: Title --}}
                                                <div
                                                    class="alert alert-secondary text-center fw-bold fs-5 rounded-3 border-0 py-3 mb-4">
                                                    عنوان پیشنهادی:
                                                    {{ $result['title'] ?? 'Title: Unknown' }}
                                                </div>

                                                <div
                                                    class="alert alert-secondary  fw-bold fs-5 rounded-3 border-0 py-3 mb-4">
                                                    خلاصه جلسه:
                                                    {{ $result['summary'] ?? 'summary: Unknown' }}
                                                </div>


                                                {{-- Section: Hot Topics --}}
                                                <h5 class="fw-bold text-dark mb-3"><i
                                                        class="bi bi-fire text-danger me-2"></i>موضوعات مهم مطرح شده
                                                </h5>
                                                <ul class="list-group list-group-flush mb-4">
                                                    @foreach($result['hot_topics'] as $topic)
                                                        <li class="list-group-item d-flex align-items-center">
                                                            <i class="bi bi-tags-fill text-muted me-3"></i>
                                                            <span class="text-black">{{ $topic }}</span>
                                                        </li>
                                                    @endforeach
                                                </ul>

                                                {{-- Section: Key People --}}
                                                <h5 class="fw-bold text-dark mb-3"><i
                                                        class="bi bi-person-bounding-box text-success me-2"></i>افراد
                                                    موثر
                                                </h5>
                                                <div class="row row-cols-1 row-cols-md-2 g-3 mb-4">
                                                    @foreach($result['key_people'] as $person)
                                                        <div class="col">
                                                            <div
                                                                class="p-3 bg-light rounded-3 d-flex align-items-center">
                                                                <i class="bi bi-person-fill text-success fs-4 me-3"></i>
                                                                <span
                                                                    class="fw-medium text-dark">{{ $person['name'] }}</span>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>

                                                {{-- Section: Actions Needed --}}
                                                <h5 class="fw-bold text-dark mb-3"><i
                                                        class="bi bi-bell-fill text-warning me-2"></i>اقدامات مورد نیاز
                                                </h5>
                                                <ul class="list-group list-group-flush mb-4">
                                                    @foreach($result['actions_needed'] as $action)
                                                        <li class="list-group-item d-flex align-items-center">
                                                            <i class="bi bi-check-circle-fill text-info me-3"></i>
                                                            <span class="text-dark">{{ $action }}</span>
                                                        </li>
                                                    @endforeach
                                                </ul>

                                                {{-- Section: Key Decisions --}}
                                                <h5 class="fw-bold text-dark mb-3"><i
                                                        class="bi bi-clipboard-check-fill text-primary me-2"></i>تصمیمات
                                                    کلیدی و اجرایی</h5>
                                                <ul class="list-group list-group-flush">
                                                    @foreach($result['key_decisions_and_action_items'] as $item)
                                                        <li class="list-group-item d-flex align-items-center">
                                                            <i class="bi bi-arrow-right-short text-primary fs-5 me-3"></i>
                                                            <span class="text-dark">{{ $item }}</span>
                                                        </li>
                                                    @endforeach
                                                </ul>

                                            </div>
                                            <div class="card-footer bg-light text-center py-3 rounded-bottom-4">
                                                <small class="text-muted"> 🤖
                                                    نتیجه پردازش هوش مصنوعی ممکن است با خطا همراه باشد
                                                </small>
                                            </div>
                                        </div>
                                        <br/>
                                        <br/>
                                        <br/>

                                    @endforeach

                                    {{-- Custom CSS for a gradient header --}}
                                    <style>
                                        .bg-gradient-primary {
                                            align-items: center !important;
                                            justify-content: center !important;
                                            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                                        }
                                    </style>

                                @else
                                @endif

                                @if(empty($messages))
                                    <img src="{{asset('assets/media/waiting_room.avif')}}" class="w-50 m-auto d-block"/>
                                    <p class="text-center">هنوز پیغامی نداریم</p>
                                    <br/>
                                    <br/>
                                    <br/>
                                @endif

                                @foreach(array_reverse($messages) as $message)
                                    <!--begin::Message(in)-->
                                    <div class="d-flex
                                     @if(chatAlignment($message->participate_name) ==='right')
                                     justify-content-end
                                     @else
                                     justify-content-start
                                     @endif
                                     mb-10 ">
                                        <!--begin::Wrapper-->
                                        <div class="d-flex flex-column align-items-start">
                                            <!--begin::User-->
                                            <div class="d-flex align-items-center mb-2">
                                                <!--begin::Avatar-->
                                                <div class="symbol  symbol-45px symbol-circle ">
                                                    <span
                                                        class="symbol-label

                                                         @if(chatAlignment($message->participate_name) ==='right')
                                                            bg-light-warning
                                                           text-warning
                                                         @else
                                                            bg-light-danger
                                                           text-danger
                                                         @endif
                                                       fs-6 fw-bolder ">
                                                        {{strtoupper(Str::limit($message->participate_name, 1,''))}}

                                                    </span>
                                                </div>
                                                <!--end::Avatar-->
                                                <!--begin::Details-->
                                                <div class="ms-3">
                                                    <a href="#"
                                                       class="fs-5 fw-bold text-gray-900 text-hover-primary me-1">
                                                        {{$message->participate_name}}
                                                    </a> ا
                                                    <span class="text-muted fs-7 mb-1">{{$message->time->ago()}} ({{\Morilog\Jalali\Jalalian::fromCarbon($message->time)->format('Y/m/d H:i:s')}})</span>
                                                </div>
                                                <!--end::Details-->

                                            </div>
                                            <!--end::User-->

                                            <!--begin::Text-->
                                            <div
                                                class="p-5 rounded
                                                 @if(chatAlignment($message->participate_name) ==='right')
                                                      bg-light-info
                                                     @else
                                                     bg-light-primary
                                                     @endif
                                                 text-gray-900 fw-semibold mw-lg-400px text-start"
                                                data-kt-element="message-text">
                                                {!! nl2br(e($message->transcript)) !!}
                                            </div>
                                            <!--end::Text-->
                                        </div>
                                        <!--end::Wrapper-->
                                    </div>
                                    <!--end::Message(in)-->
                                @endforeach

                                <!--end::Messages-->
                            </div>
                            <!--end::Card body-->

                            <!--begin::Card footer-->
                            <div class="card-footer pt-4" id="kt_chat_messenger_footer">
                                <!--begin::Input-->
                                <textarea class="form-control form-control-flush mb-3" rows="1" data-kt-element="input"
                                          placeholder="پرامپت خود را وارد کنید"></textarea>
                                <!--end::Input-->

                                <!--begin:Toolbar-->
                                <div class="d-flex flex-stack">
                                    <!--begin::Actions-->
                                    <div class="d-flex align-items-center me-2">
                                        <button class="btn btn-sm btn-icon btn-active-light-primary me-1" type="button"
                                                data-bs-toggle="tooltip" aria-label="Coming soon"
                                                data-bs-original-title="Coming soon" data-kt-initialized="1">
                                            <i class="ki-duotone ki-paper-clip fs-3"></i></button>
                                        <button class="btn btn-sm btn-icon btn-active-light-primary me-1" type="button"
                                                data-bs-toggle="tooltip" aria-label="Coming soon"
                                                data-bs-original-title="Coming soon" data-kt-initialized="1">
                                            <i class="ki-duotone ki-exit-up fs-3"><span class="path1"></span><span
                                                    class="path2"></span></i></button>
                                    </div>
                                    <!--end::Actions-->
                                    @if($canProcess)
                                        @if(isset($postProcessResult))
                                            <!--begin::Send-->
                                            <button class="btn btn-primary disabled">
                                                امکان پردازش مجدد نیست
                                                <div wire:loading wire:target="postProcess">
                                                    <span
                                                        class="spinner-border spinner-border-sm align-middle m-1"></span>
                                                </div>
                                            </button>
                                            <!--end::Send-->
                                        @else
                                            <!--begin::Send-->
                                            <button wire:click="postProcess()" class="btn btn-primary"
                                                    wire:loading.class="disabled" wire:target="postProcess"
                                            >

                                                پردازش توسط هوش مصنوعی
                                                <div wire:loading wire:target="postProcess">
                                                    <span
                                                        class="spinner-border spinner-border-sm align-middle m-1"></span>
                                                </div>
                                            </button>
                                            <!--end::Send-->
                                        @endif

                                        @if(auth()->user()->isSuperAdmin())
                                            <!--begin::Send-->
                                            <button wire:click="postProcess()" class="btn btn-primary"
                                                    wire:loading.class="disabled" wire:target="postProcess"
                                            >

                                                پردازش توسط مدیر
                                                <div wire:loading wire:target="postProcess">
                                                    <span
                                                        class="spinner-border spinner-border-sm align-middle m-1"></span>
                                                </div>
                                            </button>
                                            <!--end::Send-->
                                        @endif
                                    @else
                                        <p> فقط در صورت اتمام جلسه میتوانید آن را پردازش کنید.</p>
                                    @endif

                                </div>
                                <!--end::Toolbar-->
                            </div>
                            <!--end::Card footer-->
                        </div>
                        <!--end::Messenger-->

                    </div>
                @endif
            </div>
            <!--end::Content-->
        </div>
        <!--end::Layout-->
        <!--begin::Modals-->


    </div>

    <style>
        div#new-message-banner {
            background: #e9f3ff;
            color: #1b84ff !important;
            width: max-content;
            position: absolute;
            z-index: 9999;
            bottom: 147px;
            right: calc(50% - 60px);
        }

        #new-message-banner.hidden {
            display: none;
        }
    </style>
    <!--end::Container-->
    @push('scripts')
        <script>
            // Helpers
            function isAtBottom(chatBox) {
                return chatBox.scrollHeight - chatBox.scrollTop <= chatBox.clientHeight + 10;
            }

            function scrollToBottom(chatBox, banner) {
                chatBox.scrollTop = chatBox.scrollHeight;
                banner.classList.add('hidden');
            }

            function scrollToTop(chatBox, banner) {
                chatBox.scrollTop = 0;
                banner.classList.add('hidden');
            }

            // Livewire listener
            document.addEventListener("livewire:navigated", () => {
                Livewire.on('chat-updated', () => {

                    const chatBox = document.getElementById('chat-box');
                    const banner = document.getElementById('new-message-banner');

                    if (!chatBox || !banner) return; // safety

                    if (isAtBottom(chatBox)) {
                        scrollToTop(chatBox, banner);
                    } else {
                        banner.classList.remove('hidden');
                    }

                    // attach banner click once
                    if (!banner.dataset.listener) {
                        banner.addEventListener('click', () => scrollToTop(chatBox, banner));
                        // banner.dataset.listener = "true";
                    }
                });
            });
        </script>
    @endpush

</div>
