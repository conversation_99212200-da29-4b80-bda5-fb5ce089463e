<div>
    <h3 class="text-center mb-4 fw-semibold  mt-5">سابقه محتواهای این رویداد</h3>
    <style>
        .meeting-title {
            width: 228px;
        }
        .disabled {
            filter: grayscale(1);
        }

        @media only screen and (max-width: 768px) {
            .meeting-title {
                width: 80px !important;
                margin-left: 10px !important;
            }
        }
    </style>
    @if($hasPassword && !$skipPass)
        <style>
            .direction-ltr {
                direction: ltr;
            }

            .btn:not(.btn-outline):not(.btn-dashed):not(.btn-bordered):not(.border-hover):not(.border-active):not(.btn-flush):not(.btn-icon):not(.btn-hover-outline) {
                border: 0;
                padding: calc(.775rem + 1px) calc(1.5rem + 1px);
            }

            .errorPass {
                font-size: 12px;
                color: red;
            }

            .btn.btn-light {
                color: var(--bs-light-inverse);
                border-color: red;
                background-color: #e9e9e9;
                margin-right: 10px;
                height: 40px !important;
            }

            .passbox {
                display: flex;
                gap: 16px;
            }

            .la-eye-slash:before {
                content: "\ee0d";
            }

            .la-eye:before {
                content: "\eb4c";
            }
        </style>
        <script>
            document.addEventListener("DOMContentLoaded", function () {
                document.querySelectorAll('[data-kt-password-meter-control="visibility"]').forEach(function (toggleButton) {
                    toggleButton.addEventListener("click", function () {
                        let input = toggleButton.parentElement.querySelector('input[data-type="password"]');
                        let eyeIcon = toggleButton.querySelector('.ki-eye');
                        let eyeSlashIcon = toggleButton.querySelector('.ki-eye-slash');

                        if (input.type === "password") {
                            input.type = "text";
                            eyeIcon.classList.remove("d-none");
                            eyeSlashIcon.classList.add("d-none");
                        } else {
                            input.type = "password";
                            eyeIcon.classList.add("d-none");
                            eyeSlashIcon.classList.remove("d-none");
                        }
                    });
                });
            });

        </script>


        <div class="mb-5 d-flex flex-column align-items-center mb-2 passbox">
            <label class="w-100px"></label>
            <label class="col-form-label fw-bold text-start text-lg-end">
                برای مشاهده محتوای رویداد رمز را وارد نمایید
                <i class="fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="tooltip" title=""
                   data-bs-original-title="با توجه به عمومی بودن این صفحه بهتر است برای حفظ امنیت اطلاعات خود، یک رمز عبور تنظیم کنید. در صورت عدم تمایل رمز را خالی بگذارید."
                   aria-label="با توجه به عمومی بودن این صفحه بهتر است برای حفظ امنیت اطلاعات خود، یک رمز عبور تنظیم کنید. در صورت عدم تمایل رمز را خالی بگذارید."></i>
            </label>

            <div class="d-flex align-items-center justify-center ms-3">
                <div class="form-input input-group direction-ltr">
                    <!--begin::Visibility toggle-->
                    <span class="input-group-text "
                          data-kt-password-meter-control="visibility">
                                            <i class="ki-duotone ki-eye-slash fs-1">
                                                <i
                                                    class="la bx la-eye-slash fs-4"></i>
                                            </i>
                                            <i class="ki-duotone ki-eye d-none fs-1">
                                               <i
                                                   class="la bx la-eye fs-4"></i>
                                            </i>
                                    </span>
                    <!--end::Visibility toggle-->

                    <input
                        type="password"
                        data-type="password"
                        class="form-control form-control-solid"
                        autocomplete="off"
                        wire:model.defer="publicPagePasswordByUser"
                        placeholder="رمز عبور"/>

                </div>
                <button
                    wire:loading.class="disabled"
                    class="btn btn-light fw-bold flex-shrink-0" wire:click="loadWithPassword()">

                    <span wire:loading.remove wire:target="loadWithPassword">مشاهده</span>
                    <span wire:loading wire:target="loadWithPassword">منتظر بمانید</span>

                </button>
            </div>

            <div class="errorPass">
                {{$publicPagePasswordError}}
            </div>

        </div>

    @else

        <!-- Pricing -->
        <ul class="list-group pl-0 pr-0" style=" max-height: 312px; overflow: auto; " wire:init="loadPosts">
            <div wire:loading wire:target="loadPosts" class="d-flex justify-content-center text-center">
                <div class="dark-box" wire:loading wire:target="loadPosts">
                    در حال دریافت اطلاعات ...
                    <span class="spinner-border spinner-border-sm align-middle"></span>
                </div>
            </div>
            @if(!empty($filteredFiles))
                @forelse($filteredFiles as $meetingFile)
                    <li class="list-group-item d-flex flex-column flex-sm-row align-items-center justify-content-between p-4 dark-box dark-list-style">
                        <div class="d-flex align-items-center">
                            <spam style=" opacity: 0.4; width: 18px">
                                {!! getIconFromMime($meetingFile->mime_type) !!}
                            </spam>

                            <h4 class="fs-base fw-semibold text-nowrap pe-1 mb-0 ml-3 meeting-title" style=" margin-left: 24px; ">
                                {{$meetingFile->getRawOriginal('file_name') ?? 'جلسه تاریخ '.$meetingFile->name_from_original_name}}
                            </h4>
                        </div>
                        <div class="fs-sm" style="white-space: nowrap;">
                            @if(isset($meetingFile->file_size))
                                {{formatBytes($meetingFile->file_size) ?? '-'}}
                            @endif
                        </div>
                        <div wire:click="downloadLink('{{$meetingFile->file_id}}')"
                             wire:key="mk_{{$meetingFile->file_id}}"
                             class="badge bg-primary  fs-6 fw-semibold rounded px-3 py-1 my-3 my-sm-0 cursor-pointer dl-btn"
                             wire:loading.class="disabled"
                             wire:target="downloadLink"
                             style=" margin-right: 15px; "
                             wire:click="downloadLink">
                            <div wire:loading.remove wire:target="downloadLink('{{$meetingFile->file_id}}')">
                                دریافت
                            </div>
                            <div wire:loading wire:target="downloadLink('{{$meetingFile->file_id}}')">
                                <span class="spinner-border spinner-border-sm align-middle"></span>
                            </div>
                        </div>


                    </li>
                @empty

                @endforelse

            @endif
        </ul>
    @endif
</div>
