<div>
    {{-- Care about people's approval and you will be their prisoner. --}}

    <div class="card mt-10 mt-lg-0">
        <!--begin::Header-->
        <div class="card-header align-items-center card-px">
            <!--begin::Toolbar-->
            <div class="d-flex align-items-center">
                <div class="form-check form-check-sm form-check-custom form-check-solid me-4 my-2" data-inbox="group-select">
                    <input class="form-check-input" type="checkbox" value="1" />
                </div>
                <div class="d-flex align-items-center me-1 my-2">
                    <a href="#" class="btn btn-icon btn-active-light-primary btn-sm me-2" data-bs-toggle="tooltip" title="Favorite">
                        <i class="fas fa-heart fs-6"></i>
                    </a>
                    <a href="#" class="btn btn-icon btn-active-light-primary btn-sm me-2" data-bs-toggle="tooltip" title="Delete">
                        <i class="fas fa-trash fs-6"></i>
                    </a>
                </div>
            </div>
            <!--end::Toolbar-->
            <!--begin::Pagination-->
            <div class="d-flex align-items-center justify-content-sm-end text-end my-2">
                <!--begin::Per Page Dropdown-->
                <div class="d-flex align-items-center me-2">
                    <span class="text-muted fw-bold me-2">1/247</span>
                </div>
                <!--end::Per Page Dropdown-->
                <!--begin::Arrow Buttons-->
                <a href="#" class="btn btn-icon btn-active-light-primary btn-sm me-2" data-bs-toggle="tooltip" title="صفحه قبل" data-bs-container="body">
                    <i class="fas fa-chevron-right fs-6"></i>
                </a>
                <a href="#" class="btn btn-icon btn-active-light-primary btn-sm me-2" data-bs-toggle="tooltip" title="صفحه بعد" data-bs-container="body">
                    <i class="fas fa-chevron-left fs-6"></i>
                </a>
                <!--end::Arrow Buttons-->
            </div>
            <!--end::Pagination-->
        </div>
        <!--end::Header-->
        <!--begin::Body-->
        <div class="card-body px-0 overflow-auto">
            <!--begin::Items-->
            <div class="min-w-700px" data-inbox="list">

                @forelse($all_user_notifications as $notification)
                    <!--begin::Item-->
                        <div class="d-flex align-items-start bg-hover-light card-px py-3" data-inbox="message">
                            <!--begin::Toolbar-->
                            <div class="d-flex align-items-center">
                                <!--begin::Actions-->
                                <div class="d-flex align-items-center me-3" data-inbox="actions">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                        <input class="form-check-input" type="checkbox" value="1" />
                                    </div>
                                    <a href="#" class="btn btn-icon btn-sm text-hover-warning active" data-bs-toggle="tooltip" data-bs-placement="right" title="Pin">
																<span class="svg-icon svg-icon-muted svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
																	<path d="M11.6734943,8.3307728 L14.9993074,6.09979492 L14.1213255,5.22181303 C13.7308012,4.83128874 13.7308012,4.19812376 14.1213255,3.80759947 L15.535539,2.39338591 C15.9260633,2.00286161 16.5592283,2.00286161 16.9497526,2.39338591 L22.6066068,8.05024016 C22.9971311,8.44076445 22.9971311,9.07392943 22.6066068,9.46445372 L21.1923933,10.8786673 C20.801869,11.2691916 20.168704,11.2691916 19.7781797,10.8786673 L18.9002333,10.0007208 L16.6692373,13.3265608 C16.9264145,14.2523264 16.9984943,15.2320236 16.8664372,16.2092466 L16.4344698,19.4058049 C16.360509,19.9531149 15.8568695,20.3368403 15.3095595,20.2628795 C15.0925691,20.2335564 14.8912006,20.1338238 14.7363706,19.9789938 L5.02099894,10.2636221 C4.63047465,9.87309784 4.63047465,9.23993286 5.02099894,8.84940857 C5.17582897,8.69457854 5.37719743,8.59484594 5.59418783,8.56552292 L8.79074617,8.13355557 C9.76799113,8.00149544 10.7477104,8.0735815 11.6734943,8.3307728 Z" fill="#000000"/>
																	<polygon fill="#000000" opacity="0.3" transform="translate(7.050253, 17.949747) rotate(-315.000000) translate(-7.050253, -17.949747) " points="5.55025253 13.9497475 5.55025253 19.6640332 7.05025253 21.9497475 8.55025253 19.6640332 8.55025253 13.9497475"/>
																</svg></span>
                                    </a>
                                </div>
                                <!--end::Actions-->
                                <!--begin::Author-->
                                <div class="d-flex align-items-center flex-wrap w-xxl-200px me-3" data-bs-toggle="view">
                                    <!--begin::Symbol-->
                                    <div class="symbol symbol-40px me-4">
																<span class="symbol-label bg-light">
																	<img src="{{ asset('assets/media/svg/avatars/035-boy-15.svg') }}" class="h-75 align-self-end" alt="" />
																</span>
                                    </div>
                                    <!--end::Symbol-->
                                    <a href="#" class="fw-bold text-gray-800 text-hover-primary">{{$notification->data['type'] or "اطلاعیه"}}</a>
                                </div>
                                <!--end::Author-->
                            </div>
                            <!--end::Toolbar-->
                            <!--begin::Info-->
                            <div class="flex-grow-1 mt-2 me-2" data-bs-toggle="view">
                                <div>
                                    @if($notification->data['type'] == "ticket_submit_comment")
                                    <span class="fw-bolder fs-6 me-2">به تیکت شما پاسخ داده شد</span>
                                    @endif
                                </div>
                                <div class="mt-2">
                                    <span class="badge badge-light-primary me-1">inbox</span>
                                    <span class="badge badge-light-danger">task</span>
                                </div>
                            </div>
                            <!--end::Info-->
                            <!--begin::Datetime-->
                            <div class="mt-2 me-3 fw-bolder w-50px text-end" data-bs-toggle="view">{{jdate($notification->created_at)->format('Y/m/d')}}</div>
                            <!--end::Datetime-->
                        </div>
                        <!--end::Item-->
                @empty
                    اطلاعیه ای موجود نیست
                @endforelse


            </div>
            <!--end::Items-->
        </div>
        <!--end::Body-->
    </div>


</div>
