<div>
    <!--begin::Modal-->
    <div class="modal fade" tabindex="-1" id="kt_modal_file_share" wire:ignore.self>
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">اشتراک‌گذاری فایل: {{ $fileName }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                         aria-label="Close">
                        <span class="svg-icon svg-icon-2x"></span>
                    </div>
                </div>

                <div class="modal-body">
                    @if($isLoading)
                        <div class="text-center py-10">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">در حال بارگذاری...</span>
                            </div>
                            <p class="mt-3 text-muted">در حال پردازش...</p>
                        </div>
                    @else
                        <!--begin::Tabs-->
                        <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6" role="tablist">
                            <li class="nav-item" role="presentation">
                                <a class="nav-link active" data-bs-toggle="tab" href="#kt_tab_email_share" role="tab"
                                   aria-selected="true">
                                <span class="svg-icon svg-icon-2 me-2">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3" d="M8 8C8 7.4 8.4 7 9 7H16V3C16 2.4 15.6 2 15 2H3C2.4 2 2 2.4 2 3V13C2 13.6 2.4 14 3 14H5V16.1C5 16.8 5.79999 17.1 6.29999 16.6L8 14.9V8Z" fill="currentColor"/>
<path d="M22 8V18C22 18.6 21.6 19 21 19H19V21.1C19 21.8 18.2 22.1 17.7 21.6L15 18.9H9C8.4 18.9 8 18.5 8 17.9V7.90002C8 7.30002 8.4 6.90002 9 6.90002H21C21.6 7.00002 22 7.4 22 8ZM19 11C19 10.4 18.6 10 18 10H12C11.4 10 11 10.4 11 11C11 11.6 11.4 12 12 12H18C18.6 12 19 11.6 19 11ZM17 15C17 14.4 16.6 14 16 14H12C11.4 14 11 14.4 11 15C11 15.6 11.4 16 12 16H16C16.6 16 17 15.6 17 15Z" fill="currentColor"/>
</svg>
                                </span>
                                    اشتراک با ایمیل
                                </a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_public_link" role="tab"
                                   aria-selected="false">
                                <span class="svg-icon svg-icon-2 me-2">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path opacity="0.3"
                                              d="M18 2H9C7.34315 2 6 3.34315 6 5H8C8 4.44772 8.44772 4 9 4H18C18.5523 4 19 4.44772 19 5V16C19 16.5523 18.5523 17 18 17V19C19.6569 19 21 17.6569 21 16V5C21 3.34315 19.6569 2 18 2Z"
                                              fill="currentColor"/>
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                              d="M14.7857 7.125H6.21429C5.62255 7.125 5.14286 7.6007 5.14286 8.1875V18.8125C5.14286 19.3993 5.62255 19.875 6.21429 19.875H14.7857C15.3774 19.875 15.8571 19.3993 15.8571 18.8125V8.1875C15.8571 7.6007 15.3774 7.125 14.7857 7.125ZM6.21429 5C4.43908 5 3 6.42709 3 8.1875V18.8125C3 20.5729 4.43909 22 6.21429 22H14.7857C16.5609 22 18 20.5729 18 18.8125V8.1875C18 6.42709 16.5609 5 14.7857 5H6.21429Z"
                                              fill="currentColor"/>
                                    </svg>
                                </span>
                                    لینک عمومی
                                </a>
                            </li>
                        </ul>
                        <!--end::Tabs-->

                        <!--begin::Tab Content-->
                        <div class="tab-content" id="myTabContent">
                            <!--begin::Email Share Tab-->
                            <div class="tab-pane fade show active" id="kt_tab_email_share" role="tabpanel">
                                <div class="mb-5">
                                    <label class="form-label fw-semibold">ایمیل‌های اشتراک‌گذاری شده:</label>
                                    @if(count($sharedEmails) > 0)
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <span class="text-muted fs-7">تعداد: {{ count($sharedEmails) }} ایمیل</span>
                                            {{--                                        <button type="button" class="btn btn-sm btn-light-danger"--}}
                                            {{--                                                wire:click="clearAllEmailShares"--}}
                                            {{--                                                wire:loading.attr="disabled"--}}
                                            {{--                                                title="حذف همه دسترسی‌ها">--}}
                                            {{--                                            <span wire:loading.remove wire:target="clearAllEmailShares">حذف همه</span>--}}
                                            {{--                                            <span wire:loading wire:target="clearAllEmailShares">--}}
                                            {{--                                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>--}}
                                            {{--                                            </span>--}}
                                            {{--                                        </button>--}}
                                        </div>
                                        <div class="d-flex flex-wrap gap-2 mb-3">
                                            @foreach($sharedEmails as $email)
                                                <div class="badge badge-light-primary d-flex align-items-center">
                                                    <span class="me-2">{{ $email }}</span>
                                                    <button type="button"
                                                            class="btn btn-sm btn-icon btn-active-light-danger"
                                                            wire:click="removeEmailShare('{{ $email }}')"
                                                            title="حذف دسترسی">
                                                           <span wire:loading
                                                                 wire:target="removeEmailShare('{{ $email }}')">
                                                            <span class="spinner-border spinner-border-sm"
                                                                  role="status"
                                                                  aria-hidden="true"></span>
                                                           </span>

                                                        <span class="svg-icon svg-icon-1"
                                                              wire:loading.remove
                                                              wire:target="removeEmailShare('{{ $email }}')">
                                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                                 xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z"
                                                                    fill="currentColor"/>
                                                                <path opacity="0.5"
                                                                      d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z"
                                                                      fill="currentColor"/>
                                                                <path opacity="0.5"
                                                                      d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z"
                                                                      fill="currentColor"/>
                                                            </svg>
                                                        </span>
                                                    </button>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <div class="text-muted">هیچ ایمیلی اشتراک‌گذاری نشده است.</div>
                                    @endif
                                </div>

                                <div class="mb-5">
                                    <label class="form-label fw-semibold">افزودن ایمیل جدید:</label>
                                    <div class="input-group direction-ltr">
                                        <input type="text" class="form-control" wire:model="emails"
                                               placeholder="ایمیل‌ها را با کاما جدا کنید (مثال: <EMAIL>, <EMAIL>)">
                                        <button class="btn btn-primary d-flex align-items-center" type="button" wire:click="shareWithEmails"
                                                wire:loading.attr="disabled">
                                            <span wire:loading.remove wire:target="shareWithEmails">اشتراک‌گذاری</span>
                                            <span class="spinner-border spinner-border-sm" role="status"  wire:loading wire:target="shareWithEmails"
                                                  aria-hidden="true">
                                        </span>
                                        </button>
                                    </div>
                                    <div class="form-text">ایمیل‌ها را با کاما (,) جدا کنید</div>
                                </div>
                            </div>
                            <!--end::Email Share Tab-->

                            <!--begin::Public Link Tab-->
                            <div class="tab-pane fade" id="kt_tab_public_link" role="tabpanel">
                                <div class="mb-5">
                                    @if($isPublic)
                                        <div class="alert alert-warning d-flex align-items-center p-5 mb-5">
                                        <span class="svg-icon svg-icon-muted svg-icon-2hx me-3"><svg width="24"
                                                                                                     height="24"
                                                                                                     viewBox="0 0 24 24"
                                                                                                     fill="none"
                                                                                                     xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3"
      d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z"
      fill="currentColor"/>
<path
    d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z"
    fill="currentColor"/>
</svg>
</span>
                                            <div class="d-flex flex-column">
                                                <span class="fw-bold">لینک عمومی فعال است</span>
                                                <span class="">هر کسی با این لینک می‌تواند فایل را مشاهده کند</span>
                                            </div>
                                        </div>

                                        <div class="mb-5">
                                            <label class="form-label fw-semibold">لینک اشتراک‌گذاری:</label>
                                            <div class="input-group direction-ltr">
                                                <input type="text" class="form-control" value="{{ $publicLink }}"
                                                       readonly>
                                                <button class="btn btn-secondary" type="button"
                                                        wire:click="copyToClipboard('{{ $publicLink }}')">
                                                <span class="svg-icon svg-icon-2 me-2">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                         xmlns="http://www.w3.org/2000/svg">
                                                        <path opacity="0.3"
                                                              d="M18 2H9C7.34315 2 6 3.34315 6 5H8C8 4.44772 8.44772 4 9 4H18C18.5523 4 19 4.44772 19 5V16C19 16.5523 18.5523 17 18 17V19C19.6569 19 21 17.6569 21 16V5C21 3.34315 19.6569 2 18 2Z"
                                                              fill="currentColor"/>
                                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                                              d="M14.7857 7.125H6.21429C5.62255 7.125 5.14286 7.6007 5.14286 8.1875V18.8125C5.14286 19.3993 5.62255 19.875 6.21429 19.875H14.7857C15.3774 19.875 15.8571 19.3993 15.8571 18.8125V8.1875C15.8571 7.6007 15.3774 7.125 14.7857 7.125ZM6.21429 5C4.43908 5 3 6.42709 3 8.1875V18.8125C3 20.5729 4.43909 22 6.21429 22H14.7857C16.5609 22 18 20.5729 18 18.8125V8.1875C18 6.42709 16.5609 5 14.7857 5H6.21429Z"
                                                              fill="currentColor"/>
                                                    </svg>
                                                </span>
                                                    کپی لینک
                                                </button>
                                            </div>
                                        </div>

                                        <button class="btn btn-danger" type="button" wire:click="removePublicLink"
                                                wire:loading.attr="disabled">
                                            <span wire:loading.remove
                                                  wire:target="removePublicLink">حذف لینک عمومی</span>
                                            <span wire:loading wire:target="removePublicLink">
                                            <span class="spinner-border spinner-border-sm" role="status"
                                                  aria-hidden="true"></span>
                                        </span>
                                        </button>
                                    @else
                                        <div class="alert alert-primary d-flex align-items-center p-5 mb-5">
                                       <span class="svg-icon svg-icon-muted svg-icon-2hx me-3"><svg width="24"
                                                                                                    height="24"
                                                                                                    viewBox="0 0 24 24"
                                                                                                    fill="none"
                                                                                                    xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3"
      d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z"
      fill="currentColor"/>
<rect x="9" y="13.0283" width="7.3536" height="1.2256" rx="0.6128" transform="rotate(-45 9 13.0283)"
      fill="currentColor"/>
<rect x="9.86664" y="7.93359" width="7.3536" height="1.2256" rx="0.6128" transform="rotate(45 9.86664 7.93359)"
      fill="currentColor"/>
</svg>
</span>
                                            <div class="d-flex flex-column">
                                                <span class="fw-bold">لینک عمومی غیرفعال است</span>
                                                <span class="">فقط ایمیل شما دسترسی دریافت فایل را دارد. در صورت تمایل میتوانید دسترسی توسط لینک را فعال نمایید.</span>
                                            </div>

                                        </div>
                                        <button class="btn btn-primary mx-auto" type="button"
                                                wire:click="createPublicLink"
                                                wire:loading.attr="disabled">
                                            <span wire:loading.remove
                                                  wire:target="createPublicLink">ایجاد لینک عمومی</span>
                                            <span wire:loading wire:target="createPublicLink">
                                            <span class="spinner-border spinner-border-sm" role="status"
                                                  aria-hidden="true"></span>
                                        </span>
                                        </button>

                                    @endif
                                </div>
                            </div>
                            <!--end::Public Link Tab-->
                        </div>
                        <!--end::Tab Content-->
                    @endif
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">بستن</button>
                </div>
            </div>
        </div>
    </div>
    <!--end::Modal-->

    <script>
        document.addEventListener('livewire:init', function () {
            $('#kt_modal_file_share').on('hidden.bs.modal', function () {
                Livewire.dispatch('closeShareModal');
            });

            Livewire.on('openShareModal', (data) => {
                $('#kt_modal_file_share').modal('show');
            });

            Livewire.on('closeShareModal', () => {
                $('#kt_modal_file_share').modal('hide');
            });

            Livewire.on('copyToClipboard', (data) => {
                navigator.clipboard.writeText(data[0].text).then(function () {
                    console.log('Link copied to clipboard');
                });
            });
        });
    </script>
</div>
