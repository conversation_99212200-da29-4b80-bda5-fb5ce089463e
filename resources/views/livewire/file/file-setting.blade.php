{{-- To attain knowledge, add things every day; To attain wisdom, subtract things every day. --}}
<div
    class="menu menu-sub menu-sub-dropdown w-250px w-md-300px direction-rtl text-align-right"
    data-kt-menu="true" id="kt_menu_select{{$fileId}}" wire:ignore.self>
    <!--begin::Header-->
    <div class="px-7 py-5">
        <div class="fs-5 text-gray-900 fw-bold">تنظیمات فایل</div>
    </div>
    <!--end::Header-->

    <!--begin::Menu separator-->
    <div class="separator border-gray-200"></div>
    <!--end::Menu separator-->

    <!--begin::Form-->
    <div class="px-7 py-5">
        <!--begin::Input group-->
        <div class="mb-10">

            <!--begin::Label-->
            <label class="form-label fw-semibold">عنوان:</label>
            <!--end::Label-->

            <!--begin::Input-->
            <div>
                <input class="direction-ltr convertor form-control form-control-lg form-control-solid" type="text"
                       wire:model.blur="newFileName"/>
            </div>
            <!--end::Input-->
        </div>
        {{--        <div class="mb-10">--}}
        {{--            <!--begin::Label-->--}}
        {{--            <label class="form-label fw-semibold">وضعیت:</label>--}}
        {{--            <!--end::Label-->--}}

        {{--            <!--begin::Input-->--}}
        {{--            <div>--}}
        {{--                <select class="form-select form-select-solid"--}}
        {{--                        data-kt-select2="true" multiple--}}
        {{--                        data-close-on-select="false"--}}
        {{--                        data-placeholder="Select option"--}}
        {{--                        data-dropdown-parent="#kt_menu_select{{$fileId}}"--}}
        {{--                        data-allow-clear="true">--}}
        {{--                    <option></option>--}}
        {{--                    <option value="1">Approved</option>--}}
        {{--                    <option value="2">Pending</option>--}}
        {{--                    <option value="2">In Process</option>--}}
        {{--                    <option value="2">Rejected</option>--}}
        {{--                </select>--}}
        {{--            </div>--}}
        {{--            <!--end::Input-->--}}
        {{--        </div>--}}
        <!--end::Input group-->

        <!--begin::Input group-->
        <div class="mb-10">
            <!--begin::Label-->
            <label class="form-label fw-semibold">نمایش در:</label>
            <!--end::Label-->

            <!--begin::Options-->
            <div class="d-flex">
                <!--begin::Options-->
                <label
                    class="form-check form-check-sm form-check-custom form-check-solid me-5">
                    <input class="form-check-input" type="checkbox"
                           wire:model.live="showInPublic"/>
                    <span class="form-check-label">  صفحه عمومی    </span>
                </label>
                <!--end::Options-->
            </div>
        </div>

        {{--                <!--begin::Options-->--}}
        {{--                <label--}}
        {{--                    class="form-check form-check-sm form-check-custom form-check-solid">--}}
        {{--                    <input class="form-check-input" type="checkbox"--}}
        {{--                           value="2" checked="checked"/>--}}
        {{--                    <span class="form-check-label">--}}
        {{--                                                                            سازمان--}}
        {{--                                                                        </span>--}}
        {{--                </label>--}}
        {{--                <!--end::Options-->--}}
        {{--            </div>--}}
        {{--            <!--end::Options-->--}}
        {{--        </div>--}}
        <!--end::Input group-->
        @if($activeDeletion)
            <div class="separator my-3 opacity-75"></div>

            <div class="mb-10">

                <!--begin::Label-->
                <label class="form-label fw-semibold">جهت حذف عبارت "delete" را تایپ کنید:</label>
                <!--end::Label-->

                <!--begin::Input-->
                <div>
                    <input class="direction-ltr convertor form-control form-control-lg form-control-solid" type="text"
                           wire:model.blur="deleteSafe"/>
                    <span class="invalid-feedback d-block" role="alert">
                                            <strong>این عملکرد غیر قابل بازگشت است</strong>
                                </span>
                </div>
                <button
                    class="btn btn-sm btn-danger btn-active-light-primary me-2 mt-2"
                    wire:loading.class="disabled" wire:target="runDeletion"
                    wire:click="runDeletion"
                    data-kt-menu-dismiss="false">
                    <div wire:loading wire:target="runDeletion">
                        <span class="spinner-border spinner-border-sm align-middle m-1"></span>
                    </div>
                    تایید حذف
                </button>
                <!--end::Input-->
            </div>
        @endif

        <!--begin::Actions-->
        <div class="d-flex justify-content-end">
            @if(!$activeDeletion)
                <button
                    class="btn btn-sm btn-danger btn-active-light-primary me-2"
                    wire:click="activeDeletion"
                    data-kt-menu-dismiss="false">
                    حذف فایل
                </button>
            @endif

            <button wire:click="submit" class="btn btn-sm btn-primary"
                    wire:loading.class="disabled" wire:target="submit"
                    data-kt-menu-dismiss="false"
            >
                <div wire:loading wire:target="submit">
                    <span class="spinner-border spinner-border-sm align-middle m-1"></span>
                </div>
                ذخیره
            </button>
        </div>
        <!--end::Actions-->
    </div>
    <!--end::Form-->
</div>
