<div>
    {{-- Stop trying to control. --}}
    <div class="modal fade" tabindex="-1" id="open_report_modal" wire:ignore>
        <div class="modal-dialog direction-rtl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ثبت گزارش</h5>

                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                         aria-label="Close">
                        <span class="svg-icon svg-icon-2x"></span>
                    </div>
                    <!--end::Close-->
                </div>

                <div class="modal-body">

                @if(\App\Models\Report\Report::CATEGORY_SIZE)
                    <!--begin::Form Group-->
                        <div class="mb-10 position-relative">
                            <select class="select-arrow form-select form-select-lg form-select-solid"
                                    dir="rtl"
                                    data-control="select2"
                                    onchange="@this.category = $(this).val(); "
                                    data-placeholder="انتخاب کنید">
                                <option></option>
                                @for($i = 1; $i<=\App\Models\Report\Report::CATEGORY_SIZE ; $i++)
                                    <option
                                        value="{{$i}}">{{\App\Models\Report\Report::reportCategoryIdAttribute($i)}}</option>
                                @endfor
                            </select>
                            <br />
                            <textarea class="textarea form-control form-control-solid" wire:model.blur="comment" placeholder="لطفا کمی در مورد مشکل توضیح دهید"></textarea>


                        </div>
                    @endif



                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">انصراف</button>
                    <button type="button" class="btn btn-primary"  wire:click="create_report">ارسال</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
             Livewire.on('open_report_modal', () => {
                $('#open_report_modal').modal('show');
            });
        });

    </script>

</div>
