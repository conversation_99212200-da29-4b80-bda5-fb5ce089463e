<div class="d-flex flex-column flex-md-row">
    {{-- To attain knowledge, add things every day; To attain wisdom, subtract things every day. --}}
    <style>
        .image-max-width img {
            max-width: 100%;
            border-radius: 10px;
        }
    </style>
    <!--begin::Layout-->
    <div class="flex-md-row-fluid ms-md-12">
        <!--begin::Card-->
        <div class="card direction-rtl">
            <div wire:loading wire:target="showContent" class="card-body py-10 d-flex items-center justify-center"
                 style="min-height: 200px; display: none !important; align-items: center; justify-content: center;">
                <!-- Progress enabled state -->
                <button type="button" class="btn btn-light" data-kt-indicator="on">
                    <span class="indicator-progress">
                        در حال بارگزاری... <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                    </span>
                </button>
            </div>
            @if(empty($content))
                <div wire:loading.remove="showContent()" class="card-body py-10 d-flex items-center content-center"
                >
                    <!-- Progress enabled state -->
                    <span type="button" class="btn btn-light" data-kt-indicator="on">
                        لطفا یک موضوع را انتخاب نمایید.
                    </span>
                </div>
            @else
                <div wire:loading.remove="showContent()" class="card-body py-10">
                    <h2 class="text-gray-900 fw-bold fs-1 mb-5">
                        {{$content->title}}
                    </h2>
                    <div class="video my-5">
                        {!! $content->video !!}
                    </div>
                    <div class="p-4 text-justify image-max-width">
                        {{ Illuminate\Mail\Markdown::parse($content->content) }}
                    </div>

                    <!--begin::Accordion-->
                    <div class="accordion accordion-icon-toggle" id="kt_accordion_1">
                        @foreach($content->faq as $faq)
                            <!--begin::Item-->
                            <div class="mb-5">
                                <!--begin::Header-->
                                <div class="accordion-header py-3 d-flex collapsed" data-bs-toggle="collapse"
                                     data-bs-target="#kt_accordion_1_item_{{$loop->index}}" aria-expanded="false">
                                    <span class="accordion-icon">
                                       <span class="svg-icon svg-icon-muted svg-icon-1"><svg width="24" height="24"
                                                                                             viewBox="0 0 24 24"
                                                                                             fill="none"
                                                                                             xmlns="http://www.w3.org/2000/svg">
<rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
<path
    d="M11.276 13.654C11.276 13.2713 11.3367 12.9447 11.458 12.674C11.5887 12.394 11.738 12.1653 11.906 11.988C12.0833 11.8107 12.3167 11.61 12.606 11.386C12.942 11.1247 13.1893 10.896 13.348 10.7C13.5067 10.4947 13.586 10.2427 13.586 9.944C13.586 9.636 13.4833 9.356 13.278 9.104C13.082 8.84267 12.69 8.712 12.102 8.712C11.486 8.712 11.066 8.866 10.842 9.174C10.6273 9.482 10.52 9.82267 10.52 10.196L10.534 10.574H8.826C8.78867 10.3967 8.77 10.2333 8.77 10.084C8.77 9.552 8.90067 9.07133 9.162 8.642C9.42333 8.20333 9.81067 7.858 10.324 7.606C10.8467 7.354 11.4813 7.228 12.228 7.228C13.1987 7.228 13.9687 7.44733 14.538 7.886C15.1073 8.31533 15.392 8.92667 15.392 9.72C15.392 10.168 15.322 10.5507 15.182 10.868C15.042 11.1853 14.874 11.442 14.678 11.638C14.482 11.834 14.2253 12.0533 13.908 12.296C13.544 12.576 13.2733 12.8233 13.096 13.038C12.928 13.2527 12.844 13.528 12.844 13.864V14.326H11.276V13.654ZM11.192 15.222H12.928V17H11.192V15.222Z"
    fill="currentColor"/>
</svg>
</span>
                                    </span>
                                    <h3 class="fs-4 text-gray-800 fw-semibold mb-0 ms-4">
                                        {{$faq->title}}
                                    </h3>
                                </div>
                                <!--end::Header-->

                                <!--begin::Body-->
                                <div id="kt_accordion_1_item_{{$loop->index}}"
                                     class="fs-6 ps-10 collapse image-max-width"
                                     style="" data-bs-parent="#kt_accordion_1">
                                    <div class="mb-5">
                                        {{ Illuminate\Mail\Markdown::parse($faq->content) }}
                                    </div>

                                </div>
                                <!--end::Body-->
                            </div>
                            <!--end::Item-->
                        @endforeach
                    </div>
                    <!--end::Accordion-->
                </div>
            @endif

        </div>
        <!--end::Card-->
    </div>
    <!--end::Layout-->
    <!--begin::Aside-->
    <div class="flex-column flex-md-row-auto w-100 w-md-250px w-xxl-350px">
        <div class="card mb-10 mb-md-0 direction-rtl d-none d-md-block">
            @include('livewire.dashboard.instruct-item')
        </div>

            @include('livewire.dashboard.instruct-mobile-menu')

    </div>
    <!--end::Aside-->

</div>
