
<!--begin::List Widget 2-->
    <!--begin::Body-->
    <div class="card-body py-10 px-6">
        <!--begin::Search Input-->
        <div class="d-flex flex-column mb-10 px-3">
            <!--begin::Form-->

            <div class="input-group input-group-solid" id="kt_chat_aside_search">
                            <span class="input-group-text" id="basic-addon1">
                                  <!--begin::Svg Icon | path: icons/stockholm/General/Search.svg-->
                            <span class="svg-icon svg-icon-1 svg-icon-dark">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                     width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <rect x="0" y="0" width="24" height="24"/>
                                        <path
                                            d="M14.2928932,16.7071068 C13.9023689,16.3165825 13.9023689,15.6834175 14.2928932,15.2928932 C14.6834175,14.9023689 15.3165825,14.9023689 15.7071068,15.2928932 L19.7071068,19.2928932 C20.0976311,19.6834175 20.0976311,20.3165825 19.7071068,20.7071068 C19.3165825,21.0976311 18.6834175,21.0976311 18.2928932,20.7071068 L14.2928932,16.7071068 Z"
                                            fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                                        <path
                                            d="M11,16 C13.7614237,16 16,13.7614237 16,11 C16,8.23857625 13.7614237,6 11,6 C8.23857625,6 6,8.23857625 6,11 C6,13.7614237 8.23857625,16 11,16 Z M11,18 C7.13400675,18 4,14.8659932 4,11 C4,7.13400675 7.13400675,4 11,4 C14.8659932,4 18,7.13400675 18,11 C18,14.8659932 14.8659932,18 11,18 Z"
                                            fill="#000000" fill-rule="nonzero"/>
                                    </g>
                                </svg>
                            </span>
                                <!--end::Svg Icon-->
                            </span>
                <input type="text" class="form-control ps-0 py-4 h-auto" wire:model.blur="search"
                       placeholder="جستجو">
                <div wire:loading wire:target="search" style=" position: absolute; top: 16px; left: 16px; ">
                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </div>
            </div>

            <!--end::Form-->
        </div>
        <!--end::Search Input-->
        @forelse($this->instructors as $category => $instructors)
            <!--begin::Authors List-->
            <ul class="menu menu-column menu-rounded menu-gray-600 menu-hover-bg-light-primary menu-active-bg-light-primary fw-semibold mb-10">
                <li class="menu-content fw-semibold pb-2 px-3">
                    <span class="fs-3 fw-bold">{{$category}}</span>
                </li>
                @if(!empty($instructors) || !$instructors->isEmpty())
                    @foreach($instructors as $instructor)
                        @if(empty($instructor))
                            @continue
                        @endif
                        <li class="menu-item px-3 pb-1">
                            <a href="/instruction?slug={{$instructor['slug']}}" onclick="return false;" wire:click="showContent({{$instructor['id']}})"
                               class="menu-link fs-6 px-3 {{$instructor['id'] == $content?->id ? 'active' : ''}}">
                                {{$instructor['title']}}
                            </a>
                        </li>
                    @endforeach
                @endif
            </ul>
        @empty
            نتیجه ای یافت نشد
            <!--end::Authors List-->
        @endforelse


    </div>
    <!--end::Body-->

<!--end::List Widget 2-->
