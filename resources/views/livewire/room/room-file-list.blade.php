<div>
    <!--begin::Table Widget 1-->
    <div class="card card-stretch mb-5 mb-xxl-8">
        <!--begin::Header-->
        <div class="card-header border-0 pt-5">
            <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bolder text-dark fs-3">فایل های مرتبط این اتاق ورژن قدیم</span>
                <span class="text-muted mt-2 fw-bold fs-6"></span>
            </h3>

            <div class="card-toolbar">

                <div>
                    <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                            data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                    <span class="svg-icon svg-icon-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                                <path opacity="0.3"
                                                                      d="M4.7 17.3V7.7C4.7 6.59543 5.59543 5.7 6.7 5.7H9.8C10.2694 5.7 10.65 5.31944 10.65 4.85C10.65 4.38056 10.2694 4 9.8 4H5C3.89543 4 3 4.89543 3 6V19C3 20.1046 3.89543 21 5 21H18C19.1046 21 20 20.1046 20 19V14.2C20 13.7306 19.6194 13.35 19.15 13.35C18.6806 13.35 18.3 13.7306 18.3 14.2V17.3C18.3 18.4046 17.4046 19.3 16.3 19.3H6.7C5.59543 19.3 4.7 18.4046 4.7 17.3Z"
                                                                      fill="currentColor"></path>
                                                    <rect x="21.9497" y="3.46448" width="13" height="2" rx="1"
                                                          transform="rotate(135 21.9497 3.46448)"
                                                          fill="currentColor"></rect>
                                                    <path
                                                        d="M19.8284 4.97161L19.8284 9.93937C19.8284 10.5252 20.3033 11 20.8891 11C21.4749 11 21.9497 10.5252 21.9497 9.93937L21.9497 3.05029C21.9497 2.498 21.502 2.05028 20.9497 2.05028L14.0607 2.05027C13.4749 2.05027 13 2.52514 13 3.11094C13 3.69673 13.4749 4.17161 14.0607 4.17161L19.0284 4.17161C19.4702 4.17161 19.8284 4.52978 19.8284 4.97161Z"
                                                        fill="currentColor"></path>

                                                        </svg>
                    </span>
                        <!--end::Svg Icon-->
                    </button>
                    <!--begin::Menu-->
                    <div
                        class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold w-300px"
                        data-kt-menu="true"
                    >
                        <div class="menu-item px-3">
                            <div class="menu-content fs-6 text-gray-900 fw-bold px-3 py-4">انتقال دست جمعی</div>
                        </div>

                        <div class="separator mb-3 opacity-75"></div>
                        @if($finalFilesCount >= 1)
                            <div class="menu-item px-3">
                                <div class="separator my-3 opacity-75"></div>
                                <div class="mb-10">
                                    <!--begin::Label-->
                                    <label class="form-label fw-semibold">
                                        جهت انتقال {{$finalFilesCount}} فایل عبارت "move" را تایپ کنید:</label>
                                    <!--end::Label-->

                                    <!--begin::Input-->
                                    <div>
                                        <input
                                            class="direction-ltr convertor form-control form-control-lg form-control-solid"
                                            type="text"
                                            data-kt-menu-dismiss="false"
                                            wire:model="deleteSafeGroup"/>
                                        <span class="invalid-feedback d-block" role="alert">
                                            <strong>این عملکرد غیر قابل بازگشت است</strong>
                                        </span>
                                    </div>
                                    <button
                                        class="btn btn-sm btn-danger btn-active-light-primary me-2 mt-2"
                                        wire:loading.class="disabled" wire:target="moveGroupFiles"
                                        onclick=" @this.set('selectedFiles', JSON.stringify(checkedValues));@this.moveGroupFiles()"
                                        data-kt-menu-dismiss="false"
                                        style=" float: left; margin-bottom: 10px; ">
                                        <div wire:loading wire:target="moveGroupFiles">
                                            <span class="spinner-border spinner-border-sm align-middle m-1"></span>
                                        </div>
                                        تایید انتقال
                                    </button>
                                    <!--end::Input-->
                                </div>
                            </div>
                        @else
                            <p class="text-muted fw-bold d-block mt-1 direction-rtl px-3">ابتدا فایل ها را انتخاب
                                نمایید</p>
                        @endif
                    </div>
                    <!--end::Menu-->
                </div>
                <div>
                    <!--begin::Dropdown-->
                    <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                            data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                    <span class="svg-icon svg-icon-1">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z"
                                fill="currentColor"/>
                            <path opacity="0.5"
                                  d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z"
                                  fill="currentColor"/>
                            <path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z"
                                  fill="currentColor"/>
                        </svg>
                    </span>
                        <!--end::Svg Icon-->

                    </button>
                    <!--begin::Menu-->
                    <div
                        class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold w-300px"
                        data-kt-menu="true"
                    >
                        <div class="menu-item px-3">
                            <div class="menu-content fs-6 text-gray-900 fw-bold px-3 py-4">حذف فایل ها</div>
                        </div>

                        <div class="separator mb-3 opacity-75"></div>
                        @if($finalFilesCount >= 1)
                            <div class="menu-item px-3">
                                <div class="separator my-3 opacity-75"></div>
                                <div class="mb-10">
                                    <!--begin::Label-->
                                    <label class="form-label fw-semibold">جهت حذف {{$finalFilesCount}} فایل عبارت
                                        "delete"
                                        را تایپ کنید:</label>
                                    <!--end::Label-->

                                    <!--begin::Input-->
                                    <div>
                                        <input
                                            class="direction-ltr convertor form-control form-control-lg form-control-solid"
                                            type="text"
                                            data-kt-menu-dismiss="false"
                                            wire:model="deleteSafeGroup"/>
                                        <span class="invalid-feedback d-block" role="alert">
                                            <strong>این عملکرد غیر قابل بازگشت است</strong>
                                </span>
                                    </div>
                                    <button
                                        class="btn btn-sm btn-danger btn-active-light-primary me-2 mt-2"
                                        wire:loading.class="disabled" wire:target="removeGroupFiles"
                                        onclick=" @this.set('selectedFiles', JSON.stringify(checkedValues));@this.removeGroupFiles()"
                                        data-kt-menu-dismiss="false"
                                        style=" float: left; margin-bottom: 10px; ">
                                        <div wire:loading wire:target="removeGroupFiles">
                                            <span class="spinner-border spinner-border-sm align-middle m-1"></span>
                                        </div>
                                        تایید حذف
                                    </button>
                                    <!--end::Input-->
                                </div>
                            </div>
                        @else
                            <p class="text-muted fw-bold d-block mt-1 direction-rtl px-3">ابتدا فایل ها را انتخاب
                                نمایید</p>
                        @endif
                    </div>
                    <!--end::Menu-->
                </div>
            </div>

        </div>
        <!--end::Header-->

        <!--begin::Body-->
        <div class="card-body pt-2 pb-0 mt-n3">
            <div class="alert alert-primary d-flex align-items-center p-5 mb-10">
                <i class="ki-duotone ki-shield-tick fs-2hx text-success me-4">
                    <span class="path1"></span><span class="path2"></span></i>
                <div class="d-flex flex-column">
                    <span>ممکن است پروسه اپلود فایل ها و قرار گیری آن ها در این قسمت بسته به زمان رویداد شما تا ۳ ساعت زمانبر باشد</span>
                </div>
            </div>
            <div class="tab-content mt-5" id="myTabTables1">
                <!--begin::Tap pane-->
                <div class="tab-pane fade show active" id="kt_tab_pane_1_1" role="tabpanel"
                     aria-labelledby="kt_tab_pane_1_1">
                    <!--begin::Table-->
                    @if(!empty($meetingFiles))
                        <div class="table-responsive">
                            <table class="table table-borderless align-middle">
                                <thead>
                                <tr>
                                    <th class="p-0 w-50px"></th>
                                    <th class="p-0 min-w-200px"></th>
                                    <th class="p-0 min-w-100px"></th>
                                    <th class="p-0 min-w-40px">
                                        <div class="d-flex justify-content-end pe-4 ">
                                            <div
                                                class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                                <label class="" for="select-all"> انتخاب همه:</label>
                                                <input class="ms-3  form-check-input select-all" id="select-all"
                                                       type="checkbox">
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                @forelse($meetingFiles as $meetingFile)

                                    <tr id="file_{{$meetingFile->id}}">
                                        <th class="px-0 py-3">
                                            <div class="symbol symbol-65px  me-3">
                                                {!! getIconFromMime($meetingFile->mimeType) !!}
                                            </div>
                                        </th>
                                        <td class="ps-0 text-align-right">
                                            <a href="#"
                                               class="text-gray-800 fw-bolder text-hover-primary fs-6">{{$meetingFile->name}}</a>
                                            <span class="text-muted fw-bold d-block mt-1 direction-rtl">
                                                {{$meetingFile->mimeType}} - {{formatBytes($meetingFile->size)}} -
                                                @php
                                                    try{
                                                        $time =  \Carbon\Carbon::parse(($meetingFile->createdTime));
                                                        echo \Morilog\Jalali\Jalalian::fromCarbon($time)->format('%A, %d %B %Y');
                                                    }catch(Exception $e){

                                                    }
                                                    $showInPublic=true;
                                                    if (!empty($meetingFile->appProperties) && !empty($meetingFile->appProperties['showInPublic']) && $meetingFile->appProperties['showInPublic'] == 'false') {
                                                        $showInPublic= false;
                                                    }

                                                @endphp

                                            </span>
                                        </td>
                                        <td>
                                            {{--                                            <div class="d-flex flex-column w-100 me-3">--}}
                                            {{--                                                <div class="d-flex flex-stack mb-2">--}}
                                            {{--                                            <span class="text-dark me-2 fs-6 fw-bolder">--}}
                                            {{--                                                زمان باقی مانده تا حذف (موقتا غیر فعال)--}}
                                            {{--                                            </span>--}}
                                            {{--                                                </div>--}}
                                            {{--                                                <div class="d-flex align-items-center">--}}
                                            {{--                                                    <div class="progress h-6px  w-100 bg-light-primary">--}}
                                            {{--                                                        <div class="progress-bar bg-primary" role="progressbar"--}}
                                            {{--                                                             style="width: 46%;" aria-valuenow="50" aria-valuemin="0"--}}
                                            {{--                                                             aria-valuemax="100"></div>--}}
                                            {{--                                                    </div>--}}
                                            {{--                                                    <span class="text-muted fs-7 fw-bold ps-3">--}}
                                            {{--                                                46%--}}
                                            {{--                                            </span>--}}
                                            {{--                                                </div>--}}
                                            {{--                                            </div>--}}

                                        </td>

                                        <td class="text-end pe-0">

                                            <div class="d-flex justify-content-around">

                                                <!--begin::Menu wrapper-->
                                                <div class="m-0 relative ">
                                                    <!--begin::Menu toggle-->

                                                    <button type="button"
                                                            class="btn  btn-bg-light btn-active-primary btn-sm"
                                                            data-kt-menu-trigger="click"
                                                            data-kt-menu-placement="bottom-end"
                                                            data-kt-menu-flip="top-end">

                                                        <!--begin::Svg Icon | path: icons/stockholm/Layout/Layout-4-blocks-2.svg-->
                                                        <span class="svg-icon svg-icon-1">

															<svg xmlns="http://www.w3.org/2000/svg"
                                                                 xmlns:xlink="http://www.w3.org/1999/xlink" width="24px"
                                                                 height="24px" viewBox="0 0 24 24" version="1.1">
																<g stroke="none" stroke-width="1" fill="none"
                                                                   fill-rule="evenodd">
																	<rect x="5" y="5" width="5" height="5" rx="1"
                                                                          fill="#000000"/>
																	<rect x="14" y="5" width="5" height="5" rx="1"
                                                                          fill="#000000" opacity="0.3"/>
																	<rect x="5" y="14" width="5" height="5" rx="1"
                                                                          fill="#000000" opacity="0.3"/>
																	<rect x="14" y="14" width="5" height="5" rx="1"
                                                                          fill="#000000" opacity="0.3"/>
																</g>
															</svg>
														</span>
                                                        <!--end::Svg Icon-->
                                                        تنظیمات

                                                    </button>
                                                    <!--end::Menu toggle-->

                                                    <!--begin::Menu dropdown-->
                                                    <livewire:file.file-setting :file-id="$meetingFile->id"
                                                                                :file-name="$meetingFile->name"
                                                                                :licence-id="$licenceId"
                                                                                :meeting-id="$meetingId"
                                                                                :show-in-public="$showInPublic"
                                                                                :wire:key="$meetingFile->id"
                                                    />

                                                    <!--end::Menu dropdown-->
                                                </div>
                                                <!--end::Menu wrapper-->

                                                <button wire:click="downloadLink('{{$meetingFile->id}}')"
                                                        wire:key="mk_{{$meetingFile->id}}"
                                                        class="btn btn-bg-light btn-active-primary btn-sm"
                                                        wire:loading.class="disabled"
                                                        wire:target="downloadLink('{{$meetingFile->id}}')">
                                                    <div wire:loading.remove
                                                         wire:target="downloadLink('{{$meetingFile->id}}')">
                                                        <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                                        <span class="svg-icon svg-icon-4">
                                                         <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                              viewBox="0 0 24 24" fill="none">
                                                                <path opacity="0.3"
                                                                      d="M4.7 17.3V7.7C4.7 6.59543 5.59543 5.7 6.7 5.7H9.8C10.2694 5.7 10.65 5.31944 10.65 4.85C10.65 4.38056 10.2694 4 9.8 4H5C3.89543 4 3 4.89543 3 6V19C3 20.1046 3.89543 21 5 21H18C19.1046 21 20 20.1046 20 19V14.2C20 13.7306 19.6194 13.35 19.15 13.35C18.6806 13.35 18.3 13.7306 18.3 14.2V17.3C18.3 18.4046 17.4046 19.3 16.3 19.3H6.7C5.59543 19.3 4.7 18.4046 4.7 17.3Z"
                                                                      fill="currentColor"/>
                                                    <rect x="21.9497" y="3.46448" width="13" height="2" rx="1"
                                                          transform="rotate(135 21.9497 3.46448)" fill="currentColor"/>
                                                    <path
                                                        d="M19.8284 4.97161L19.8284 9.93937C19.8284 10.5252 20.3033 11 20.8891 11C21.4749 11 21.9497 10.5252 21.9497 9.93937L21.9497 3.05029C21.9497 2.498 21.502 2.05028 20.9497 2.05028L14.0607 2.05027C13.4749 2.05027 13 2.52514 13 3.11094C13 3.69673 13.4749 4.17161 14.0607 4.17161L19.0284 4.17161C19.4702 4.17161 19.8284 4.52978 19.8284 4.97161Z"
                                                        fill="currentColor"/>

                                                        </svg>
                                                            دریافت لینک
                                                            <!--end::Svg Icon-->
                                                </span>
                                                        <!--end::Svg Icon-->
                                                    </div>
                                                    <div wire:loading
                                                         wire:target="downloadLink('{{$meetingFile->id}}')">
                                                    <span
                                                        class="spinner-border spinner-border-sm align-middle m-1"></span>
                                                    </div>
                                                </button>

                                                <div
                                                    class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                                    <input class="form-check-input remove-file" type="checkbox"
                                                           value="{{$meetingFile->id}}"
                                                           wire:key="ipt-{{$meetingFile->id}}">
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @empty

                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    @endif
                    <!--end::Table-->
                </div>
                <!--end::Tap pane-->

                <!--begin::Tap pane-->

                <!--end::Tap pane-->

                <!--begin::Tap pane-->

                <!--end::Tap pane-->
            </div>
        </div>
    </div>
    <!--end::Table Widget 1-->
</div>

@push('scripts')
    <script>
        var checkedValues = [];
        Livewire.on('hide_item', (id) => {
            $(id.id).hide(1000);
        });
        $('.select-all').on('click', function () {
            let allChecked = $('.remove-file:checked').length === $('.remove-file').length;
            $('.remove-file').prop("checked", !allChecked);
            $('.remove-file').trigger('change')
        });

        document.addEventListener('livewire:init', function () {

            $('.remove-file').on('change', function () {
                checkedValues = [];
                $('.remove-file:checked').each(function () {
                    checkedValues.push($(this).val());
                });
                @this.
                set('selectedFiles', JSON.stringify(checkedValues));
            });
        })


    </script>
@endpush
