{{-- If you look to others for fulfillment, you will never truly be fulfilled. --}}
<div>

    <style>
        /* Framer-inspired UI Enhancements */
        span.dot.color3 {
            background-color: #FF605C;
        }

        span.dot.color2 {
            background-color: #FFBD44;
        }

        span.dot.color1 {
            background-color: #00CA4E;
        }

        /* --- Vertical Navigation --- */
        .framer-nav .nav-link {
            transition: all 0.25s ease;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            color: #5E6278;
            margin-bottom: 4px;
        }

        .framer-nav .nav-link:hover {
            background-color: rgb(232 240 254 / 54%);
            color: #000000;
            font-weight: 900 !important;
            border-radius: 50px 0 0 50px;
        }

        .framer-nav .nav-link.active {
            background-color: rgb(232 240 254 / 54%);
            color: #000000;
            font-weight: 900 !important;
            border-radius: 50px 0 0 50px;
        }

        .framer-nav .nav-link.active div span {
            font-weight: 600 !important;
        }

        /* --- Content Sections --- */
        .framer-section {
            background-color: #ffffff;
            border: 1px solid #f1f1f1;
            border-radius: 12px;
            padding: 2rem;
            transition: all 0.25s ease;
            margin-bottom: 2rem;
        }


        .framer-section:last-child {
            margin-bottom: 0;
        }

        /* --- Tab Content Animation --- */
        .framer-content > .tab-pane.fade {
            transition-duration: .4s;
        }

        .framer-content > .tab-pane {
            animation: fadeIn 0.4s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* --- General Transitions --- */
        .form-control, .form-select, .btn, .form-check-input {
            transition: all 0.25s ease;
        }

        /* --- Theme Preview --- */
        .theme-preview-label {
            display: block;
            cursor: pointer;
            border: 2px solid #f1f1f1;
            border-radius: 10px;
            padding: 8px;
            transition: all 0.2s ease-in-out;
            text-align: center;
            height: 100%;
        }

        .theme-preview-label.active {
            border-color: var(--bs-primary);
            background-color: var(--bs-primary-light);
        }

        .theme-preview-label:hover {
            border-color: var(--bs-primary);
        }

        .theme-preview-browser {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            background-color: #ffffff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .browser-header {
            background-color: #f1f3f5;
            padding: 6px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #e0e0e0;
        }

        .browser-header .dot {
            height: 8px;
            width: 8px;
            border-radius: 50%;
            background-color: #ced4da;
            margin-right: 4px;
        }

        .browser-content {
            padding: 12px;
            height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
        }

        .preview-line-lg, .preview-line-md, .preview-line-sm {
            border-radius: 4px;
        }

        .preview-line-lg {
            height: 12px;
            width: 80%;
        }

        .preview-line-md {
            height: 10px;
            width: 60%;
        }

        .preview-line-sm {
            height: 8px;
            width: 90%;
        }

        /* Light Theme Preview */
        .browser-content.theme-1 {
            background-color: #ffffff;
        }

        .browser-content.theme-1 .preview-line-lg,
        .browser-content.theme-1 .preview-line-md,
        .browser-content.theme-1 .preview-line-sm {
            background-color: #dee2e6;
        }

        /* Dark Theme Preview */
        .browser-content.theme-2 {
            background-color: #181C32;
        }

        .browser-content.theme-2 .preview-line-lg,
        .browser-content.theme-2 .preview-line-md,
        .browser-content.theme-2 .preview-line-sm {
            background-color: #4b5675;
        }

        /* Modern Theme Preview */
        .browser-content.theme-3 {
            background-image: linear-gradient(120deg, #14151f 0%, #252a38 100%);
        }

        .browser-content.theme-3 .preview-line-lg {
            background-color: #a1c4fd;
        }

        .browser-content.theme-3 .preview-line-md,
        .browser-content.theme-3 .preview-line-sm {
            background-color: #c2e9fb;
        }

        /* Space Theme Preview */
        .browser-content.theme-4 {
            background: #232526;
            background: linear-gradient(to right, #414345, #232526);
        }

        .browser-content.theme-4 .preview-line-lg,
        .browser-content.theme-4 .preview-line-md,
        .browser-content.theme-4 .preview-line-sm {
            background-color: #ffffff2b;
        }

        .theme-preview-name {
            margin-top: 8px;
            font-weight: 500;
            color: #5E6278;
            font-size: 0.9rem;
        }

        .theme-preview-label.active .theme-preview-name {
            color: var(--bs-primary);
            font-weight: 600;
        }

        /* --- Event Preview Browser --- */
        .browser-preview-container {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            background-color: #ffffff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            height: 80%;
            display: flex;
            flex-direction: column;
        }

        .browser-preview-container .browser-header {
            background-color: #f1f3f5;
            padding: 8px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #e0e0e0;
            flex-shrink: 0;
        }

        .browser-preview-container .browser-header .dot {
            height: 10px;
            width: 10px;
            border-radius: 50%;
            margin-right: 6px;
        }

        .browser-preview-container .browser-body {
            padding: 1.5rem;
            flex-grow: 1;
            background-color: #f9f9f9;
        }

        .browser-preview-container .preview-title {
            font-weight: bold;
            color: #181C32;
            margin-bottom: 1rem;
            min-height: 24px; /* To prevent jumping */
            word-wrap: break-word;
        }

        .browser-preview-container .preview-description {
            color: #5E6278;
            font-size: 0.9rem;
            white-space: pre-wrap; /* To respect newlines */
            word-wrap: break-word;
            min-height: 48px; /* To prevent jumping */
        }

        /* --- Event Preview Browser Themes --- */
        .browser-body.theme-1 {
            background-color: #f9f9f9;
        }

        .browser-body.theme-1 .preview-title {
            color: #181C32;
        }

        .browser-body.theme-1 .preview-description {
            color: #5E6278;
        }

        .browser-body.theme-2 {
            background-color: #1e1e2d;
        }

        .browser-body.theme-2 .preview-title {
            color: #ffffff;
        }

        .browser-body.theme-2 .preview-description {
            color: #a1a5b7;
        }

        .browser-body.theme-3 {
            background: linear-gradient(135deg, #f5f7fa 0%, #e8eef5 100%);
        }

        .browser-body.theme-3 .preview-title {
            color: #2c3e50;
        }

        .browser-body.theme-3 .preview-description {
            color: #ffffff;
        }

        .browser-body.theme-4 {
            background: linear-gradient(to top, #302b63, #24243e, #0f0c29);
        }

        .browser-body.theme-4 .preview-title {
            color: #e0e0e0;
        }

        .browser-body.theme-4 .preview-description {
            color: #b0b0d0;
        }


        .theme-2 {
            background-color: #181C32 !important;
            color: #fff !important;
            text-align: center;
        }

        .theme-2 h4, .theme-3 h4, .theme-4 h4 {
            background-color: #181C32 !important;
            color: #fff !important;
        }

        .theme-4 {
            background-color: linear-gradient(to top, #302b63, #24243e, #0f0c29) !important;
            background-image: url("{{asset('index-assets/png/bg-colored.png')}}") !important;
            color: #fff !important;
            text-align: center;
            color: #fff !important;
            background-size: cover !important;
            background-position: center !important
        }


        .browser-body.theme-3 {
            background: linear-gradient(0deg, rgba(186, 207, 247, .04), rgba(186, 207, 247, .04)), rgba(5, 6, 15, .97);
            box-shadow: inset 0 1px 1px 0 rgba(216, 236, 248, .2), inset 0 24px 48px 0 rgba(168, 216, 245, .06),;
            color: #fff !important;
            text-align: center;
        }

        .browser-preview-container .browser-body.theme-3:after {
            content: " ";
            background-image: conic-gradient(from 0deg at 50% -5%, transparent 45%, rgba(124, 145, 182, .3) 49%, rgba(124, 145, 182, .5) 50%, rgba(124, 145, 182, .3) 51%, transparent 55%);
            position: absolute;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 80%;
            opacity: 0.5;
        }

        .browser-preview-container .browser-body.theme-3 {
            position: relative;
        }


    </style>

    <div class="card card-flush">
        <!--begin::Card header-->
        <div class="card-header border-0 pt-6">
            <div class="card-title">
                <div class="d-flex align-items-center position-relative">
                    <div class="symbol symbol-50px me-5">
                        <div class="symbol-label" style="background-color: #f3f6f9;">
                           <span class="svg-icon svg-icon-4">
                               <svg xmlns="http://www.w3.org/2000/svg" width="24"
                                    height="24" viewBox="0 0 24 24" fill="none">
                                    <path
                                        d="M17.5 11H6.5C4 11 2 9 2 6.5C2 4 4 2 6.5 2H17.5C20 2 22 4 22 6.5C22 9 20 11 17.5 11ZM15 6.5C15 7.9 16.1 9 17.5 9C18.9 9 20 7.9 20 6.5C20 5.1 18.9 4 17.5 4C16.1 4 15 5.1 15 6.5Z"
                                        fill="black"></path>
                                    <path opacity="0.3"
                                          d="M17.5 22H6.5C4 22 2 20 2 17.5C2 15 4 13 6.5 13H17.5C20 13 22 15 22 17.5C22 20 20 22 17.5 22ZM4 17.5C4 18.9 5.1 20 6.5 20C7.9 20 9 18.9 9 17.5C9 16.1 7.9 15 6.5 15C5.1 15 4 16.1 4 17.5Z"
                                          fill="black"></path>
                                    </svg>
                           </span>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <h2 class="fw-bold m-0 text-gray-800">تنظیمات اتاق</h2>
                        <div class="text-muted mt-1 fw-semibold fs-6">مدیریت تنظیمات و زمان‌بندی اتاق</div>
                    </div>
                </div>
            </div>
        </div>
        <!--end::Card header-->

        <!--begin::Card body-->
        <div class="card-body pt-4 ps-0">
            <div class="d-flex flex-column flex-lg-row">
                <!--begin::Aside-->
                <div class="flex-column flex-lg-row-auto w-100 w-lg-250px mb-10 mb-lg-0">
                    <div class="nav nav-tabs flex-column nav-pills framer-nav nav-pills-start" wire:ignore>
                        <a class="nav-link active" data-bs-toggle="tab" href="#kt_tab_basic_info">
                            <div class="d-flex align-items-center p-2">
                                <span class="symbol symbol-50px me-6">
                                    <span class="svg-icon svg-icon-muted svg-icon-2hx">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3"
      d="M22.1 11.5V12.6C22.1 13.2 21.7 13.6 21.2 13.7L19.9 13.9C19.7 14.7 19.4 15.5 18.9 16.2L19.7 17.2999C20 17.6999 20 18.3999 19.6 18.7999L18.8 19.6C18.4 20 17.8 20 17.3 19.7L16.2 18.9C15.5 19.3 14.7 19.7 13.9 19.9L13.7 21.2C13.6 21.7 13.1 22.1 12.6 22.1H11.5C10.9 22.1 10.5 21.7 10.4 21.2L10.2 19.9C9.4 19.7 8.6 19.4 7.9 18.9L6.8 19.7C6.4 20 5.7 20 5.3 19.6L4.5 18.7999C4.1 18.3999 4.1 17.7999 4.4 17.2999L5.2 16.2C4.8 15.5 4.4 14.7 4.2 13.9L2.9 13.7C2.4 13.6 2 13.1 2 12.6V11.5C2 10.9 2.4 10.5 2.9 10.4L4.2 10.2C4.4 9.39995 4.7 8.60002 5.2 7.90002L4.4 6.79993C4.1 6.39993 4.1 5.69993 4.5 5.29993L5.3 4.5C5.7 4.1 6.3 4.10002 6.8 4.40002L7.9 5.19995C8.6 4.79995 9.4 4.39995 10.2 4.19995L10.4 2.90002C10.5 2.40002 11 2 11.5 2H12.6C13.2 2 13.6 2.40002 13.7 2.90002L13.9 4.19995C14.7 4.39995 15.5 4.69995 16.2 5.19995L17.3 4.40002C17.7 4.10002 18.4 4.1 18.8 4.5L19.6 5.29993C20 5.69993 20 6.29993 19.7 6.79993L18.9 7.90002C19.3 8.60002 19.7 9.39995 19.9 10.2L21.2 10.4C21.7 10.5 22.1 11 22.1 11.5ZM12.1 8.59998C10.2 8.59998 8.6 10.2 8.6 12.1C8.6 14 10.2 15.6 12.1 15.6C14 15.6 15.6 14 15.6 12.1C15.6 10.2 14 8.59998 12.1 8.59998Z"
      fill="currentColor"/>
<path
    d="M17.1 12.1C17.1 14.9 14.9 17.1 12.1 17.1C9.30001 17.1 7.10001 14.9 7.10001 12.1C7.10001 9.29998 9.30001 7.09998 12.1 7.09998C14.9 7.09998 17.1 9.29998 17.1 12.1ZM12.1 10.1C11 10.1 10.1 11 10.1 12.1C10.1 13.2 11 14.1 12.1 14.1C13.2 14.1 14.1 13.2 14.1 12.1C14.1 11 13.2 10.1 12.1 10.1Z"
    fill="currentColor"/>
</svg>
                                    </span>
                                </span>
                                <span class="fw-bold">اطلاعات پایه</span>
                            </div>
                        </a>
                        <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_public_page">
                            <div class="d-flex align-items-center p-2">
                                <span class="symbol symbol-50px me-6">
                                    <span class="svg-icon svg-icon-muted svg-icon-2hx">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
<path
    d="M19.4 13.9411L10.7 5.24112C10.4 4.94112 10 4.84104 9.60001 5.04104C9.20001 5.24104 9 5.54107 9 5.94107V18.2411C9 18.6411 9.20001 18.941 9.60001 19.141C9.70001 19.241 9.9 19.2411 10 19.2411C10.2 19.2411 10.4 19.141 10.6 19.041C11.4 18.441 12.1 17.941 12.9 17.541L14.4 21.041C14.6 21.641 15.2 21.9411 15.8 21.9411C16 21.9411 16.2 21.9411 16.4 21.8411C17.2 21.5411 17.5 20.6411 17.2 19.8411L15.7 16.2411C16.7 15.9411 17.7 15.741 18.8 15.541C19.2 15.541 19.5 15.2411 19.6 14.8411C19.8 14.6411 19.7 14.2411 19.4 13.9411Z"
    fill="currentColor"/>
<path opacity="0.3"
      d="M15 6.941C14.8 6.941 14.7 6.94102 14.6 6.84102C14.1 6.64102 13.9 6.04097 14.2 5.54097L15.2 3.54097C15.4 3.04097 16 2.84095 16.5 3.14095C17 3.34095 17.2 3.941 16.9 4.441L15.9 6.441C15.7 6.741 15.4 6.941 15 6.941ZM18.4 9.84102L20.4 8.84102C20.9 8.64102 21.1 8.04097 20.8 7.54097C20.6 7.04097 20 6.84095 19.5 7.14095L17.5 8.14095C17 8.34095 16.8 8.941 17.1 9.441C17.3 9.841 17.6 10.041 18 10.041C18.2 9.94097 18.3 9.94102 18.4 9.84102ZM7.10001 10.941C7.10001 10.341 6.70001 9.941 6.10001 9.941H4C3.4 9.941 3 10.341 3 10.941C3 11.541 3.4 11.941 4 11.941H6.10001C6.70001 11.941 7.10001 11.541 7.10001 10.941ZM4.89999 17.1409L6.89999 16.1409C7.39999 15.9409 7.59999 15.341 7.29999 14.841C7.09999 14.341 6.5 14.141 6 14.441L4 15.441C3.5 15.641 3.30001 16.241 3.60001 16.741C3.80001 17.141 4.1 17.341 4.5 17.341C4.6 17.241 4.79999 17.2409 4.89999 17.1409Z"
      fill="currentColor"/>
</svg>
                                    </span>
                                </span>
                                <span class="fw-bold">تنظیمات دسترسی</span>
                            </div>
                        </a>
                        <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_recording">
                            <div class="d-flex align-items-center p-2">
                                <span class="symbol symbol-50px me-6">
                                    <span class="svg-icon svg-icon-muted svg-icon-2hx">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3"
      d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10ZM6.39999 9.89999C6.99999 8.19999 8.40001 6.9 10.1 6.4C10.6 6.2 10.9 5.7 10.7 5.1C10.5 4.6 9.99999 4.3 9.39999 4.5C7.09999 5.3 5.29999 7 4.39999 9.2C4.19999 9.7 4.5 10.3 5 10.5C5.1 10.5 5.19999 10.6 5.39999 10.6C5.89999 10.5 6.19999 10.2 6.39999 9.89999ZM14.8 19.5C17 18.7 18.8 16.9 19.6 14.7C19.8 14.2 19.5 13.6 19 13.4C18.5 13.2 17.9 13.5 17.7 14C17.1 15.7 15.8 17 14.1 17.6C13.6 17.8 13.3 18.4 13.5 18.9C13.6 19.3 14 19.6 14.4 19.6C14.5 19.6 14.6 19.6 14.8 19.5Z"
      fill="currentColor"/>
<path
    d="M16 12C16 14.2 14.2 16 12 16C9.8 16 8 14.2 8 12C8 9.8 9.8 8 12 8C14.2 8 16 9.8 16 12ZM12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10Z"
    fill="currentColor"/>
</svg>
                                    </span>
                                </span>
                                <span class="fw-bold">تنظیمات ضبط</span>
                            </div>
                        </a>
                        <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_scheduling">
                            <div class="d-flex align-items-center p-2">
                                <span class="symbol symbol-50px me-6">
                                    <span class="svg-icon svg-icon-muted svg-icon-2hx">
                                        <svg width="25" height="28" viewBox="0 0 25 28" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
<path
    d="M24.0259 11.4401H1.97259C1.69436 11.4505 1.43123 11.5693 1.2394 11.7711C1.04757 11.9729 0.942247 12.2417 0.945922 12.5201V20.0801C0.933592 21.0248 1.10836 21.9627 1.46016 22.8395C1.81196 23.7164 2.33382 24.515 2.99568 25.1892C3.65754 25.8635 4.4463 26.4001 5.3165 26.7681C6.1867 27.1361 7.12112 27.3282 8.06592 27.3334H17.9993C19.8855 27.288 21.6778 26.5012 22.988 25.1436C24.2983 23.7859 25.0208 21.9667 24.9993 20.0801V12.5201C25.0037 12.2504 24.9057 11.989 24.7251 11.7886C24.5445 11.5882 24.2947 11.4637 24.0259 11.4401ZM8.73259 21.8401C8.51017 21.84 8.29271 21.7744 8.1073 21.6515C7.92189 21.5287 7.77672 21.354 7.68989 21.1492C7.60306 20.9444 7.5784 20.7186 7.61899 20.5C7.65957 20.2813 7.76361 20.0794 7.91813 19.9194C8.07266 19.7594 8.27084 19.6484 8.48798 19.6003C8.70513 19.5522 8.93164 19.569 9.1393 19.6487C9.34695 19.7283 9.52658 19.8673 9.65578 20.0484C9.78499 20.2294 9.85807 20.4445 9.86592 20.6668C9.86241 20.965 9.74146 21.2499 9.5293 21.4595C9.31714 21.6692 9.03087 21.7868 8.73259 21.7868V21.8401ZM8.73259 17.5868C8.50844 17.5868 8.28932 17.5203 8.10294 17.3958C7.91657 17.2712 7.77131 17.0942 7.68553 16.8871C7.59975 16.6801 7.5773 16.4522 7.62103 16.2323C7.66476 16.0125 7.7727 15.8105 7.9312 15.652C8.0897 15.4935 8.29164 15.3856 8.51149 15.3419C8.73133 15.2981 8.95921 15.3206 9.1663 15.4064C9.37339 15.4921 9.55039 15.6374 9.67492 15.8238C9.79945 16.0102 9.86592 16.2293 9.86592 16.4534C9.86771 16.6028 9.83962 16.7509 9.7833 16.8892C9.72697 17.0276 9.64356 17.1532 9.53796 17.2588C9.43236 17.3644 9.30672 17.4478 9.1684 17.5041C9.03009 17.5605 8.88192 17.5886 8.73259 17.5868ZM12.9993 21.8401C12.701 21.8331 12.4175 21.7088 12.2104 21.4941C12.0032 21.2794 11.889 20.9917 11.8926 20.6934C11.8926 20.3964 12.0106 20.1115 12.2206 19.9015C12.4307 19.6914 12.7155 19.5734 13.0126 19.5734C13.3096 19.5734 13.5945 19.6914 13.8045 19.9015C14.0146 20.1115 14.1326 20.3964 14.1326 20.6934C14.1291 20.9917 14.0081 21.2765 13.796 21.4862C13.5838 21.6959 13.2975 21.8135 12.9993 21.8134V21.8401ZM12.9993 17.5868C12.701 17.5798 12.4175 17.4555 12.2104 17.2408C12.0032 17.0261 11.889 16.7384 11.8926 16.4401C11.8926 16.1431 12.0106 15.8582 12.2206 15.6481C12.4307 15.4381 12.7155 15.3201 13.0126 15.3201C13.3096 15.3201 13.5945 15.4381 13.8045 15.6481C14.0146 15.8582 14.1326 16.1431 14.1326 16.4401C14.1326 16.7384 14.015 17.0246 13.8054 17.2368C13.5957 17.449 13.3109 17.5699 13.0126 17.5734L12.9993 17.5868ZM17.2393 21.8401C16.9387 21.8401 16.6504 21.7207 16.4379 21.5082C16.2253 21.2956 16.1059 21.0073 16.1059 20.7068C16.1059 20.4062 16.2253 20.1179 16.4379 19.9054C16.6504 19.6928 16.9387 19.5734 17.2393 19.5734C17.5398 19.5734 17.8281 19.6928 18.0406 19.9054C18.2532 20.1179 18.3726 20.4062 18.3726 20.7068C18.3726 21.0073 18.2532 21.2956 18.0406 21.5082C17.8281 21.7207 17.5398 21.8401 17.2393 21.8401ZM17.2393 17.5868C16.9387 17.5868 16.6504 17.4674 16.4379 17.2548C16.2253 17.0423 16.1059 16.754 16.1059 16.4534C16.1059 16.1529 16.2253 15.8646 16.4379 15.652C16.6504 15.4395 16.9387 15.3201 17.2393 15.3201C17.5398 15.3201 17.8281 15.4395 18.0406 15.652C18.2532 15.8646 18.3726 16.1529 18.3726 16.4534C18.3726 16.754 18.2532 17.0423 18.0406 17.2548C17.8281 17.4674 17.5398 17.5868 17.2393 17.5868ZM24.6393 8.13343C24.7349 8.40774 24.7203 8.7085 24.5984 8.9722C24.4765 9.2359 24.2569 9.44192 23.9859 9.54677C23.8703 9.58813 23.7487 9.61063 23.6259 9.61343H2.62592C2.2723 9.61343 1.93316 9.47296 1.68311 9.22291C1.43306 8.97286 1.29259 8.63372 1.29259 8.2801C1.28883 8.11525 1.32066 7.95153 1.38592 7.8001C1.77683 6.84295 2.37003 5.98161 3.12487 5.27509C3.87972 4.56858 4.77837 4.03358 5.75926 3.70677V1.62677C5.75926 1.3863 5.85478 1.15569 6.02481 0.985655C6.19485 0.815622 6.42546 0.720099 6.66592 0.720099C6.90639 0.720099 7.137 0.815622 7.30703 0.985655C7.47707 1.15569 7.57259 1.3863 7.57259 1.62677V3.33343H12.3059V1.62677C12.2904 1.49938 12.3021 1.37015 12.3402 1.24761C12.3783 1.12508 12.442 1.01204 12.5271 0.915961C12.6122 0.819883 12.7167 0.74296 12.8337 0.690277C12.9507 0.637594 13.0776 0.610352 13.2059 0.610352C13.3343 0.610352 13.4611 0.637594 13.5781 0.690277C13.6952 0.74296 13.7997 0.819883 13.8847 0.915961C13.9698 1.01204 14.0335 1.12508 14.0716 1.24761C14.1098 1.37015 14.1215 1.49938 14.1059 1.62677V3.33343H18.3326V1.62677C18.3171 1.49938 18.3287 1.37015 18.3669 1.24761C18.405 1.12508 18.4687 1.01204 18.5538 0.915961C18.6389 0.819883 18.7434 0.74296 18.8604 0.690277C18.9774 0.637594 19.1043 0.610352 19.2326 0.610352C19.3609 0.610352 19.4878 0.637594 19.6048 0.690277C19.7218 0.74296 19.8263 0.819883 19.9114 0.915961C19.9965 1.01204 20.0602 1.12508 20.0983 1.24761C20.1364 1.37015 20.1481 1.49938 20.1326 1.62677V3.70677C21.1713 4.05261 22.1173 4.63121 22.8984 5.39839C23.6794 6.16557 24.2749 7.10105 24.6393 8.13343Z"
    fill="currentColor"/>
</svg>
                                    </span>
                                </span>
                                <span class="fw-bold">زمان‌بندی</span>
                            </div>
                        </a>
                        <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_notifications">
                            <div class="d-flex align-items-center p-2">
                                <span class="symbol symbol-50px me-6">
                                    <span class="svg-icon svg-icon-muted svg-icon-2hx">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3"
      d="M14 3V20H2V3C2 2.4 2.4 2 3 2H13C13.6 2 14 2.4 14 3ZM11 13V11C11 9.7 10.2 8.59995 9 8.19995V7C9 6.4 8.6 6 8 6C7.4 6 7 6.4 7 7V8.19995C5.8 8.59995 5 9.7 5 11V13C5 13.6 4.6 14 4 14V15C4 15.6 4.4 16 5 16H11C11.6 16 12 15.6 12 15V14C11.4 14 11 13.6 11 13Z"
      fill="currentColor"/>
<path
    d="M2 20H14V21C14 21.6 13.6 22 13 22H3C2.4 22 2 21.6 2 21V20ZM9 3V2H7V3C7 3.6 7.4 4 8 4C8.6 4 9 3.6 9 3ZM6.5 16C6.5 16.8 7.2 17.5 8 17.5C8.8 17.5 9.5 16.8 9.5 16H6.5ZM21.7 12C21.7 11.4 21.3 11 20.7 11H17.6C17 11 16.6 11.4 16.6 12C16.6 12.6 17 13 17.6 13H20.7C21.2 13 21.7 12.6 21.7 12ZM17 8C16.6 8 16.2 7.80002 16.1 7.40002C15.9 6.90002 16.1 6.29998 16.6 6.09998L19.1 5C19.6 4.8 20.2 5 20.4 5.5C20.6 6 20.4 6.60005 19.9 6.80005L17.4 7.90002C17.3 8.00002 17.1 8 17 8ZM19.5 19.1C19.4 19.1 19.2 19.1 19.1 19L16.6 17.9C16.1 17.7 15.9 17.1 16.1 16.6C16.3 16.1 16.9 15.9 17.4 16.1L19.9 17.2C20.4 17.4 20.6 18 20.4 18.5C20.2 18.9 19.9 19.1 19.5 19.1Z"
    fill="currentColor"/>
</svg>
                                    </span>
                                </span>
                                <span class="fw-bold">اطلاع‌رسانی</span>
                            </div>
                        </a>
                    </div>
                </div>
                <!--end::Aside-->

                <!--begin::Content-->
                <div class="flex-lg-row-fluid ms-lg-10">
                    <!--begin::Tab content-->
                    <div class="tab-content framer-content">
                        <!--begin::Tab pane-->
                        <div class="tab-pane fade show active" id="kt_tab_basic_info" wire:ignore.self role="tabpanel">
                            <div class="framer-section">
                                <h3 class="fw-bold text-dark mb-4">
                                    اطلاعات اتاق
                                </h3>

                                <div class="row g-5">
                                    <!-- Input Column -->
                                    <div class="col-lg-7">
                                        <div class="mb-5">
                                            <label class="form-label required">عنوان اتاق</label>
                                            <div class="input-group direction-ltr">
                                                <input type="text" class="form-control direction-rtl"
                                                       wire:model.live.debounce.500ms="settings.title"
                                                       placeholder="جلسات روزانه"/>
                                                <span class="input-group-text" id="basic-addon1">
                                                   <i class="las la-window-maximize fs-1"></i>
                                                </span>
                                            </div>
                                            <div class="form-text">این عنوان در صفحه عمومی مرتبط با اتاق نمایش داده
                                                خواهد
                                                شد.
                                            </div>
                                        </div>

                                        <div class="mb-5">
                                            <label class="form-label">توضیحات</label>
                                            <div class="input-group">
                                                <textarea class="form-control"
                                                          wire:model.live.debounce.500ms="settings.summery"
                                                          rows="8"
                                                          placeholder="توضیحات اتاق"></textarea>
                                            </div>
                                            <div class="form-text">این توضیحات در صفحه عمومی مرتبط با اتاق نمایش داده
                                                خواهد
                                                شد.
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Preview Column -->
                                    <div class="col-lg-5 d-none d-lg-block">
                                        <label class="form-label"></label>
                                        <div class="browser-preview-container"
                                             wire:key="theme-preview-{{ $settings->theme }}">
                                            <div class="browser-header">

                                                <span class="dot color1"></span>
                                                <span class="dot color2"></span>
                                                <span class="dot color3"></span>
                                            </div>
                                            <div id="live-preview-body"
                                                 class="browser-body theme-{{ $settings->theme ?? 'light' }}">
                                                <h4 class="preview-title">{{ $settings->title ?: 'عنوان اتاق شما' }}</h4>
                                                <p class="preview-description">{{ $settings->summery ?: 'توضیحات اتاق در اینجا نمایش داده می‌شود...' }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="framer-section">
                                <h3 class="fw-bold text-dark mb-4">
                                    طراحی صفحه
                                </h3>
                                <div class="form-text mt-3 mb-3 direction-rtl">میتوانید طراحی صفحه عمومی خود را انتخاب
                                    نمایید.
                                </div>
                                <div class="row g-4">
                                    <!-- Light Theme -->
                                    <div class="col-6 col-md-3">
                                        <label
                                            class="theme-preview-label {{ ($settings->theme ?? '1') == 1 ? 'active' : '' }}">
                                            <input type="radio" class="btn-check" name="theme" value="1"
                                                   wire:model="settings.theme"/>
                                            <div class="theme-preview-box  theme-preview-browser">
                                                <div class="browser-header">
                                                    <span class="dot color1"></span>
                                                    <span class="dot color2"></span>
                                                    <span class="dot color3"></span>
                                                </div>
                                                <div class="browser-content theme-1">
                                                    <div class="preview-line-sm"></div>
                                                    <div class="preview-line-lg"></div>
                                                    <div class="preview-line-md"></div>
                                                </div>
                                            </div>
                                            <span class="theme-preview-name">روشن</span>
                                        </label>
                                    </div>
                                    <!-- Dark Theme -->
                                    <div class="col-6 col-md-3">
                                        <label
                                            class="theme-preview-label {{ ($settings->theme ?? '1') == '2' ? 'active' : '' }}">
                                            <input type="radio" class="btn-check" name="theme" value="2"
                                                   wire:model="settings.theme"/>
                                            <div class="theme-preview-box  theme-preview-browser">
                                                <div class="browser-header">

                                                    <span class="dot color1"></span>
                                                    <span class="dot color2"></span>
                                                    <span class="dot color3"></span>
                                                </div>
                                                <div class="browser-content theme-2">
                                                    <div class="preview-line-sm"></div>
                                                    <div class="preview-line-lg"></div>
                                                    <div class="preview-line-md"></div>
                                                </div>
                                            </div>
                                            <span class="theme-preview-name">تیره</span>
                                        </label>
                                    </div>
                                    <!-- Modern Theme -->
                                    <div class="col-6 col-md-3">
                                        <label
                                            class="theme-preview-label {{ ($settings->theme ?? '1') == '3' ? 'active' : '' }}">
                                            <input type="radio" class="btn-check" name="theme" value="3"
                                                   wire:model="settings.theme"/>
                                            <div class="theme-preview-box  theme-preview-browser">
                                                <div class="browser-header">

                                                    <span class="dot color1"></span>
                                                    <span class="dot color2"></span>
                                                    <span class="dot color3"></span>
                                                </div>
                                                <div class="browser-content theme-3">
                                                    <div class="preview-line-sm"></div>
                                                    <div class="preview-line-lg"></div>
                                                    <div class="preview-line-md"></div>
                                                </div>
                                            </div>
                                            <span class="theme-preview-name">مدرن</span>
                                        </label>
                                    </div>
                                    <!-- Space Theme -->
                                    <div class="col-6 col-md-3">
                                        <label
                                            class="theme-preview-label {{ ($settings->theme ?? '1') == '4' ? 'active' : '' }}">
                                            <input type="radio" class="btn-check" name="theme" value="4"
                                                   wire:model="settings.theme"/>
                                            <div class="theme-preview-box theme-preview-browser">

                                                <div class="browser-header">
                                                    <span class="dot color1"></span>
                                                    <span class="dot color2"></span>
                                                    <span class="dot color3"></span>
                                                </div>
                                                <div class="browser-content theme-4">
                                                    <div class="preview-line-sm"></div>
                                                    <div class="preview-line-lg"></div>
                                                    <div class="preview-line-md"></div>
                                                </div>
                                            </div>
                                            <span class="theme-preview-name">فضایی</span>
                                        </label>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <!--end::Tab pane-->

                        <!--begin::Tab pane-->
                        <div class="tab-pane fade" id="kt_tab_public_page" role="tabpanel" wire:ignore.self>
                            <div class="framer-section">
                                <h3 class="fw-bold text-dark mb-4">
                                    تنظیمات صفحه عمومی
                                </h3>

                                <div class="form-check form-switch form-check-custom form-check-solid mb-5">
                                    <input class="form-check-input" type="checkbox" wire:model="settings.public_page"/>
                                    <label class="form-check-label">نمایش صفحه عمومی
                                        <div class="form-text">دسترسی به صفحه عمومی اتاق با امکان مشاهده فایل ها و
                                            لینک
                                            مستقیم ورود به اتاق.
                                        </div>
                                    </label>
                                </div>
                                <div class="alert alert-primary d-flex align-items-center p-5 mb-10">
                                    <span class="svg-icon svg-icon-success svg-icon-2hx me-4"><svg width="24"
                                                                                                   height="24"
                                                                                                   viewBox="0 0 24 24"
                                                                                                   fill="none"
                                                                                                   xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3"
      d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z"
      fill="currentColor"/>
<path
    d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z"
    fill="currentColor"/>
</svg>
</span>
                                    <div class="d-flex flex-column">
                                        <h4 class="mb-1 text-success">کاربرد صفحه عمومی چیست؟</h4>
                                        <span>
                                            با استفاده از لینک زیر علاوه بر تعیین دلخواه آدرس ورود به جلسات، شما میتوانید محدودیت های تحریمی که روی سرویس های
                                            Firebase
                                            برای ایرانیان است را حذف کنید و شرکت کنندگان شما به آسانی و بدون هیچگونه مشکلی میتوانند با هردستگاهی
                                            وارد اتاق شما شوند. <br/>
                                            همچنین شما میتوانید بر اساس برندینگ خود، طراحی این صفحه را مشخص کنید و در صورت تمایل دسترسی شرکت کنندگان را به دانلود ویدئو و چت های
                                            ذخیره شده جلسه را فعال کنید. <br/>
                                            در صورت خاموش کردن صفحه عمومی همچنان میتوانید از لینک زیر استفاده کنید و کاربران بدون مشاهده صفحه ی عمومی به اتاق شما هدایت خواهند شد
                                        </span>
                                    </div>
                                </div>

                                <div class="separator separator-dashed my-6"></div>

                                <div class="mb-5">
                                    <label class="form-label">آدرس صفحه عمومی</label>
                                    <div class="input-group direction-ltr">
                                        <span class="input-group-text">
                                          {{config('app.url')}}show/
                                        </span>
                                        <input type="text" class="form-control"
                                               id="public_page_slug_input"
                                               wire:model="settings.public_page_slug"
                                               placeholder="daily-meetings"/>
                                        <span class="input-group-text" id="copy_slug_button" style="cursor: pointer;">
                                            <i class="las la-copy fs-1"></i>
                                        </span>
                                    </div>
                                </div>

                                <div class="separator separator-dashed my-6"></div>

                                <div class="form-check form-switch form-check-custom form-check-solid mb-5">
                                    <input class="form-check-input" type="checkbox"
                                           wire:model.live="settings.download_access"/>
                                    <label class="form-check-label">دانلود ویدئو های ضبط شده

                                        <div class="form-text">
                                            در صورت فعال بودن. محتوای ضبط شده اتاق شما در صفحه عمومی نمایش داده خواهد شد
                                            و کاربران میتوانند آن را دانلود کنند
                                        </div>
                                        @if($settings->download_access)
                                            <div
                                                class="alert alert-warning alert-dismissible fade show mt-2 direction-rtl"
                                                role="alert">
                                                <span class='fw-bold'>توجه:</span>
                                                در صورت فعال بودن این گزینه - تنظیمات دسترسی ویدئوهای شما بصورت عمومی
                                                خواهد شد و همگان امکان دانلود ویدئوهای این اتاق را خواهند داشت.
                                            </div>
                                        @endif
                                    </label>

                                </div>

                                <div class="form-check form-switch form-check-custom form-check-solid mb-5">
                                    <input class="form-check-input" type="checkbox"
                                           wire:model.live="settings.chat_download_access"/>
                                    <label class="form-check-label">دانلود چت کاربران
                                        <div class="form-text">
                                            در صورت فعال بودن، علاوه امکان دانلود ویدئو ضبط شده، امکان دانلود گفت و
                                            گوهای متنی در جلسات نیز در صفحه عمومی فعال خواهد شد
                                        </div>
                                        @if($settings->chat_download_access)
                                            <div
                                                class="alert alert-warning alert-dismissible fade show mt-2 direction-rtl"
                                                role="alert">
                                                <span class='fw-bold'>توجه:</span>
                                                در صورت فعال بودن این گزینه - تنظیمات دسترسی ویدئوهای شما بصورت عمومی
                                                خواهد شد و همگان امکان دانلود ویدئوهای این اتاق را خواهند داشت.
                                            </div>
                                        @endif

                                    </label>

                                </div>

                                @if($settings->chat_download_access || $settings->download_access)
                                    <div class="separator separator-dashed my-6"></div>
                                    <div class="mb-5">
                                        <label class="form-label">رمز صفحه عمومی (اختیاری)</label>
                                        <div class="input-group direction-ltr">
                                            <input type="password" class="form-control"
                                                   wire:model.defer="settings.public_page_password"
                                                   placeholder="رمز عبور"/>
                                            <span class="input-group-text" onclick="window.eyeControl(event)">
                                                <i class="las la-fingerprint fs-1"></i>
                                            </span>
                                        </div>
                                        <div class="form-text">
                                            لینک صفحه عمومی ممکن است در اختیار سایرین قرار بگیرد. لذا در صورتیکه
                                            میخواهید از محتوای جلسات خود حفاظت کنید میتواند برای نمایش آن ها
                                            رمز عبور تعیین کنید. طبیعی است که با داشتن و وارد کردن رمز عبور و در صورت
                                            فعال بودن دانلود محتوا، امکان دانلود محتوای جلسات شما فراهم خواهد شد.
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <div class="framer-section">
                                <h3 class="fw-bold text-dark mb-4">
                                    تنظیمات دسترسی
                                </h3>
                                <div class="rounded border p-5 mb-5">
                                    <div class="d-flex flex-stack">
                                        <!--begin::Label-->
                                        <div class="me-5 text-align-right direction-rtl">
                                            <label class="fs-6 fw-bold form-label">
                                                یکی از مدیران باید قبل از سایرین وارد شود؟
                                            </label>
                                            <div class="fs-7 fw-bold text-muted">
                                                تا زمان وارد شدن یکی از مدیران، سایرین منتظر خواهند ماند
                                            </div>
                                        </div>
                                        <!--end::Label-->

                                        <!--begin::Switch-->
                                        <label class="form-check form-switch form-check-custom form-check-solid">
                                            <input class="form-check-input" type="checkbox"
                                                   wire:model.live="settings.host_join_type">

                                        </label>
                                        <!--end::Switch-->
                                    </div>
                                </div>
                                <div class="fv-row fv-plugins-icon-container">
                                    <!--begin::Label-->
                                    <label class="d-flex align-items-center fs-5 fw-bold mb-4">
                                        <span class="required">نحوه ورود کاربران</span>
                                        <i class="fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="tooltip" title=""
                                           data-bs-original-title="لیست کاربران را از منوی کاربران این اتاق میتوانید تنظیم کنید"
                                           aria-label="لیست کاربران را از منوی کاربران این اتاق میتوانید تنظیم کنید"></i>
                                    </label>
                                    <!--end::Label-->

                                    <!--begin:Option-->
                                    <label class="d-flex flex-stack cursor-pointer mb-5">
                                        <!--begin:Label-->
                                        <span class="d-flex align-items-center me-2">
                                                    <!--begin:Icon-->
                                                    <span class="symbol symbol-50px me-6">
                                                        <span class="symbol-label bg-light-warning">
                                                            <!--begin::Svg Icon | path: assets/media/icons/duotune/technology/teh008.svg-->
                                                                <span
                                                                    class="svg-icon svg-icon-warning   text-warning svg-icon-2hx"><svg
                                                                        xmlns="http://www.w3.org/2000/svg" width="24"
                                                                        height="24" viewBox="0 0 24 24" fill="none">
                                                                <path opacity="0.3"
                                                                      d="M11 6.5C11 9 9 11 6.5 11C4 11 2 9 2 6.5C2 4 4 2 6.5 2C9 2 11 4 11 6.5ZM17.5 2C15 2 13 4 13 6.5C13 9 15 11 17.5 11C20 11 22 9 22 6.5C22 4 20 2 17.5 2ZM6.5 13C4 13 2 15 2 17.5C2 20 4 22 6.5 22C9 22 11 20 11 17.5C11 15 9 13 6.5 13ZM17.5 13C15 13 13 15 13 17.5C13 20 15 22 17.5 22C20 22 22 20 22 17.5C22 15 20 13 17.5 13Z"
                                                                      fill="black"></path>
                                                                <path
                                                                    d="M17.5 16C17.5 16 17.4 16 17.5 16L16.7 15.3C16.1 14.7 15.7 13.9 15.6 13.1C15.5 12.4 15.5 11.6 15.6 10.8C15.7 9.99999 16.1 9.19998 16.7 8.59998L17.4 7.90002H17.5C18.3 7.90002 19 7.20002 19 6.40002C19 5.60002 18.3 4.90002 17.5 4.90002C16.7 4.90002 16 5.60002 16 6.40002V6.5L15.3 7.20001C14.7 7.80001 13.9 8.19999 13.1 8.29999C12.4 8.39999 11.6 8.39999 10.8 8.29999C9.99999 8.19999 9.20001 7.80001 8.60001 7.20001L7.89999 6.5V6.40002C7.89999 5.60002 7.19999 4.90002 6.39999 4.90002C5.59999 4.90002 4.89999 5.60002 4.89999 6.40002C4.89999 7.20002 5.59999 7.90002 6.39999 7.90002H6.5L7.20001 8.59998C7.80001 9.19998 8.19999 9.99999 8.29999 10.8C8.39999 11.5 8.39999 12.3 8.29999 13.1C8.19999 13.9 7.80001 14.7 7.20001 15.3L6.5 16H6.39999C5.59999 16 4.89999 16.7 4.89999 17.5C4.89999 18.3 5.59999 19 6.39999 19C7.19999 19 7.89999 18.3 7.89999 17.5V17.4L8.60001 16.7C9.20001 16.1 9.99999 15.7 10.8 15.6C11.5 15.5 12.3 15.5 13.1 15.6C13.9 15.7 14.7 16.1 15.3 16.7L16 17.4V17.5C16 18.3 16.7 19 17.5 19C18.3 19 19 18.3 19 17.5C19 16.7 18.3 16 17.5 16Z"
                                                                    fill="black"></path>
                                                                </svg></span>
                                                            <!--end::Svg Icon-->
                                                        </span>
                                                    </span>
                                            <!--end:Icon-->

                                            <!--begin:Info-->
                                                    <span class="d-flex flex-column text-align-right">
                                                        <span class="fw-bolder fs-6">باز</span>
                                                        <span class="fs-7 text-muted">همه میتوانند بدون نیاز به تایید وارد اتاق شما شوند. (مناسب جلسات عمومی و همایش ها)</span>
                                                    </span>
                                            <!--end:Info-->
                                                </span>
                                        <!--end:Label-->

                                        <!--begin:Input-->
                                        <span class="form-check form-check-custom form-check-solid">
                                                    <input class="form-check-input" type="radio" checked="checked"
                                                           wire:model="settings.access_type" name="settings.access_type"
                                                           value="1">
                                                </span>
                                        <!--end:Input-->
                                    </label>
                                    <!--end::Option-->

                                    <!--begin:Option-->
                                    <label class="d-flex flex-stack cursor-pointer mb-5">
                                        <!--begin:Label-->
                                        <span class="d-flex align-items-center me-2">
                                                    <!--begin:Icon-->
                                                    <span class="symbol symbol-50px me-6">
                                                        <span class="symbol-label bg-light-success">
                                                            <!--begin::Svg Icon | path: assets/media/icons/duotune/communication/com013.svg-->
                                                                <span class="svg-icon svg-icon-success svg-icon-2hx"><svg
                                                                        xmlns="http://www.w3.org/2000/svg" width="24"
                                                                        height="24" viewBox="0 0 24 24" fill="none">
                                                                <path
                                                                    d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z"
                                                                    fill="black"></path>
                                                                <rect opacity="0.3" x="8" y="3" width="8" height="8"
                                                                      rx="4" fill="black"></rect>
                                                                </svg></span>
                                                            <!--end::Svg Icon-->
                                                        </span>
                                                    </span>
                                            <!--end:Icon-->

                                            <!--begin:Info-->
                                                    <span class="d-flex flex-column text-align-right">
                                                        <span class="fw-bolder fs-6">دعوت شده ها</span>
                                                        <span
                                                            class="fs-7 text-muted">دعوت شده ها بدون تایید وارد شوند (این امکان فقط در بازه زمان بندی اتاق امکان پذیر است.<br/> برای تنظیم بازه زمان بندی به قسمت زمان بندی مراجعه نمایید.)</span>
                                                    </span>
                                            <!--end:Info-->
                                                </span>
                                        <!--end:Label-->

                                        <!--begin:Input-->
                                        <span class="form-check form-check-custom form-check-solid">
                                                    <input class="form-check-input" type="radio"
                                                           name="settings.access_type"
                                                           wire:model="settings.access_type" value="5">
                                                </span>
                                        <!--end:Input-->
                                    </label>
                                    <!--end::Option-->


                                    <div class="fv-plugins-message-container invalid-feedback"></div>
                                </div>
                            </div>
                        </div>
                        <!--end::Tab pane-->

                        <!--begin::Tab pane-->
                        <div class="tab-pane fade" id="kt_tab_recording" role="tabpanel" wire:ignore.self>
                            <div class="framer-section">
                                <h3 class="fw-bold text-dark mb-4">
                                    تنظیمات ضبط جلسات
                                </h3>

                                <p class="fw-bold text-dark mb-4">
                                    امکان ضبط در تمامی سرویس های آی روم روی گوگل میت برای شما فراهم است و در شروع هر
                                    جلسه مدیر اتاق میتواند ضبط را فعال نماید. در صورت تمایل
                                    به ضبط خودکار تمامی جلسات میتوانید گزینه زیر را روشن کنید.
                                </p>

                                <div class="form-check form-switch form-check-custom form-check-solid mb-5 disabled">
                                    <input class="form-check-input" type="checkbox"
                                           wire:model="settings.auto_recording"/>
                                    <label class="form-check-label">ضبط خودکار جلسات
                                        <span
                                            class="badge badge-exclusive badge-light-primary fw-semibold fs-8 px-2 py-1 ms-1"
                                            data-bs-toggle="tooltip" data-bs-placement="right"
                                            data-bs-original-title="ویژگی های پیشرفته سازمانی" data-kt-initialized="1">
                                            <span class="svg-icon svg-icon-4 no-color">
       <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="512"
            height="512" x="0" y="0" viewBox="0 0 105 105" style="enable-background:new 0 0 512 512"
            xml:space="preserve" class=""><g><path fill="#364f66"
                                                   d="M14.913 17.918a.2.2 0 0 1-.2-.2v-5a.2.2 0 0 1 .4 0v5a.2.2 0 0 1-.2.2zM13.12 18.574a.2.2 0 0 1-.2-.2V7.718a.2.2 0 0 1 .4 0v10.656a.2.2 0 0 1-.2.2zM11.234 19.418a.2.2 0 0 1-.2-.2v-8.625a.2.2 0 0 1 .4 0v8.625a.2.2 0 0 1-.2.2zM9.217 20.127a.2.2 0 0 1-.2-.2v-4.71a.2.2 0 0 1 .4 0v4.71a.2.2 0 0 1-.2.2zM93.22 40.8a.2.2 0 0 1-.2-.2l-.015-3.012c-.668.01-2.752.044-3.165.01a.2.2 0 0 1-.181-.217c.009-.109.114-.188.217-.182.461.04 3.296-.015 3.325-.015h.004a.201.201 0 0 1 .2.199l.015 3.216c0 .11-.09.2-.2.2zM63.957 12.934c-.954 0-1.73-.777-1.73-1.732 0-.954.776-1.73 1.73-1.73s1.73.776 1.73 1.73c0 .955-.776 1.732-1.73 1.732zm0-3.062a1.332 1.332 0 0 0 0 2.661 1.332 1.332 0 0 0 0-2.661zM54.413 96.412c-.954 0-1.73-.776-1.73-1.73 0-.956.776-1.732 1.73-1.732.955 0 1.731.776 1.731 1.732 0 .954-.776 1.73-1.731 1.73zm0-3.062a1.332 1.332 0 0 0 0 2.662 1.332 1.332 0 0 0 0-2.662z"
                                                   opacity="1" data-original="#364f66"></path><path fill="#fbd46c"
                                                                                                    d="M77.1 31.664v16.86l-36.12 49.91L4.5 48.514v-16.92l36.48-25.03z"
                                                                                                    opacity="1"
                                                                                                    data-original="#fbd46c"
                                                                                                    class=""></path><path
                   fill="#f9c539" d="M40.983 6.565 4.503 31.592v16.924z" opacity="1" data-original="#f9c539"
                   class=""></path><path fill="#fbd46c" d="M40.983 6.565 25.81 60.51 4.502 48.516z" opacity="1"
                                         data-original="#fbd46c" class=""></path><path fill="#f9c539"
                                                                                       d="M40.983 6.565 55.77 61.532l-29.96-1.021z"
                                                                                       opacity="1"
                                                                                       data-original="#f9c539"
                                                                                       class=""></path><path
                   fill="#fbd46c" d="m40.983 6.565 36.12 41.956L55.77 61.532z" opacity="1" data-original="#fbd46c"
                   class=""></path><path fill="#f9c539" d="m40.983 6.565 36.121 25.103v16.853z" opacity="1"
                                         data-original="#f9c539" class=""></path><path fill="#eaad24"
                                                                                       d="M77.103 48.52 40.977 98.437 55.77 61.532z"
                                                                                       opacity="1"
                                                                                       data-original="#eaad24"></path><path
                   fill="#fbd46c" d="m55.77 61.532-29.96-1.021 15.167 37.925z" opacity="1" data-original="#fbd46c"
                   class=""></path><path fill="#eaad24" d="M4.502 48.516 25.81 60.51l15.167 37.925z" opacity="1"
                                         data-original="#eaad24"></path><path fill="#fbd46c"
                                                                              d="m100.5 72.864-2.05 7.27-21.68 17.15-9.66-25.99 2.06-7.31 18.8-6.36z"
                                                                              opacity="1" data-original="#fbd46c"
                                                                              class=""></path><path fill="#f9c539"
                                                                                                    d="m87.966 57.625-18.795 6.36-2.061 7.306z"
                                                                                                    opacity="1"
                                                                                                    data-original="#f9c539"
                                                                                                    class=""></path><path
                   fill="#fbd46c" d="m87.966 57.625-13.12 21.438-7.736-7.772z" opacity="1" data-original="#fbd46c"
                   class=""></path><path fill="#f9c539" d="m87.966 57.625-.312 25.528-12.808-4.09z" opacity="1"
                                         data-original="#f9c539" class=""></path><path fill="#fbd46c"
                                                                                       d="m87.966 57.625 10.481 22.51-10.793 3.018z"
                                                                                       opacity="1"
                                                                                       data-original="#fbd46c"
                                                                                       class=""></path><path
                   fill="#f9c539" d="M87.966 57.625 100.5 72.86l-2.053 7.275z" opacity="1" data-original="#f9c539"
                   class=""></path><path fill="#eaad24" d="M98.447 80.135 76.774 97.28l10.88-14.128z" opacity="1"
                                         data-original="#eaad24"></path><path fill="#fbd46c"
                                                                              d="m87.654 83.153-12.808-4.09 1.928 18.218z"
                                                                              opacity="1" data-original="#fbd46c"
                                                                              class=""></path><path fill="#eaad24"
                                                                                                    d="m67.11 71.29 7.736 7.773 1.928 18.218z"
                                                                                                    opacity="1"
                                                                                                    data-original="#eaad24"></path><path
                   fill="#364f66"
                   d="M10.335 64.977a.199.199 0 0 1-.162-.082l-4.158-5.69a.2.2 0 1 1 .322-.236l4.159 5.69a.2.2 0 0 1-.161.318zM25.984 86.395a.199.199 0 0 1-.161-.082l-14.16-19.378a.2.2 0 1 1 .323-.236l14.16 19.378a.2.2 0 0 1-.162.318zM32.234 94.95a.199.199 0 0 1-.161-.083l-4.414-6.04a.2.2 0 1 1 .322-.237l4.414 6.04a.2.2 0 0 1-.161.32z"
                   opacity="1" data-original="#364f66"></path></g></svg>

    </span>
                                            فقط در پنل سازمانی

                                        </span>

                                        <div class="form-text">
                                            بصورت خودکار با ورود اولین مدیر به اتاق، جلسه شروع به ضبط شدن میکند.
                                        </div>
                                    </label>

                                </div>

                            </div>

                        </div>
                        <!--end::Tab pane-->

                        <!--begin::Tab pane-->
                        <div class="tab-pane fade" id="kt_tab_scheduling" role="tabpanel" wire:ignore.self>
                            <div class="framer-section">
                                <h3 class="fw-bold text-dark mb-5">زمان‌بندی اتاق</h3>

                                <div class="mb-8">
                                    <label class="fs-6 fw-bold form-label mb-3">زمان بندی های پیشنهادی</label>
                                    <div class="d-flex flex-wrap gap-3">
                                        <button type="button" class="btn btn-outline btn-outline-dashed"
                                                wire:click="applyPreset('daily')">

                                            جلسه روزانه (۱۵ دقیقه)

                                            <span wire:loading wire:target="applyPreset('daily')">
                                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                            </span>
                                        </button>
                                        <button type="button" class="btn btn-outline btn-outline-dashed"
                                                wire:click="applyPreset('weekly')">

                                            جلسه هفتگی (۴۵ دقیقه)

                                            <span wire:loading wire:target="applyPreset('weekly')">
                                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                            </span>
                                        </button>
                                        <button type="button" class="btn btn-outline btn-outline-dashed"
                                                wire:click="applyPreset('all-day')">
                                            دسترسی آزاد

                                            <span wire:loading wire:target="applyPreset('all-day')">
                                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                            </span>

                                            <i class="fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="tooltip"
                                               title=""
                                               data-bs-original-title="در صورتیکه نیاز دارید در هر زمانی کاربران دعوت شده بتوانند بدون تایید وارد اتاق شما شوند این گزینه را فعال نمایید. مناسب جلسات آموزشی برنامه ریزی نشده و جلسات سازمانی که زمان بندی مشخصی ندارند"
                                               aria-label="در صورتیکه نیاز دارید در هر زمانی کاربران دعوت شده بتوانند بدون تایید وارد اتاق شما شوند این گزینه را فعال نمایید. مناسب جلسات آموزشی برنامه ریزی نشده و جلسات سازمانی که زمان بندی مشخصی ندارند"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="separator separator-dashed mb-8"></div>

                                <h5 class="fw-bold text-dark mb-4">تنظیمات زمان</h5>

                                <div class="row g-5">
                                    <div class="col-md-8">
                                        <label class="form-label required">آغاز در</label>
                                        <div class="row g-3">
                                            <div class="col-md-7 direction-ltr">
                                                <div class="input-group">
                                                    <input type="hidden"
                                                           class="form-control text-center"
                                                           wire:model="settings.start_date"
                                                           id="settings_start_date_holder"/>

                                                    <input type="text" class="form-control"
                                                           wire:ignore.self
                                                           id="start-date-picker"
                                                           value="{{$settings->start_date}}"
                                                           wire:loading.class="disabled"/>
                                                    <span class="input-group-text">
                                                       <i class="las la-calendar fs-1"></i>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="col-md-5">
                                                <div class="d-flex align-items-center direction-ltr">
                                                    <input type="number"
                                                           class="form-control text-center"
                                                           wire:model="settings.start_date_hour"
                                                           max="23" min="0"/>
                                                    <span class="mx-2">:</span>
                                                    <input type="number"
                                                           class="form-control text-center"
                                                           wire:model="settings.start_date_minute"
                                                           max="59" min="0"/>
                                                    <label class="form-label me-2">ساعت</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <label class="form-label required">مدت زمان جلسه</label>
                                        <!-- Preset Durations -->
                                        <div class="input-group direction-ltr">
                                            <input type="number" class="form-control"
                                                   wire:model="settings.meeting_duration" max="1440" min="5"/>
                                            <span class="input-group-text">دقیقه</span>
                                        </div>


                                    </div>
                                </div>

                                <!--begin::Repeat-->
                                <div class="separator separator-dashed my-8"></div>

                                <div class="d-flex flex-stack mb-5">
                                    <div class="me-5">
                                        <label class="fs-5 fw-bold form-label">تکرار جلسات</label>
                                        <div class="fs-7 fw-semibold text-muted">
                                            آیا این جلسه شما به صورت دوره‌ای تکرار
                                            می‌شود؟
                                        </div>
                                    </div>
                                    <div class="form-check form-switch form-check-custom form-check-solid">
                                        <input class="form-check-input w-50px h-30px" type="checkbox"
                                               wire:model.live="settings.repeat_status"
                                               value="1"/>
                                    </div>
                                </div>

                                <div id="check_repeat_status"
                                     class="p-6 bg-light-secondary border border-dashed rounded-3"
                                     @if($settings->repeat_status != '1') style="display: none" @endif>

                                    <div class="row g-5 mb-8">
                                        <div class="col-md-5">
                                            <label class="form-label required">تکرار هر</label>
                                            <input type="number" class="form-control"
                                                   wire:model="settings.recurrence_interval" value="1"/>
                                        </div>
                                        <div class="col-md-7">
                                            <label class="form-label required">دوره تناوب</label>
                                            <select class="form-select "
                                                    wire:model.live="settings.recurrence_frequency">
                                                <option value="DAILY">روز</option>
                                                <option value="WEEKLY">هفته</option>
                                                <option value="MONTHLY">ماه</option>
                                            </select>
                                        </div>
                                    </div>

                                    @if($settings->recurrence_frequency === 'WEEKLY')
                                        <div class="mb-8">
                                            <label class="form-label required">در این روزها تکرار شود</label>
                                            <div class="btn-group w-100 direction-ltr">
                                                <label
                                                    class="btn btn-sm btn-outline btn-color-muted btn-active-primary {{ in_array('SA', $settings->recurrence_days ?? []) ? 'active' : '' }}">
                                                    <input class="btn-check" type="checkbox"
                                                           wire:model.live="settings.recurrence_days" value="SA"/>ش
                                                </label>
                                                <label
                                                    class="btn btn-sm btn-outline btn-color-muted btn-active-primary {{ in_array('SU', $settings->recurrence_days ?? []) ? 'active' : '' }}">
                                                    <input class="btn-check" type="checkbox"
                                                           wire:model.live="settings.recurrence_days" value="SU"/>ی
                                                </label>
                                                <label
                                                    class="btn btn-sm btn-outline btn-color-muted btn-active-primary {{ in_array('MO', $settings->recurrence_days ?? []) ? 'active' : '' }}">
                                                    <input class="btn-check" type="checkbox"
                                                           wire:model.live="settings.recurrence_days" value="MO"/>د
                                                </label>
                                                <label
                                                    class="btn btn-sm btn-outline btn-color-muted btn-active-primary {{ in_array('TU', $settings->recurrence_days ?? []) ? 'active' : '' }}">
                                                    <input class="btn-check" type="checkbox"
                                                           wire:model.live="settings.recurrence_days" value="TU"/>س
                                                </label>
                                                <label
                                                    class="btn btn-sm btn-outline btn-color-muted btn-active-primary {{ in_array('WE', $settings->recurrence_days ?? []) ? 'active' : '' }}">
                                                    <input class="btn-check" type="checkbox"
                                                           wire:model.live="settings.recurrence_days" value="WE"/>چ
                                                </label>
                                                <label
                                                    class="btn btn-sm btn-outline btn-color-muted btn-active-primary {{ in_array('TH', $settings->recurrence_days ?? []) ? 'active' : '' }}">
                                                    <input class="btn-check" type="checkbox"
                                                           wire:model.live="settings.recurrence_days" value="TH"/>پ
                                                </label>
                                                <label
                                                    class="btn btn-sm btn-outline btn-color-muted btn-active-primary {{ in_array('FR', $settings->recurrence_days ?? []) ? 'active' : '' }}">
                                                    <input class="btn-check" type="checkbox"
                                                           wire:model.live="settings.recurrence_days" value="FR"/>ج
                                                </label>
                                            </div>
                                        </div>
                                    @endif

                                    <div>
                                        <label class="form-label">پایان تکرار</label>
                                        <div class="d-flex flex-column gap-5">
                                            <div class="form-check form-check-custom form-check-solid">
                                                <input class="form-check-input" type="radio"
                                                       wire:model="settings.recurrence_until_radio_all_time"
                                                       value="1"
                                                       id="recurrence_never" name="recurrence_type"/>
                                                <label class="form-check-label" for="recurrence_never">
                                                    همیشه فعال باشد (حداکثر ۶ ماه تنظیم خواهد شد)</label>
                                            </div>
                                            <div class="form-check form-check-custom form-check-solid">
                                                <input class="form-check-input" type="radio"
                                                       wire:model="settings.recurrence_until_radio_specific_date"
                                                       value="1"
                                                       id="recurrence_on_date"
                                                       name="recurrence_type"/>

                                                <label class="form-check-label d-flex align-items-center"
                                                       for="recurrence_on_date">
                                                    <span>در تاریخ</span>
                                                    <input type="text"
                                                           class="form-control form-control-sm form-control-solid ms-3 w-150px"
                                                           wire:ignore.self
                                                           wire:model="settings.recurrence_until"
                                                           id="daterange-picker"
                                                           wire:loading.class="disabled"
                                                    />
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <!--end::Repeat-->
                            </div>
                        </div>
                        <!--end::Tab pane-->

                        <!--begin::Tab pane-->
                        <div class="tab-pane fade" id="kt_tab_notifications" role="tabpanel" wire:ignore.self>
                            <div class="framer-section">
                                <h3 class="fw-bold text-dark mb-4">
                                    تنظیمات اطلاع‌رسانی
                                </h3>
                                <div class="form-text mb-6">در صورتیکه اتاق شما متناوب باشد قبل از شروع هر اتاق به
                                    کاربران دعوت شده از طریق گوگل کلندر اطلاع رسانی خواهد شد.
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h4 class="fw-bold mb-3">ایمیل</h4>
                                        <div class="d-flex flex-column gap-3">
                                            <div class="form-check form-check-custom form-check-solid">
                                                <input class="form-check-input" type="checkbox"
                                                       wire:model="settings.reminders"
                                                       value="email:10"/>
                                                <label class="form-check-label">۱۰ دقیقه قبل</label>
                                            </div>
                                            <div class="form-check form-check-custom form-check-solid">
                                                <input class="form-check-input" type="checkbox"
                                                       wire:model="settings.reminders"
                                                       value="email:30"/>
                                                <label class="form-check-label">۳۰ دقیقه قبل</label>
                                            </div>
                                            <div class="form-check form-check-custom form-check-solid">
                                                <input class="form-check-input" type="checkbox"
                                                       wire:model="settings.reminders"
                                                       value="email:60"/>
                                                <label class="form-check-label">۱ ساعت قبل</label>
                                            </div>
                                            <div class="form-check form-check-custom form-check-solid">
                                                <input class="form-check-input" type="checkbox"
                                                       wire:model="settings.reminders"
                                                       value="email:1440"/>
                                                <label class="form-check-label">۱ روز قبل</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h4 class="fw-bold mb-3">اعلان</h4>
                                        <div class="d-flex flex-column gap-3">
                                            <div class="form-check form-check-custom form-check-solid">
                                                <input class="form-check-input" type="checkbox"
                                                       wire:model="settings.reminders"
                                                       value="popup:10"/>
                                                <label class="form-check-label">۱۰ دقیقه قبل</label>
                                            </div>
                                            <div class="form-check form-check-custom form-check-solid">
                                                <input class="form-check-input" type="checkbox"
                                                       wire:model="settings.reminders"
                                                       value="popup:30"/>
                                                <label class="form-check-label">۳۰ دقیقه قبل</label>
                                            </div>
                                            <div class="form-check form-check-custom form-check-solid">
                                                <input class="form-check-input" type="checkbox"
                                                       wire:model="settings.reminders"
                                                       value="popup:60"/>
                                                <label class="form-check-label">۱ ساعت قبل</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--end::Tab pane-->
                    </div>
                    <!--end::Tab content-->
                </div>
                <!--end::Content-->
            </div>
        </div>
        <!--end::Card body-->

        <!--begin::Card footer-->
        <div class="card-footer d-flex justify-content-end py-6 px-9"
             style="    position: sticky;    bottom: 0px;    background: #fff;">
            <button type="button" class="btn btn-primary"
                    wire:loading.class="disabled"
                    wire:target="saveSetting"
                    wire:click="saveSetting">
                <span wire:loading.remove wire:target="saveSetting">
                   <i class="las la-save fs-1"></i>
                    ذخیره تغییرات
                </span>
                <span wire:loading wire:target="saveSetting">
                    در حال ذخیره سازی...
                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </span>
            </button>
        </div>
        <!--end::Card footer-->
    </div>
</div>

@push('scripts')
    <script>
        window.eyeControl = function (event) {
            let input = $(event.currentTarget).siblings('input[data-type="password"]');
            let eyeIcon = $(event.currentTarget).find('.ki-eye');
            let eyeSlashIcon = $(event.currentTarget).find('.ki-eye-slash');

            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
                eyeIcon.removeClass('d-none');
                eyeSlashIcon.addClass('d-none');
            } else {
                input.attr('type', 'password');
                eyeIcon.addClass('d-none');
                eyeSlashIcon.removeClass('d-none');
            }
        };

        $('#daterange-picker').attr("readonly", true)
        $('#daterange-picker').persianDatepicker({
            locale: 'fa',
            format: 'L',
            autoClose: true,
            onSelect: function (unix) {
                const dateStr = new persianDate(unix).format('YYYY/MM/DD');
                @this.
                set('settings.recurrence_until', dateStr);
            }
        });

        $('#start-date-picker').attr("readonly", true)
        $('#start-date-picker').persianDatepicker({
            locale: 'fa',
            format: 'L',
            initialValueType: 'gregorian',
            autoClose: true,
            onSelect: function (unix) {
                const dateStr = new persianDate(unix).format('YYYY/MM/DD');
                @this.
                set('settings.start_date', dateStr);
            }
        });

        document.addEventListener('livewire:navigated', function () {
            const copyButton = document.getElementById('copy_slug_button');
            if (copyButton && !copyButton.dataset.listenerAttached) {
                const inputEl = document.getElementById('public_page_slug_input');

                copyButton.addEventListener('click', function () {
                    const slug = inputEl.value;
                    if (!slug) return; // Do nothing if the input is empty

                    const fullUrl = `{{config('app.url')}}` + `show/${slug}`;

                    navigator.clipboard.writeText(fullUrl).then(() => {
                        // --- Success Feedback ---
                        const icon = copyButton.querySelector('i');
                        if (!icon) return;

                        const originalIconClass = icon.className;

                        // 1. Change icon to checkmark
                        icon.className = 'las la-check fs-1 text-success';

                        // 2. Show "Copied!" tooltip
                        const tooltip = new bootstrap.Tooltip(copyButton, {
                            title: 'کپی شد!',
                            trigger: 'manual',
                            placement: 'top'
                        });
                        tooltip.show();

                        // 3. Revert back after 2 seconds
                        setTimeout(() => {
                            tooltip.hide();
                            icon.className = originalIconClass;
                            tooltip.dispose(); // Clean up tooltip instance
                        }, 2000);

                    }).catch(err => {
                        console.error('Failed to copy text: ', err);
                    });
                });

                copyButton.dataset.listenerAttached = 'true';
            }
        });

        document.addEventListener('livewire:navigated', () => {
            const recurrenceDatePicker = document.getElementById('daterange-picker');
            if (recurrenceDatePicker) {
                recurrenceDatePicker.addEventListener('change', (e) => {
                    document.getElementById('recurrence_on_date').click();
                });
            }
        });

        function setupThemeSwitcher() {
            const themeRadios = document.querySelectorAll('input[name="theme"]');
            const previewBody = document.getElementById('live-preview-body');
            const themeLabels = document.querySelectorAll('.theme-preview-label');

            if (!previewBody || themeRadios.length === 0) {
                return;
            }

            const themes = ['1', '2', '3', '4'];

            function applyTheme(selectedTheme) {
                // Update preview pane
                themes.forEach(theme => previewBody.classList.remove(`theme-${theme}`));
                previewBody.classList.add(`theme-${selectedTheme}`);

                // Update active state on labels
                themeLabels.forEach(label => {
                    const input = label.querySelector('input');
                    if (input && input.value === selectedTheme) {
                        label.classList.add('active');
                    } else {
                        label.classList.remove('active');
                    }
                });
            }

            themeRadios.forEach(radio => {
                radio.addEventListener('change', (event) => {
                    applyTheme(event.target.value);
                });
            });

            // Set initial state
            const initiallyChecked = document.querySelector('input[name="theme"]:checked');
            if (initiallyChecked) {
                applyTheme(initiallyChecked.value);
            }
        }

        document.addEventListener('livewire:navigated', setupThemeSwitcher);
        setupThemeSwitcher(); // Also run on initial page load

        // Function to update all date pickers with current Livewire values
        function updateAllDatePickersFromLivewire() {
            // Update start date picker
            const startDateValue = @this.
            get('settings.start_date');
            if (startDateValue) {
                const startDatePicker = $('#start-date-picker').data('datepicker');
                if (startDatePicker) {
                    try {
                        const dateObj = new persianDate(startDateValue);
                        startDatePicker.setDate(dateObj.valueOf());
                    } catch (e) {
                        console.error('Error updating start date picker:', e);
                    }
                }
            }

            // Update recurrence until date picker
            const recurrenceUntilValue = @this.
            get('settings.recurrence_until');
            if (recurrenceUntilValue) {
                const recurrenceDatePicker = $('#daterange-picker').data('datepicker');
                if (recurrenceDatePicker) {
                    try {
                        const dateObj = new persianDate(recurrenceUntilValue);
                        recurrenceDatePicker.setDate(dateObj.valueOf());
                    } catch (e) {
                        console.error('Error updating recurrence date picker:', e);
                    }
                }
            }

            // Add any other date pickers that need to be updated here
        }

        // Listen for the event from Livewire
        document.addEventListener('livewire:initialized', () => {
            Livewire.on('updateDatePickers', updateAllDatePickersFromLivewire);
        });
    </script>
@endpush
