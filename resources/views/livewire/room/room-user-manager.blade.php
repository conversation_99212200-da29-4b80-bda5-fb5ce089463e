<div>
    <div class="card mb-5 mb-xxl-8">
        <!--begin::Header-->
        <div class="card-header align-items-center border-0 mt-5 direction-rtl">
            <h3 class="card-title align-items-start flex-column">
                <span class="fw-bolder text-dark fs-3">لیست مهمان ها</span>
                <span class="text-muted mt-2 fw-bold fs-6">
                    در صورتیکه وضعیت دسترسی اتاق شما روی "دعوت شده" تنظیم شده باشند و زمانبندی جلسه تنظیم شده باشد، افراد این لیست میتوانند وارد شوند
                </span>
            </h3>

            <div class="card-toolbar">

                @if($isListDirty)
                    <a wire:click="saveDirty()"
                       class="btn btn-light-warning btn-icon btn-active-light-primary btn-sm me-2  pulse-warning w-125px"
                       data-bs-toggle="tooltip" title="" data-bs-original-title="همگام سازی با رویداد"
                       wire:loading.class="disabled"
                       wire:target="saveDirty"
                    >

                        <span wire:loading.remove wire:target="saveDirty">
                               <i class="fas fa-save fs-6 me-3 position-relative">
                                   <span class="pulse-ring" style=" top: -15px; right: -14px; "></span>
                               </i>
                       ذخیره نهایی
                        </span>
                        <span wire:loading wire:target="saveDirty">
                            <span class="spinner-border spinner-border-sm align-middle"></span>
                        </span>

                    </a>

                    <a wire:click="cancelListChanges()" class="btn btn-icon btn-active-light-primary btn-sm me-2"
                       data-bs-toggle="tooltip"
                       title="" data-bs-original-title="انصراف">
                        <i class="fas fa-trash fs-6"></i>
                    </a>
                @endif
                <!--begin::Dropdown-->
                <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                        data-kt-menu-trigger="hover" data-kt-menu-placement="bottom-end">
                    <!--begin::Svg Icon | path: icons/duotune/general/gen024.svg-->
                    <span class="svg-icon svg-icon-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24px"
                                         height="24px" viewBox="0 0 24 24">
                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                         <rect x="5" y="5" width="5" height="5" rx="1" fill="#000000"></rect>
                                            <rect x="14" y="5" width="5" height="5" rx="1" fill="#000000"
                                                  opacity="0.3"></rect>
                                            <rect x="5" y="14" width="5" height="5" rx="1" fill="#000000"
                                                  opacity="0.3"></rect>
                                            <rect x="14" y="14" width="5" height="5" rx="1" fill="#000000"
                                                  opacity="0.3"></rect>
                                        </g>
                                    </svg>
                                </span>
                    <!--end::Svg Icon-->            </button>
                <!--begin::Menu-->
                <div
                    class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold w-200px"
                    data-kt-menu="true">
                    <div class="menu-item px-3">
                        <div class="menu-content fs-6 text-dark fw-bolder px-3 py-4">مدیریت لیست</div>
                    </div>

                    <div class="separator mb-3 opacity-75"></div>

                    <div class="menu-item px-3">
                        <a data-bs-toggle="modal" data-bs-target="#kt_modal_add_new_guest" class="menu-link px-3">
                            اضافه کردن مهمان فقط با ایمیل
                        </a>
                    </div>

                    <div class="menu-item px-3">

                        <a data-bs-toggle="modal" data-bs-target="#kt_modal_add_new_admin" class="menu-link px-3">
                            اضافه کردن مدیر فقط با ایمیل
                        </a>
                    </div>

                    <div class="separator mt-3 opacity-75"></div>

                    <div class="menu-item px-3">
                        <div class="menu-content px-3 py-3">
                            {{--                                        <a class="btn btn-primary fw-bold btn-sm px-4" href="#">--}}
                            {{--                                         --}}
                            {{--                                        </a>--}}
                        </div>
                    </div>
                </div>
                <!--end::Menu-->
                <!--end::Dropdown-->
            </div>
        </div>
        <!--end::Header-->

        <!--begin::Body-->
        <div class="card-body pt-0" style=" max-height: 400px; overflow: auto; ">
            <!--begin::Table-->
            <div class="table-responsive">
                <table class="table table-borderless align-middle mb-0 direction-rtl">
                    <thead>
                    <tr>
                        <th class="p-0"></th>
                        <th class="p-0"></th>
                        <th class="p-0 min-w-70px"></th>
                    </tr>
                    </thead>
                    <tbody>

                    <tr>
                        <hr/>
                    </tr>

                    <tr>
                        <td class="ps-0 text-align-right">
                            <div class="symbol  symbol-45px symbol-circle mr-5 " style=" margin-left: 10px; "><span
                                    class="symbol-label  bg-light-danger text-danger fs-6 fw-bolder ">
                                        {{strtoupper($mainAdminEmail[0])}}
                                    </span>
                            </div>

                            <span
                                class="text-dark fw-bolder text-hover-primary fs-6 userList">{{$mainAdminEmail}}</span>
                        </td>
                        <td class="text-end pe-0">
                            <span class="text-muted fw-bold d-block mt-1">سازنده اتاق</span>
                        </td>
                        <td class="text-end pe-0">
                            <a wire:click="editMainAdmin()"
                               class="btn btn-icon btn-bg-light btn-active-primary btn-sm"
                               data-bs-toggle="tooltip"
                               title="ویرایش ایمیل سازنده">
                                <span class="svg-icon svg-icon-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                         fill="none">
                                        <path
                                            d="M3.69601 15.8112L2.15894 19.91C1.70676 21.1158 2.88429 22.2934 4.09012 21.8412L8.18896 20.3041C8.72361 20.1036 9.20914 19.791 9.6129 19.3872L10 19L5 14L4.6129 14.3872C4.20914 14.791 3.8965 15.2765 3.69601 15.8112Z"
                                            fill="black"/>
                                        <path opacity="0.25" d="M5 14L10 19L19.5 9.5L14.5 4.5L5 14Z" fill="black"/>
                                    </svg>
                                </span>
                            </a>
                        </td>
                    </tr>

                    @foreach($initialRoomAdminList as $admin)
                        <tr>
                            <td class="ps-0 text-align-right">

                                <div class="symbol  symbol-45px symbol-circle mr-5 " style=" margin-left: 10px; ">
                                    <span
                                        class="symbol-label  bg-light-danger text-danger fs-6 fw-bolder ">
                                        {{strtoupper($admin->email[0])}}
                                    </span>
                                </div>

                                <span
                                    class="text-dark fw-bolder text-hover-primary fs-6 userList">{{$admin->email}} ({{$admin->first_name .' '. $admin->last_name}})</span>

                            </td>

                            <td class="text-end pe-0">
                                <span class="text-muted fw-bold d-block mt-1">{{$admin->phone}}</span>
                            </td>

                            <td class="text-end pe-0">
                                <span class="text-muted fw-bold d-block mt-1">مدیر</span>
                            </td>
                            <td class="text-end pe-0">
                                <a wire:click="removeEmail('{{$admin->email}}')" wire:key="{{$admin->email}}"
                                   wire:loading.class="disabled"
                                   wire:target="removeEmail('{{$admin->email}}')"
                                   class="btn btn-icon btn-bg-light btn-active-primary btn-sm">
                                    <span wire:loading.remove wire:target="removeEmail('{{$admin->email}}')"
                                          class="svg-icon svg-icon-4">

                                               <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none">
                                                    <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1"
                                                          transform="rotate(-45 6 17.3137)" fill="black"></rect>
                                                    <rect x="7.41422" y="6" width="16" height="2" rx="1"
                                                          transform="rotate(45 7.41422 6)" fill="black"></rect>
                                                </svg>

                                    </span>
                                    <span wire:loading wire:target="removeEmail('{{$admin->email}}')">
                                        <span class="spinner-border spinner-border-sm align-middle"></span>
                                    </span>
                                </a>
                            </td>
                        </tr>
                    @endforeach

                    @foreach($initialRoomGuestList as $guest)
                        <tr>
                            <td class="ps-0 text-align-right">

                                <div class="symbol  symbol-45px symbol-circle mr-5 " style=" margin-left: 10px; "><span
                                        class="symbol-label  bg-light-danger text-danger fs-6 fw-bolder ">
                                        {{strtoupper($guest->email[0])}}
                                    </span>
                                </div>

                                <span
                                    class="text-dark fw-bolder text-hover-primary fs-6 userList">{{$guest->email}}
                                    ({{$guest->first_name .' '. $guest->last_name}})</span>

                            </td>
                            <td class="text-end pe-0">
                                <span class="text-muted fw-bold d-block mt-1">{{$guest->phone}}</span>
                            </td>


                            <td class="text-end pe-0">
                                <span class="text-muted fw-bold d-block mt-1">مهمان</span>
                            </td>
                            <td class="text-end pe-0">
                                <a wire:click="removeEmail('{{$guest->email}}')" wire:key="{{$guest->email}}"
                                   wire:loading.class="disabled"
                                   wire:target="removeEmail('{{$guest->email}}')"
                                   class="btn btn-icon btn-bg-light btn-active-primary btn-sm">
                                    <span wire:loading.remove wire:target="removeEmail('{{$guest->email}}')"
                                          class="svg-icon svg-icon-4">

                                               <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none">
                                                    <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1"
                                                          transform="rotate(-45 6 17.3137)" fill="black"></rect>
                                                    <rect x="7.41422" y="6" width="16" height="2" rx="1"
                                                          transform="rotate(45 7.41422 6)" fill="black"></rect>
                                                </svg>

                                    </span>
                                    <span wire:loading wire:target="removeEmail('{{$guest->email}}')">
                                        <span class="spinner-border spinner-border-sm align-middle"></span>
                                    </span>
                                </a>
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
            <!--end::Table-->
        </div>
        <!--end: Card Body-->
    </div>


    <div class="modal fade" tabindex="-1" id="kt_modal_add_new_guest" wire:ignore>
        <div class="modal-dialog direction-rtl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">افزودن مهمان جدید</h5>

                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                         aria-label="Close">
                        <span class="svg-icon svg-icon-2x"></span>
                    </div>
                    <!--end::Close-->
                </div>

                <div class="modal-body">
                    <div class="mb-10">
                        <label class="form-label">آدرس ایمیل ها را وارد نمایید. میتوانید از حافظه پیست کنید و یا در زیر تایپ کنید</label>
                        <input class="form-control" value="{{$initialRoomGuestListJs}}"
                               id="kt_tagify_1"/>
                    </div>

                    <div class="alert alert-warning alert-dismissible fade show mt-2 direction-rtl" role="alert">
                        <span class="fw-bold">توجه:</span>
                        در این قسمت تنها امکان درج ایمیل وجود دارد. در صورت تمایل استفاده از سرویس پنل پیامک، بهتر است کاربران خود را توسط گزینه افزودن توسط اکسل، اضافه نمایید.
                    </div>

                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">بستن</button>
                    <button type="button" class="btn btn-primary" id="saveUserList" wire:loading.class="disabled">
                        <span wire:loading.remove wire:target="saveUserList">ذخیره لیست</span>
                        <span wire:loading wire:target="saveUserList">
                            <span class="spinner-border spinner-border-sm align-middle"></span>
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>


    <div class="modal fade" tabindex="-1" id="kt_modal_add_new_admin" wire:ignore>
        <div class="modal-dialog direction-rtl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">افزودن مدیر جدید</h5>

                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                         aria-label="Close">
                        <span class="svg-icon svg-icon-2x"></span>
                    </div>
                    <!--end::Close-->
                </div>


                <div class="modal-body">
                    <div class="mb-10">
                        <label class="form-label">آدرس ایمیل ها را وارد نمایید. میتوانید از حافظه پیست کنید و یا در زیر تایپ کنید</label>
                        <input class="form-control" value="{{$initialRoomAdminListJs}}"
                               id="kt_tagify_2"/>
                    </div>

                    <div class="alert alert-warning alert-dismissible fade show mt-2 direction-rtl" role="alert">
                        <span class="fw-bold">توجه:</span>
                        در این قسمت تنها امکان درج ایمیل وجود دارد. در صورت تمایل استفاده از سرویس پنل پیامک، بهتر است کاربران خود را توسط گزینه افزودن توسط اکسل، اضافه نمایید.
                    </div>

                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">بستن</button>
                    <button type="button" class="btn btn-primary" id="saveAdminList" wire:loading.class="disabled">
                        <span wire:loading.remove wire:target="saveAdminList">ذخیره لیست</span>
                        <span wire:loading wire:target="saveAdminList">
                            <span class="spinner-border spinner-border-sm align-middle"></span>
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>


    <div class="modal fade" tabindex="-1" id="kt_modal_edit_main_admin" wire:ignore>
        <div class="modal-dialog direction-rtl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ویرایش ایمیل سازنده اتاق</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                         aria-label="Close">
                        <span class="svg-icon svg-icon-2x"></span>
                    </div>
                </div>

                <div class="modal-body">
                    <div class="mb-10">
                        <p>
                            شما باید حداقل یک ایمیل به عنوان ایمیل اصلی اتاق خود معرفی کنید. در صورت تمایل میتوانید آن
                            را تغییر دهید. اما امکان حذف آن وجود ندارد
                        </p>
                        <label class="form-label">ایمیل جدید سازنده اتاق</label>
                        <input type="email" class="form-control" wire:model="newMainAdminEmail"
                               placeholder="<EMAIL>"/>
                        @error('newMainAdminEmail')
                        <div class="text-danger mt-2">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">بستن</button>
                    <button type="button" class="btn btn-primary" wire:click="updateMainAdminEmail"
                            wire:loading.class="disabled">
                        <span wire:loading.remove wire:target="updateMainAdminEmail">ذخیره تغییرات</span>
                        <span wire:loading wire:target="updateMainAdminEmail">
                            <span class="spinner-border spinner-border-sm align-middle"></span>
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>


</div>


@push('scripts')
    <script>
        // The DOM elements you wish to replace with Tagify
        var input1 = document.querySelector("#kt_tagify_1");
        var admin = document.querySelector("#kt_tagify_2");
        var randomStringsArr = [];
        // Initialize Tagify components on the above inputs
        var tagify = new Tagify(input1, {
            // email address validation (https://stackoverflow.com/a/46181/104380)
            pattern: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
            whitelist: randomStringsArr,
            delimiters: ",|\n|\r"
        });

        var tagifyAdmin = new Tagify(admin, {
            pattern: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
            whitelist: randomStringsArr,
            delimiters: ",|\n|\r"
        });


        var userList = "";
        var AdminList = "";

        tagify.on('add remove click', e => {
            userList = "";
            tagify.value.map(function (item) {
                userList += (item.value) + ",";
            })
        })

        tagifyAdmin.on('add remove click', e => {
            AdminList = "";
            tagifyAdmin.value.map(function (item) {
                AdminList += (item.value) + ",";
            })
        })

        $('#saveUserList').on('click', function () {
            @this.
            roomGuestList = userList;
            @this.
            saveUserList();
        })

        $('#saveAdminList').on('click', function () {
            @this.
            roomAdminList = AdminList;
            @this.
            saveAdminList();
        })

        Livewire.on('show_modal', (data) => {
            $('#' + data[0].modalId).modal('show');
        });
    </script>
@endpush
