<div>
    @if($step === 1)
        <!-- Upload Step -->
        <div class="text-center mb-8">
            <h4>آپلود فایل اکسل کاربران</h4>
            <p class="text-muted">فایل نمونه را دریافت کنید و مطابق محتوای نمونه داده های خود را در زیر بارگزاری نمایید. </p>
            <div class="mb-6">
                <button wire:click="downloadSample" class="btn btn-light btn-sm">
                    <i class="bi bi-download"></i> دانلود فایل نمونه
                </button>
            </div>
        </div>



        <div class="mb-6">
            <label class="form-label">انتخاب فایل اکسل</label>
            <input type="file" class="form-control" wire:model="excelFile" accept=".xlsx,.xls,.csv">
            @error('excelFile')
            <div class="text-danger mt-2">{{ $message }}</div>
            @enderror
        </div>

        <div wire:loading wire:target="excelFile" class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">در حال پردازش...</span>
            </div>
            <p class="mt-2">در حال پردازش فایل...</p>
        </div>

    @elseif($step === 2)
        <!-- Preview Step -->
        <div class="mb-6">
            <h4>پیش‌نمایش و بررسی داده‌ها</h4>

            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-light-primary">
                        <div class="card-body text-center">
                            <h5 class="text-primary">{{ $totalUsers }}</h5>
                            <p class="mb-0">کل کاربران</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light-success">
                        <div class="card-body text-center">
                            <h5 class="text-success">{{ $validUsers }}</h5>
                            <p class="mb-0">معتبر</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light-danger">
                        <div class="card-body text-center">
                            <h5 class="text-danger">{{ $invalidUsers }}</h5>
                            <p class="mb-0">نامعتبر</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-responsive mb-6" style="max-height: 400px; overflow-y: auto;">
            <table class="table table-bordered">
                <thead class="table-light">
                <tr>
                    <th>ردیف</th>
                    <th>ایمیل</th>
                    <th>نوع</th>
                    <th>شماره موبایل</th>
                    <th>نام</th>
                    <th>نام خانوادگی</th>
                    <th>وضعیت</th>
                    <th>خطاها</th>
                </tr>
                </thead>
                <tbody>
                @foreach($previewData as $user)
                    <tr class="{{ $user['is_valid'] ? 'table-success' : 'table-danger' }}">
                        <td>{{ $user['row'] }}</td>
                        <td>{{ $user['email'] }}</td>
                        <td>{{ $user['type'] }}</td>
                        <td>{{ $user['phone'] }}</td>
                        <td>{{ $user['firstName'] }}</td>
                        <td>{{ $user['lastName'] }}</td>
                        <td>
                            @if($user['is_valid'])
                                <span class="badge badge-success">معتبر</span>
                            @else
                                <span class="badge badge-danger">نامعتبر</span>
                            @endif
                        </td>
                        <td>
                            @if(!empty($user['errors']))
                                <ul class="mb-0 text-danger">
                                    @foreach($user['errors'] as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            @endif
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>

        <div class="d-flex justify-content-between">
            <button wire:click="resetUpload" class="btn btn-light">
                بازگشت
            </button>
            <button wire:click="uploadUsers" class="btn btn-primary" wire:loading.class="disabled"
                {{ $invalidUsers > 0 ? 'disabled' : '' }}>
                آپلود کاربران ({{ $validUsers }})
                <div wire:loading wire:target="uploadUsers">
                    <span class="spinner-border spinner-border-sm align-middle m-1"></span>
                </div>
            </button>
        </div>

    @elseif($step === 3)
        <!-- Processing Step -->
        <div class="text-center">
            <div class="spinner-border text-primary mb-4" role="status">
                <span class="visually-hidden">در حال آپلود...</span>
            </div>
            <h4>در حال آپلود کاربران...</h4>
            <p class="text-muted">لطفا صبر کنید</p>
        </div>
    @endif
</div>

@push('scripts')
    <script>
        Livewire.on('close_modal', () => {
            $('#kt_modal_upload_excel').modal('hide');
        });
    </script>
@endpush
