<div class="direction-rtl">
    <!-- Page Title -->
    <div class="mb-5 px-3 px-md-0">
        <h1 class="fs-2x fw-bolder">گزارشات اتاق</h1>
        <p class="text-muted fs-6">اطلاعات کامل و گزارشات مربوط به این اتاق.</p>
    </div>

    <div class="row g-5 g-xl-8 px-3 px-md-0">
        <!-- Left Column: Report Generation & Meeting History -->
        <div class="col-xl-4">

            <!-- Card 2: Meeting History -->
            <div class="card card-flush">
                <div class="card-header">
                    <h3 class="card-title align-items-start flex-column">
                        <span class="card-label fw-bolder text-dark fs-3">جلسات این اتاق</span>
                        <span class="text-muted mt-1 fw-semibold fs-7">تاریخچه جلسات برگزار شده</span>
                    </h3>
                </div>
                <div class="card-body pt-3" style="    max-height: 354px;    overflow: auto;">
                    @if($reportList && count($reportList) > 0)
                        <div class="table-responsive">
                            <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4 fs-6">
                                <thead>
                                <tr class="fw-bolder text-muted">
                                    <th class="min-w-120px">تاریخ جلسه</th>
                                    <th class="min-w-100px">مدت زمان</th>
                                    <th class="min-w-100px text-end">اقدامات</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($reportList as $report)
                                    <tr>
                                        <td>{{\Morilog\Jalali\Jalalian::fromDateTime(\Illuminate\Support\Carbon::parse($report->start_time)->setTimezone(config('app.timezone')))->format('Y/m/d H:i')}}</td>
                                        <td>{{\Carbon\CarbonInterval::seconds($report->duration_second)->cascade()->forHumans()}}</td>
                                        <td class="text-end">
                                            <a href="{{route('room.audits.report',['id'=>$meetingId,'meetingId'=>$report->meeting_id])}}"
                                               class="btn btn-sm btn-light-primary">مشاهده</a>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center text-muted py-5">
                            <p>جلسه‌ای برای این اتاق ثبت نشده است.</p>
                        </div>
                    @endif
                </div>
            </div>
            <!-- Card 1: Generate Custom Report -->
            <div class="card card-flush mt-5">
                <div class="card-header">
                    <h3 class="card-title align-items-start flex-column">
                        <span class="card-label fw-bolder text-dark fs-3">گزارش گیری زمانی</span>
                        <span class="text-muted mt-1 fw-semibold fs-7">بازه زمانی دلخواه خود را انتخاب کنید</span>
                    </h3>
                </div>
                <div class="card-body pt-3">
                    <div class="mb-5">
                        <label class="fs-6 fw-semibold mb-2 required">از تاریخ:</label>
                        <input type="hidden" wire:model="startDate" id="SettingStartDate">
                        <input type="text" class="direction-ltr form-control form-control-solid"
                               onchange="changeLivewireModel('SettingStartDate',$(this).val())"
                               id="daterange-picker" name="startDate"
                               value="{{$startDate}}"
                               wire:ignore
                               wire:loading.attr="disabled"/>
                        @error('startDate') <span class="invalid-feedback d-block"
                                                  role="alert"><strong>{{ $message }}</strong></span> @enderror
                    </div>

                    <div class="mb-5">
                        <label class="fs-6 fw-semibold mb-2 required">تا تاریخ:</label>
                        <input type="hidden" wire:model="endDate"
                               id="SettingEndDate">

                        <input type="text"
                               class="direction-ltr form-control form-control-lg form-control-solid"
                               onchange="changeLivewireModel('SettingEndDate',$(this).val())"
                               wire:ignore
                               wire:key="das35234"
                               id="daterange-picker-end" name="endDate" placeholder=""
                               value="{{$endDate}}"
                               wire:loading.class="disabled"/>
                        @error('endDate') <span class="invalid-feedback d-block"
                                                role="alert"><strong>{{ $message }}</strong></span> @enderror
                    </div>

                    <div>
                        <button type="button" class="btn btn-primary w-100" wire:click="createChart"
                                wire:loading.attr="disabled" wire:target="createChart">
                            <span wire:loading.remove wire:target="createChart">ایجاد گزارش</span>
                            <span wire:loading wire:target="createChart">
                                در حال دریافت اطلاعات...
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                            </span>
                        </button>
                    </div>
                </div>
            </div>

        </div>

        <!-- Right Column: Charts -->
        <div class="col-xl-8">
            <div class="card card-flush">
                <div class="card-header">
                    <h3 class="card-title align-items-start flex-column">
                        <span class="card-label fw-bolder text-dark fs-3">گزارش هفته اخیر</span>
                        <span class="text-muted mt-1 fw-semibold fs-7">مدت زمان جلسات هفته اخیر شما</span>
                    </h3>
                </div>
                <div class="card-body pt-5" id="kt_stats_widget_2_tab_4">
                    <div id="kt_stats_widget_2_chart_4" style="height: 250px"></div>
                </div>
            </div>
        </div>
    </div>
    @push('scripts')
        <script>
            $('#daterange-picker').attr("readonly", true)
            $('#daterange-picker').persianDatepicker({
                locale: 'fa',
                format: 'L',
                autoClose: true,
                onSelect: function (unix) {
                    $('#daterange-picker').trigger('change')
                }
            });

            $('#daterange-picker-end').attr("readonly", true)
            $('#daterange-picker-end').persianDatepicker({
                locale: 'fa',
                format: 'L',
                autoClose: true,
                onSelect: function (unix) {
                    $('#daterange-picker-end').trigger('change')
                }
            });
        </script>
        <script>
            const labels = {!! json_encode($lastActivitiesChartDays) !!};

            var _initStatsWidget2 = function (tabSelector, chartSelector, data1, initByDefault) {
                var element = document.querySelector(chartSelector);
                var height = parseInt(KTUtil.css(element, 'height'));

                if (!element) {
                    return;
                }

                var options = {
                    series: [{
                        name: 'مجموع دقایق جلسات ',
                        data: data1
                    }],
                    chart: {
                        fontFamily: 'inherit',
                        type: 'bar',
                        height: height,
                        toolbar: {
                            show: false
                        }
                    },
                    plotOptions: {
                        bar: {
                            horizontal: false,
                            columnWidth: ['40%'],
                            endingShape: 'rounded'
                        },
                    },
                    legend: {
                        show: false
                    },
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        show: true,
                        width: 2,
                        colors: ['transparent']
                    },
                    xaxis: {
                        categories: labels,
                        axisBorder: {
                            show: false,
                        },
                        axisTicks: {
                            show: false
                        },
                        labels: {
                            style: {
                                colors: KTUtil.getCssVariableValue('--bs-gray-700'),
                                fontSize: '12px'
                            }
                        }
                    },
                    yaxis: {
                        labels: {
                            style: {
                                colors: KTUtil.getCssVariableValue('--bs-gray-700'),
                                fontSize: '12px'
                            }
                        }
                    },
                    fill: {
                        opacity: 1
                    },
                    states: {
                        normal: {
                            filter: {
                                type: 'none',
                                value: 0
                            }
                        },
                        hover: {
                            filter: {
                                type: 'none',
                                value: 0
                            }
                        },
                        active: {
                            allowMultipleDataPointsSelection: false,
                            filter: {
                                type: 'none',
                                value: 0
                            }
                        }
                    },
                    tooltip: {
                        style: {
                            fontSize: '12px'
                        },
                        y: {
                            formatter: function (val) {
                                return val + " mins"; // or customize how you like
                            }
                        }
                    },
                    colors: [KTUtil.getCssVariableValue('--bs-primary'), KTUtil.getCssVariableValue('--bs-light-primary')],
                    grid: {
                        borderColor: KTUtil.getCssVariableValue('--bs-gray-200'),
                        strokeDashArray: 4,
                        yaxis: {
                            lines: {
                                show: true
                            }
                        }
                    }
                };

                var chart = new ApexCharts(element, options);

                var init = false;
                var tab = document.querySelector(tabSelector);

                if (initByDefault === true) {
                    chart.render();
                    init = true;
                }

                if (init == false) {
                    chart.render();
                    init = true;
                }
            }
            const data1 = {!! json_encode($lastActivitiesChartDurations) !!};
            _initStatsWidget2('#kt_stats_widget_2_tab_4', '#kt_stats_widget_2_chart_4', data1, true);
        </script>
    @endpush
</div>

@assets
<style>
    .datepicker-plot-area {
        font-family: 'estedad';
    }

    div.dataTables_wrapper > .row {
        width: 100%;
        direction: ltr;
    }

    .table-responsive {
        min-width: 100%;
    }

    table.dataTable {
        width: 100% !important;
    }

    table.dataTable thead tr th, table.dataTable thead tr td {
        direction: rtl !important;
        text-align: right !important;
        font-weight: 900;
        vertical-align: middle;
    }

    div.dataTables_wrapper div.dataTables_length select {
        min-width: 67px;
    }
</style>

<link href="{{asset('assets/css/datatables.bundle.css')}}" rel="stylesheet" type="text/css"/>
<script defer src="{{asset('assets/js/datatables.bundle.js')}}"></script>
<script src="{{asset('assets/js/apexcharts.js')}}" defer></script>
<script src="{{asset('assets/js/keen-scripts.bundle.js')}}" defer></script>
@endassets
