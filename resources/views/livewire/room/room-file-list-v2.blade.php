<div>
    <div class="card card-stretch mb-5 mb-xxl-8">
        <!--begin::Body-->
        <div class="card-body pb-0">
            <div class="row g-0 g-xl-5 g-xxl-8">
                <div class="col-xl-4">
                    <!--begin::Stats Widget 4-->
                    <div class="card card-stretch mb-5 mb-xxl-8">
                        <!--begin::Body-->
                        <div class="card-body">
                            <!--begin::Section-->
                            <div class="d-flex align-items-center">
                                <!--begin::Symbol-->
                                <div class="symbol symbol-50px me-5">
                                    <span class="symbol-label bg-gray-200">
                                         <!--begin::Svg Icon | path: /var/www/preview.keenthemes.com/keenthemes/start/docs/core/html/src/media/icons/duotune/general/gen012.svg-->
<span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="14" height="21" viewBox="0 0 14 21" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3"
      d="M12 6.20001V1.20001H2V6.20001C2 6.50001 2.1 6.70001 2.3 6.90001L5.6 10.2L2.3 13.5C2.1 13.7 2 13.9 2 14.2V19.2H12V14.2C12 13.9 11.9 13.7 11.7 13.5L8.4 10.2L11.7 6.90001C11.9 6.70001 12 6.50001 12 6.20001Z"
      fill="currentColor"/>
<path
    d="M13 2.20001H1C0.4 2.20001 0 1.80001 0 1.20001C0 0.600012 0.4 0.200012 1 0.200012H13C13.6 0.200012 14 0.600012 14 1.20001C14 1.80001 13.6 2.20001 13 2.20001ZM13 18.2H10V16.2L7.7 13.9C7.3 13.5 6.7 13.5 6.3 13.9L4 16.2V18.2H1C0.4 18.2 0 18.6 0 19.2C0 19.8 0.4 20.2 1 20.2H13C13.6 20.2 14 19.8 14 19.2C14 18.6 13.6 18.2 13 18.2ZM4.4 6.20001L6.3 8.10001C6.7 8.50001 7.3 8.50001 7.7 8.10001L9.6 6.20001H4.4Z"
    fill="currentColor"/>
</svg>
</span>
                                        <!--end::Svg Icon-->
                                    </span>
                                </div>
                                <!--end::Symbol-->

                                <!--begin::Title-->
                                <div>
                                    <a class="fs-4 text-gray-800 fw-bold">{{$maxKeepFiles}} روز</a>
                                    @if($this->hasAutoStorage)
                                        <a href="{{route('licence.show',['licence' => $this->licenceId ?? 1])}}"
                                           class="ms-3 badge badge-light-success"> PAY AS YOU GO is active <i
                                                class="fas fa-check-circle text-success fs-6 ms-2"></i></a>
                                    @else
                                        <a href="{{route('licence.show',['licence' => $this->licenceId ?? 1])}}"
                                           class="ms-3 badge badge-light-danger">PAY AS YOU GO is in active</a>
                                    @endif

                                    @if($this->hasAutoStorage)
                                        <div class="fs-7 text-muted fw-semibold mt-1">حداکثر زمانی که یک فایل نگه داری
                                            خواهد
                                            شد
                                            بعد از آن روزانه محاسبه خواهد شد
                                        </div>
                                    @else
                                        <div class="fs-7 text-muted fw-semibold mt-1">حداکثر زمانی که یک فایل نگه داری
                                            خواهد
                                            شد
                                        </div>
                                    @endif

                                </div>
                                <!--end::Title-->
                            </div>
                            <!--end::Section-->

                        </div>
                        <!--end::Body-->
                    </div>
                    <!--end::Stats Widget 4-->
                </div>
                <div class="col-xl-4">
                    <!--begin::Stats Widget 4-->
                    <div class="card card-stretch mb-5 mb-xxl-8">
                        <!--begin::Body-->
                        <div class="card-body">
                            <!--begin::Section-->
                            <div class="d-flex align-items-center">
                                <!--begin::Symbol-->
                                <div class="symbol symbol-50px me-5">
                                    <span class="symbol-label bg-gray-200">
                                         <!--begin::Svg Icon | path: /var/www/preview.keenthemes.com/keenthemes/start/docs/core/html/src/media/icons/duotune/abstract/abs027.svg-->
<span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3"
      d="M21.25 18.525L13.05 21.825C12.35 22.125 11.65 22.125 10.95 21.825L2.75 18.525C1.75 18.125 1.75 16.725 2.75 16.325L4.04999 15.825L10.25 18.325C10.85 18.525 11.45 18.625 12.05 18.625C12.65 18.625 13.25 18.525 13.85 18.325L20.05 15.825L21.35 16.325C22.35 16.725 22.35 18.125 21.25 18.525ZM13.05 16.425L21.25 13.125C22.25 12.725 22.25 11.325 21.25 10.925L13.05 7.62502C12.35 7.32502 11.65 7.32502 10.95 7.62502L2.75 10.925C1.75 11.325 1.75 12.725 2.75 13.125L10.95 16.425C11.65 16.725 12.45 16.725 13.05 16.425Z"
      fill="currentColor"/>
<path
    d="M11.05 11.025L2.84998 7.725C1.84998 7.325 1.84998 5.925 2.84998 5.525L11.05 2.225C11.75 1.925 12.45 1.925 13.15 2.225L21.35 5.525C22.35 5.925 22.35 7.325 21.35 7.725L13.05 11.025C12.45 11.325 11.65 11.325 11.05 11.025Z"
    fill="currentColor"/>
</svg>
</span>
                                        <!--end::Svg Icon-->
                                        <!--end::Svg Icon-->
                                    </span>
                                </div>
                                <!--end::Symbol-->

                                <!--begin::Title-->
                                <div>
                                    <a class="fs-4 text-gray-800 fw-bold direction-ltr">{{formatBytes($totalFileSize)}}</a>
                                    <div class="fs-7 text-muted fw-semibold mt-1">حجم فایل های شما</div>
                                </div>
                                <!--end::Title-->
                            </div>
                            <!--end::Section-->

                        </div>
                        <!--end::Body-->
                    </div>
                    <!--end::Stats Widget 4-->
                </div>
                <div class="col-xl-4">
                    <!--begin::Stats Widget 4-->
                    <div class="card card-stretch mb-5 mb-xxl-8">
                        <!--begin::Body-->
                        <div class="card-body">
                            <!--begin::Section-->
                            <div class="d-flex align-items-center">
                                <!--begin::Symbol-->
                                <div class="symbol symbol-50px me-5">
                                    <span class="symbol-label bg-gray-200">
                                         <!--begin::Svg Icon | path: /var/www/preview.keenthemes.com/keenthemes/start/docs/core/html/src/media/icons/duotune/graphs/gra001.svg-->
<span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3"
      d="M14 3V21H10V3C10 2.4 10.4 2 11 2H13C13.6 2 14 2.4 14 3ZM7 14H5C4.4 14 4 14.4 4 15V21H8V15C8 14.4 7.6 14 7 14Z"
      fill="currentColor"/>
<path
    d="M21 20H20V8C20 7.4 19.6 7 19 7H17C16.4 7 16 7.4 16 8V20H3C2.4 20 2 20.4 2 21C2 21.6 2.4 22 3 22H21C21.6 22 22 21.6 22 21C22 20.4 21.6 20 21 20Z"
    fill="currentColor"/>
</svg>
</span>
                                        <!--end::Svg Icon-->
                                        <!--end::Svg Icon-->
                                    </span>
                                </div>
                                <!--end::Symbol-->

                                <!--begin::Title-->
                                <div>
                                    <a class="fs-4 text-gray-800 fw-bold">{{$finalFilesCount}} فایل</a>
                                    <div class="fs-7 text-muted fw-semibold mt-1">تعداد کل فایل های شما</div>
                                </div>
                                <!--end::Title-->
                            </div>
                            <!--end::Section-->

                        </div>
                        <!--end::Body-->
                    </div>
                    <!--end::Stats Widget 4-->
                </div>
            </div>
        </div>

    </div>
    <!--begin::Table Widget 1-->
    <div class="card card-stretch mb-5 mb-xxl-8">
        <!--begin::Header-->
        <div class="card-header border-0 pt-5">
            <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bolder text-dark fs-3">فایل های مرتبط این اتاق</span>
                <span class="text-muted mt-2 fw-bold fs-6"></span>
            </h3>

            <div class="card-toolbar">
                <!--begin::Dropdown-->
                <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                        data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                    <span class="svg-icon svg-icon-1">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z"
                                fill="currentColor"/>
                            <path opacity="0.5"
                                  d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z"
                                  fill="currentColor"/>
                            <path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z"
                                  fill="currentColor"/>
                        </svg>
                    </span>
                    <!--end::Svg Icon-->

                </button>
                <!--begin::Menu-->
                <div
                    class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold w-300px"
                    data-kt-menu="true"
                >
                    <div class="menu-item px-3">
                        <div class="menu-content fs-6 text-gray-900 fw-bold px-3 py-4">حذف فایل ها</div>
                    </div>

                    <div class="separator mb-3 opacity-75"></div>
                    @if($finalFilesCountV2 >= 1)
                        <div class="menu-item px-3">
                            <div class="separator my-3 opacity-75"></div>
                            <div class="mb-10">
                                <!--begin::Label-->
                                <label class="form-label fw-semibold">جهت حذف {{$finalFilesCountV2}} فایل عبارت "delete"
                                    را تایپ کنید:</label>
                                <!--end::Label-->

                                <!--begin::Input-->
                                <div>
                                    <input
                                        class="direction-ltr convertor form-control form-control-lg form-control-solid"
                                        type="text"
                                        data-kt-menu-dismiss="false"
                                        wire:model="deleteSafeGroupV2"/>
                                    <span class="invalid-feedback d-block" role="alert">
                                            <strong>این عملکرد غیر قابل بازگشت است</strong>
                                </span>
                                </div>
                                <button
                                    class="btn btn-sm btn-danger btn-active-light-primary me-2 mt-2"
                                    wire:loading.class="disabled" wire:target="removeGroupFilesV2()"
                                    onclick=" @this.set('selectedFilesV2', JSON.stringify(checkedValues));@this.removeGroupFilesV2()"
                                    data-kt-menu-dismiss="false"
                                    style=" float: left; margin-bottom: 10px; ">
                                    <div wire:loading wire:target="removeGroupFilesV2()">
                                        <span class="spinner-border spinner-border-sm align-middle m-1"></span>
                                    </div>
                                    تایید حذف
                                </button>
                                <!--end::Input-->
                            </div>
                        </div>
                    @else
                        <p class="text-muted fw-bold d-block mt-1 direction-rtl px-3">ابتدا فایل ها را انتخاب نمایید</p>
                    @endif
                </div>
                <!--end::Menu-->
            </div>

        </div>
        <!--end::Header-->

        <!--begin::Body-->
        <div class="card-body pt-2 pb-0 mt-n3">
            <div class="alert alert-primary d-flex align-items-center p-5 mb-10">
                <i class="ki-duotone ki-shield-tick fs-2hx text-success me-4">
                    <span class="path1"></span><span class="path2"></span></i>
                <div class="d-flex flex-column">
                    <span>ممکن است پروسه اپلود فایل ها و قرار گیری آن ها در این قسمت بسته به زمان رویداد شما تا ۳ ساعت زمانبر باشد</span>
                </div>
            </div>
            <div class="alert alert-primary d-flex align-items-center p-5 mb-10">
                <i class="ki-duotone ki-shield-tick fs-2hx text-success me-4">
                    <span class="path1"></span><span class="path2"></span></i>
                <div class="d-flex flex-column">
                    <span>محتوای این اتاق با ایمیل {{implode(',',$this->admins)}} در گوگل درایو به اشتراک گذاشته شده است و میتوانید از گوگل درایو خود نیز به آن دسترسی داشته باشید</span>
                </div>
            </div>
            <div class="tab-content mt-5" id="myTabTables1">
                <!--begin::Tap pane-->
                <div class="tab-pane fade show active" id="kt_tab_pane_1_1" role="tabpanel"
                     aria-labelledby="kt_tab_pane_1_1">
                    <!--begin::Table-->
                    @if(!empty($files))
                        <div class="table-responsive">
                            <table class="table table-borderless align-middle">
                                <thead>
                                <tr class="mb-15">
                                    <th class="p-0 w-50px">عنوان</th>
                                    <th class="p-0 min-w-200px"></th>

                                    <th class="p-0 min-w-100px">
                                        @if(!$hasAutoStorage)
                                            تعداد روز تا حذف
                                        @endif
                                    </th>
                                    <th class="p-0 min-w-140px">
                                        <div class="d-flex justify-content-end pe-4 ">
                                            <div
                                                class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                                <label class="" for="select-all"> انتخاب همه:</label>
                                                <input class="ms-3  form-check-input select-all" id="select-all"
                                                       type="checkbox">
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                @forelse($files as $meetingFile)

                                    <tr id="file_{{$meetingFile['file_id']}}">
                                        <th class="px-0 py-3">
                                            <div class="symbol symbol-65px  me-3">
                                                {!! getIconFromMime($meetingFile['mime_type']) !!}
                                            </div>
                                        </th>
                                        <td class="ps-0 text-align-right">
                                            <b
                                                class="text-gray-800 fw-bolder text-hover-primary fs-6">
                                                {{$meetingFile['file_name']}}

                                            </b>
                                            <span class="text-muted fw-bold d-block mt-1 direction-rtl">
                                                {{$meetingFile['mime_type']}} - {{formatBytes($meetingFile['file_size'])}} -
                                                @php
                                                    try{
                                                        $time =  \Carbon\Carbon::parse(($meetingFile['meeting_created_at']))->timezone('Europe/Guernsey');
                                                        echo \Morilog\Jalali\Jalalian::fromCarbon($time)->format('%A, %d %B %Y H:i:s');
                                                    }catch(Exception $e){

                                                    }

                                                @endphp

                                            </span>
                                        </td>

                                        <td>
                                            @if(!$hasAutoStorage)
                                                @php
                                                    $percentage = 100 - (\App\Models\File\MeetingFile::remainAvailableDay($maxKeepFiles,$meetingFile['created_at']) / $maxKeepFiles * 100);
                                                    if($percentage < 70){
                                                        $class = 'primary';
                                                    }else{
                                                         $class = 'danger';
                                                    }
                                                @endphp
                                                <div class="d-flex flex-column w-100 me-3">
                                                    <div class="d-flex align-items-center">
                                                        <div class="progress h-6px  w-100 bg-light-primary">
                                                            <div class="progress-bar bg-{{$class}}" role="progressbar"
                                                                 style="width: {{$percentage}}%;"
                                                                 aria-valuenow="50" aria-valuemin="0"
                                                                 aria-valuemax="100"></div>
                                                        </div>
                                                        <span class="text-muted fs-7 fw-bold ps-3">
                                                         {{\App\Models\File\MeetingFile::remainAvailableDay($maxKeepFiles,$meetingFile['created_at'])}} روز
                                                    </span>
                                                    </div>
                                                </div>
                                            @endif

                                        </td>

                                        <td class="text-end pe-0">

                                            <div class="d-flex justify-content-around">

                                                <!--begin::Menu wrapper-->
                                                <div class="m-0 relative ">
                                                    <!--begin::Menu toggle-->

                                                    <button type="button"
                                                            class="btn  btn-bg-light btn-active-primary btn-sm"
                                                            data-kt-menu-trigger="click"
                                                            data-kt-menu-placement="bottom-end"
                                                            data-kt-menu-flip="top-end">

                                                        <!--begin::Svg Icon | path: icons/stockholm/Layout/Layout-4-blocks-2.svg-->
                                                        <span class="svg-icon svg-icon-1">

															<svg xmlns="http://www.w3.org/2000/svg"
                                                                 xmlns:xlink="http://www.w3.org/1999/xlink" width="24px"
                                                                 height="24px" viewBox="0 0 24 24" version="1.1">
																<g stroke="none" stroke-width="1" fill="none"
                                                                   fill-rule="evenodd">
																	<rect x="5" y="5" width="5" height="5" rx="1"
                                                                          fill="#000000"/>
																	<rect x="14" y="5" width="5" height="5" rx="1"
                                                                          fill="#000000" opacity="0.3"/>
																	<rect x="5" y="14" width="5" height="5" rx="1"
                                                                          fill="#000000" opacity="0.3"/>
																	<rect x="14" y="14" width="5" height="5" rx="1"
                                                                          fill="#000000" opacity="0.3"/>
																</g>
															</svg>
														</span>
                                                        <!--end::Svg Icon-->
                                                        تنظیمات

                                                    </button>
                                                    <!--end::Menu toggle-->

                                                    <!--begin::Menu dropdown-->
                                                    <livewire:file.file-setting-local :file-id="$meetingFile['file_id']"
                                                                                      :file-name="$meetingFile['file_name']"
                                                                                      :licence-id="$licenceId"
                                                                                      :meeting-id="$meetingId"
                                                                                      :showInPublic="$meetingFile['public_status'] =='1'"
                                                                                      :wire:key="$meetingFile['file_id']"
                                                    />

                                                    <!--end::Menu dropdown-->
                                                </div>
                                                <!--end::Menu wrapper-->
                                                <a class="btn btn-bg-light btn-active-primary btn-sm  z-index-3  kt_clipboard_4 whitespace-nowrap"
                                                   aria-label="https://drive.google.com/file/d/{{$meetingFile['file_id']}}/view?usp=sharing"
                                                   data-bs-toggle="tooltip"
                                                   data-bs-placement="bottom"
                                                   data-bs-original-title="کپی لینک مستقیم فایل"
                                                   data-clipboard-target="#kt_clipboard_{{$meetingFile['file_id']}}">
                                                    <!--begin::Svg Icon | path: icons/duotune/general/gen054.svg-->
                                                    <span class="svg-icon svg-icon-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24"
                                     height="24" viewBox="0 0 24 24" fill="none">
                                    <path opacity="0.5"
                                          d="M18 2H9C7.34315 2 6 3.34315 6 5H8C8 4.44772 8.44772 4 9 4H18C18.5523 4 19 4.44772 19 5V16C19 16.5523 18.5523 17 18 17V19C19.6569 19 21 17.6569 21 16V5C21 3.34315 19.6569 2 18 2Z"
                                          fill="black"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M14.7857 7.125H6.21429C5.62255 7.125 5.14286 7.6007 5.14286 8.1875V18.8125C5.14286 19.3993 5.62255 19.875 6.21429 19.875H14.7857C15.3774 19.875 15.8571 19.3993 15.8571 18.8125V8.1875C15.8571 7.6007 15.3774 7.125 14.7857 7.125ZM6.21429 5C4.43908 5 3 6.42709 3 8.1875V18.8125C3 20.5729 4.43909 22 6.21429 22H14.7857C16.5609 22 18 20.5729 18 18.8125V8.1875C18 6.42709 16.5609 5 14.7857 5H6.21429Z"
                                          fill="black"/>
                                </svg>
                            </span>
                                                    کپی لینک
                                                </a>

                                                <button
                                                    class="btn btn-bg-light btn-active-primary btn-sm whitespace-nowrap"
                                                    wire:click="$dispatch('openShareModal', {fileId: '{{$meetingFile['file_id']}}', fileName: '{{$meetingFile['file_name']}}'})"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="bottom"
                                                    data-bs-original-title="اشتراک‌گذاری فایل">
                                                    <!--begin::Svg Icon | path: icons/duotune/communication/com013.svg-->
                                                    <span class="svg-icon svg-icon-2"><svg width="24" height="24"
                                                                                           viewBox="0 0 24 24"
                                                                                           fill="none"
                                                                                           xmlns="http://www.w3.org/2000/svg">
<path
    d="M19.4 13.9411L10.7 5.24112C10.4 4.94112 10 4.84104 9.60001 5.04104C9.20001 5.24104 9 5.54107 9 5.94107V18.2411C9 18.6411 9.20001 18.941 9.60001 19.141C9.70001 19.241 9.9 19.2411 10 19.2411C10.2 19.2411 10.4 19.141 10.6 19.041C11.4 18.441 12.1 17.941 12.9 17.541L14.4 21.041C14.6 21.641 15.2 21.9411 15.8 21.9411C16 21.9411 16.2 21.9411 16.4 21.8411C17.2 21.5411 17.5 20.6411 17.2 19.8411L15.7 16.2411C16.7 15.9411 17.7 15.741 18.8 15.541C19.2 15.541 19.5 15.2411 19.6 14.8411C19.8 14.6411 19.7 14.2411 19.4 13.9411Z"
    fill="currentColor"/>
<path opacity="0.3"
      d="M15 6.941C14.8 6.941 14.7 6.94102 14.6 6.84102C14.1 6.64102 13.9 6.04097 14.2 5.54097L15.2 3.54097C15.4 3.04097 16 2.84095 16.5 3.14095C17 3.34095 17.2 3.941 16.9 4.441L15.9 6.441C15.7 6.741 15.4 6.941 15 6.941ZM18.4 9.84102L20.4 8.84102C20.9 8.64102 21.1 8.04097 20.8 7.54097C20.6 7.04097 20 6.84095 19.5 7.14095L17.5 8.14095C17 8.34095 16.8 8.941 17.1 9.441C17.3 9.841 17.6 10.041 18 10.041C18.2 9.94097 18.3 9.94102 18.4 9.84102ZM7.10001 10.941C7.10001 10.341 6.70001 9.941 6.10001 9.941H4C3.4 9.941 3 10.341 3 10.941C3 11.541 3.4 11.941 4 11.941H6.10001C6.70001 11.941 7.10001 11.541 7.10001 10.941ZM4.89999 17.1409L6.89999 16.1409C7.39999 15.9409 7.59999 15.341 7.29999 14.841C7.09999 14.341 6.5 14.141 6 14.441L4 15.441C3.5 15.641 3.30001 16.241 3.60001 16.741C3.80001 17.141 4.1 17.341 4.5 17.341C4.6 17.241 4.79999 17.2409 4.89999 17.1409Z"
      fill="currentColor"/>
</svg>
</span>
                                                    اشتراک
                                                </button>


                                                <div
                                                    class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                                    <input class="form-check-input remove-file" type="checkbox"
                                                           value="{{$meetingFile['file_id']}}"
                                                           wire:key="ipt-{{$meetingFile['file_id']}}">
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @empty

                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    @endif
                    <!--end::Table-->
                </div>
                <!--end::Tap pane-->

                <!--begin::Tap pane-->

                <!--end::Tap pane-->

                <!--begin::Tap pane-->

                <!--end::Tap pane-->
            </div>
        </div>
    </div>
    <!--end::Table Widget 1-->

    <!-- File Share Modal -->
    <livewire:file.file-share-modal :meetingId="$meetingId"/>
</div>

@push('scripts')
    <script>
        var checkedValues = [];
        Livewire.on('hide_item', (id) => {
            $(id.id).hide(1000);
        });
        $('.select-all').on('click', function () {
            let allChecked = $('.remove-file:checked').length === $('.remove-file').length;
            $('.remove-file').prop("checked", !allChecked);
            $('.remove-file').trigger('change')
        });

        document.addEventListener('livewire:init', function () {

            $('.remove-file').on('change', function () {
                checkedValues = [];
                $('.remove-file:checked').each(function () {
                    checkedValues.push($(this).val());
                });
                @this.
                set('selectedFilesV2', JSON.stringify(checkedValues));
            });
        })

        clipboard = new ClipboardJS('.kt_clipboard_4', {
            target: function (trigger) {
                return trigger.nextElementSibling;
            },
            text: function (trigger) {
                return trigger.getAttribute('aria-label');
            },
        });
        var button = null;
        $('.kt_clipboard_').on('click', function () {
            button = this;
        })

        // var clipboard = new ClipboardJS('.kt_clipboard_4');

        // Success action handler
        clipboard.on('success', function (e) {
            console.log(clipboard.target)
            let target = clipboard.target;
            let button = e.trigger;
            var checkIcon = button.querySelector('.bi.bi-check');
            var svgIcon = button.querySelector('.svg-icon');

            // Exit check icon when already showing
            if (checkIcon) {
                return;
            }

            // Create check icon
            checkIcon = document.createElement('i');
            checkIcon.classList.add('bi');
            checkIcon.classList.add('bi-check');
            checkIcon.classList.add('fs-2x');

            // Append check icon
            button.appendChild(checkIcon);

            // Highlight target
            const classes = ['text-success', 'fw-boldest'];
            // target.classList.add(...classes);

            // Highlight button
            button.classList.add('btn-success');

            // Hide copy icon
            svgIcon.classList.add('d-none');

            // Revert button label after 3 seconds
            setTimeout(function () {
                // Remove check icon
                svgIcon.classList.remove('d-none');

                // Revert icon
                button.removeChild(checkIcon);

                // Remove target highlight
                // target.classList.remove(...classes);

                // Remove button highlight
                button.classList.remove('btn-success');
            }, 3000)
        });
    </script>

@endpush
