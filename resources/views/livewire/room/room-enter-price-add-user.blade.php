<div class="modal-body py-lg-10 px-lg-10">

    @if(!$showLinkMode)
        <div class="row mb-8">
            <label class="col-lg-3 col-form-label">راهنما</label>
            <div class="col-lg-9">
                <div class="form-text-bl">
                    شما میتوانید با ساخت لینک یکبار مصرف، به سایر کاربران، به عنوان یکی از
                    مدیران این اتاق، دسترسی دلخواه بدهید. توجه کنید
                    جهت استفاده از لینک یکبار مصرف، ابتدا باید در سایت آی روم حساب کاربری ایجاد
                    شود و سپس از لینک استفاده گردد.
                </div>
            </div>
        </div>

        <div class="row mb-8">
            <label class="col-lg-3 col-form-label">پیغام دعوت:</label>
            <div class="col-lg-9">
                <div class="spinner spinner-sm spinner-primary spinner-right">
                                            <textarea class="form-control form-control-lg form-control-solid"
                                                      rows="5"
                                                      type="text" value="max_stone" wire:model="message"></textarea>
                </div>
            </div>
        </div>

        <div class="row align-items-center mb-3">
            <label class="col-lg-3 col-form-label">دسترسی به:</label>
            <div class="col-lg-9">
                <div class="d-flex align-items-center">
                    <div class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox"
                               id="inlineCheckbox1" wire:model="usersAccess">
                        <label class="form-check-label fw-semibold" for="inlineCheckbox1">کاربران</label>
                    </div>

                    <div class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" id="inlineCheckbox2"
                               wire:model="settingsAccess">
                        <label class="form-check-label fw-semibold" for="inlineCheckbox2">تنظیمات</label>
                    </div>
                    <div class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" id="inlineCheckbox2"
                               wire:model="filesAccess">
                        <label class="form-check-label fw-semibold" for="inlineCheckbox2">فایل
                            ها</label>
                    </div>
                    <div class="form-check form-check-custom form-check-solid">
                        <input class="form-check-input" type="checkbox" id="inlineCheckbox3"
                               wire:model="reportsAccess">
                        <label class="form-check-label fw-semibold" for="inlineCheckbox3">گزارشات</label>
                    </div>
                </div>
            </div>
        </div>



        <div class="d-flex flex-center">

            <button type="submit" data-kt-element="apps-submit" class="btn btn-primary"
                    wire:loading.class="disabled" wire:target="getInviteLink"
                    wire:click="getInviteLink">
            <span class="indicator-label" wire:loading.remove wire:target="getInviteLink">
                دریافت لینک دعوت
            </span>
                <span class="indicator-progress" wire:loading wire:target="getInviteLink">
                کمی صبر کنید
                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
            </span>
            </button>
            <button type="reset" data-bs-dismiss="modal" class="btn btn-light ms-3">
                انصراف
            </button>
        </div>

    @else
        <h3 class="text-gray-900 fw-bold mb-7">
            لینک دسترسی:
        </h3>
        <div class="text-gray-500 fw-semibold fs-6 mb-10">
            جهت دعوت سایرین میتوانید از لینک زیر استفاده نمایید. برای استفاده از لینک زیر، کاربران باید در سایت اکانت
            فعال داشته باشند.
        </div>

        <div class="notice d-flex bg-light-warning rounded border-warning border border-dashed mb-10 p-6">
            <!--begin::Icon-->
            <!--begin::Svg Icon | path: /var/www/preview.keenthemes.com/keenthemes/start/docs/core/html/src/media/icons/duotune/general/gen051.svg-->
            <span class="svg-icon svg-icon-warning svg-icon-2hx"><svg width="24" height="24" viewBox="0 0 24 24"
                                                                      fill="none" xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3"
      d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z"
      fill="currentColor"/>
<path
    d="M14.854 11.321C14.7568 11.2282 14.6388 11.1818 14.4998 11.1818H14.3333V10.2272C14.3333 9.61741 14.1041 9.09378 13.6458 8.65628C13.1875 8.21876 12.639 8 12 8C11.361 8 10.8124 8.21876 10.3541 8.65626C9.89574 9.09378 9.66663 9.61739 9.66663 10.2272V11.1818H9.49999C9.36115 11.1818 9.24306 11.2282 9.14583 11.321C9.0486 11.4138 9 11.5265 9 11.6591V14.5227C9 14.6553 9.04862 14.768 9.14583 14.8609C9.24306 14.9536 9.36115 15 9.49999 15H14.5C14.6389 15 14.7569 14.9536 14.8542 14.8609C14.9513 14.768 15 14.6553 15 14.5227V11.6591C15.0001 11.5265 14.9513 11.4138 14.854 11.321ZM13.3333 11.1818H10.6666V10.2272C10.6666 9.87594 10.7969 9.57597 11.0573 9.32743C11.3177 9.07886 11.6319 8.9546 12 8.9546C12.3681 8.9546 12.6823 9.07884 12.9427 9.32743C13.2031 9.57595 13.3333 9.87594 13.3333 10.2272V11.1818Z"
    fill="currentColor"/>
</svg>
</span>
            <!--end::Svg Icon-->

            <i class="ki-duotone ki-information fs-2tx text-warning me-4"><span class="path1"></span><span
                    class="path2"></span><span class="path3"></span></i>
            <!--end::Icon-->

            <!--begin::Wrapper-->
            <div class="d-flex flex-stack flex-grow-1 ">
                <!--begin::Content-->
                <div class=" fw-semibold">

                    <div class="fs-6 text-gray-700 ">
                        <div class="fw-bold text-gray-900 pb-2">توجه:</div>

                        توجه کنید که لینک زیر، یکبار مصرف است و اولین فردی که از آن استفاده کند، به اتاق فعلی و محتوای
                        آن دسترسی پیدا خواهد کرد. لذا در هنگام دراختیار گذاشتن آن امنیت را رعایت نمایید.

                        <div class="fw-bold text-gray-900 pb-2">کلید دعوت: {{$createdToken}}</div>
                    </div>
                </div>
                <!--end::Content-->

            </div>
            <!--end::Wrapper-->
        </div>

        <div class="card card-bordered">
            <div class="card-body">
                <!--begin::Row-->
                <div class="d-flex align-items-center justify-center ms-3">
                    <div class="form-input input-group direction-ltr">
                        <span class="input-group-text  text-align-center cursor-pointer" id="copyText" onclick="copyToClipboard('copyText')"><i
                                class="la la-copy fs-4"></i></span>
                        <input type="text" class="form-control form-control-solid" id="copyTextTarget" readonly
                               value="{{$createdTokenLink}}">
                    </div>
                </div>
                <!--end::Row-->
            </div>
        </div>

    @endif


</div>
@script
<script>
        function confirmDelete(inviteId) {
            swal.fire({
                text: "آیا از حذف این عضو اطمینان دارید؟",
                icon: "warning",
                buttonsStyling: false,
                showCancelButton: true,
                confirmButtonText: "بله، حذف شود",
                cancelButtonText: "انصراف",
                customClass: {
                    confirmButton: "btn fw-bold btn-danger",
                    cancelButton: "btn fw-bold btn-light"
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    $wire.removeEnterpriseUser(inviteId);
                }
            });
        }
</script>
@endscript
