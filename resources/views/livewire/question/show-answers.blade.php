<div>
    {{-- Be like water. --}}


    @if($accepted_answer)
    <livewire:question.show-answer-component :answer="$accepted_answer" :creator="$question_creator"
                                             :wire:key="'got_accepted_answer'"/>
    @endif

    <div wire:loading.class="opacity-05">
        @foreach($answers as $answer)
            <livewire:question.show-answer-component :answer="$answer" :creator="$question_creator"
                                                     :wire:key="$answer->id.'answer'"/>
        @endforeach
    </div>

    {{$paginate->links()}}

    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function () {
             Livewire.on('removed_answer', param => {
                $('#answer_' + param['aim']).slideUp('400');
            });
             Livewire.on('accepted_answer_changed', param => {
                $('.best-answer').first().removeClass('best-answer bg-light-success border border-dark-success border-active active')
            });
        });
    </script>


</div>

