<div>
{{-- Care about people's approval and you will be their prisoner. --}}
<!--begin::Modal - Users Search-->
    <div class="modal fade" id="kt_modal_question_search" aria-hidden="true" wire:ignore.self>
        <!--begin::Modal dialog-->
        <div class="modal-dialog modal-dialog-centered mw-650px">
            <!--begin::Modal content-->
            <div class="modal-content">
                <!--begin::Modal header-->
                <div class="modal-header pb-0 border-0 justify-content-end">
                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-icon-primary" data-bs-dismiss="modal">
                        <!--begin::Svg Icon | path: icons/duotone/Interface/Close-Square.svg-->
                        <span class="svg-icon svg-icon-2x">
														<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                             viewBox="0 0 24 24" fill="none">
															<path opacity="0.25" fill-rule="evenodd" clip-rule="evenodd"
                                                                  d="M2.36899 6.54184C2.65912 4.34504 4.34504 2.65912 6.54184 2.36899C8.05208 2.16953 9.94127 2 12 2C14.0587 2 15.9479 2.16953 17.4582 2.36899C19.655 2.65912 21.3409 4.34504 21.631 6.54184C21.8305 8.05208 22 9.94127 22 12C22 14.0587 21.8305 15.9479 21.631 17.4582C21.3409 19.655 19.655 21.3409 17.4582 21.631C15.9479 21.8305 14.0587 22 12 22C9.94127 22 8.05208 21.8305 6.54184 21.631C4.34504 21.3409 2.65912 19.655 2.36899 17.4582C2.16953 15.9479 2 14.0587 2 12C2 9.94127 2.16953 8.05208 2.36899 6.54184Z"
                                                                  fill="#12131A"/>
															<path fill-rule="evenodd" clip-rule="evenodd"
                                                                  d="M8.29289 8.29289C8.68342 7.90237 9.31658 7.90237 9.70711 8.29289L12 10.5858L14.2929 8.29289C14.6834 7.90237 15.3166 7.90237 15.7071 8.29289C16.0976 8.68342 16.0976 9.31658 15.7071 9.70711L13.4142 12L15.7071 14.2929C16.0976 14.6834 16.0976 15.3166 15.7071 15.7071C15.3166 16.0976 14.6834 16.0976 14.2929 15.7071L12 13.4142L9.70711 15.7071C9.31658 16.0976 8.68342 16.0976 8.29289 15.7071C7.90237 15.3166 7.90237 14.6834 8.29289 14.2929L10.5858 12L8.29289 9.70711C7.90237 9.31658 7.90237 8.68342 8.29289 8.29289Z"
                                                                  fill="#12131A"/>
														</svg>
													</span>
                        <!--end::Svg Icon-->
                    </div>
                    <!--end::Close-->
                </div>
                <!--begin::Modal header-->
                <!--begin::Modal body-->
                <div class="modal-body scroll-y mx-5 mx-xl-18 pt-0 pb-15">
                    <!--begin::Content-->
                    <div class="text-center mb-12">
                        <div class="fs-2 fw-bolder mb-1">جستجو در سوالات</div>
                        <div class="text-gray-400 fw-bold fs-3">کلمه دلخواه خود را وارد نمایید.</div>
                    </div>
                    <!--end::Content-->
                    <!--begin::Search-->
                    <div id="kt_modal_users_search_handler" data-kt-search-keypress="true" data-kt-search-min-length="2"
                         data-kt-search-enter="enter" data-kt-search-layout="inline">
                        <!--begin::Form-->
                        <form data-kt-search-element="form" class="w-100 position-relative mb-5" autocomplete="off">
                            <!--begin::Hidden input(Added to disable form autocomplete)-->
                            <input type="hidden"/>
                            <!--end::Hidden input-->
                            <!--begin::Icon-->
                            <!--begin::Svg Icon | path: icons/duotone/General/Search.svg-->
                            <span
                                class="svg-icon svg-icon-2 svg-icon-lg-1 svg-icon-gray-500 position-absolute top-50 ms-5 translate-middle-y">
															<svg xmlns="http://www.w3.org/2000/svg"
                                                                 xmlns:xlink="http://www.w3.org/1999/xlink" width="24px"
                                                                 height="24px" viewBox="0 0 24 24" version="1.1">
																<g stroke="none" stroke-width="1" fill="none"
                                                                   fill-rule="evenodd">
																	<rect x="0" y="0" width="24" height="24"/>
																	<path
                                                                        d="M14.2928932,16.7071068 C13.9023689,16.3165825 13.9023689,15.6834175 14.2928932,15.2928932 C14.6834175,14.9023689 15.3165825,14.9023689 15.7071068,15.2928932 L19.7071068,19.2928932 C20.0976311,19.6834175 20.0976311,20.3165825 19.7071068,20.7071068 C19.3165825,21.0976311 18.6834175,21.0976311 18.2928932,20.7071068 L14.2928932,16.7071068 Z"
                                                                        fill="#000000" fill-rule="nonzero"
                                                                        opacity="0.3"/>
																	<path
                                                                        d="M11,16 C13.7614237,16 16,13.7614237 16,11 C16,8.23857625 13.7614237,6 11,6 C8.23857625,6 6,8.23857625 6,11 C6,13.7614237 8.23857625,16 11,16 Z M11,18 C7.13400675,18 4,14.8659932 4,11 C4,7.13400675 7.13400675,4 11,4 C14.8659932,4 18,7.13400675 18,11 C18,14.8659932 14.8659932,18 11,18 Z"
                                                                        fill="#000000" fill-rule="nonzero"/>
																</g>
															</svg>
														</span>
                            <!--end::Svg Icon-->
                            <!--end::Icon-->
                            <!--begin::Input-->
                            <input type="text" class="form-control form-control-lg form-control-solid px-15"
                                   name="search" value="" placeholder="یک کلمه وارد نمایید..." wire:model.live="search_term"
                                   wire:model.live="search_term" />
                            <span wire:loading wire:target="search_term" style=" position: absolute; top: 24%; left: 10px; ">
                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
            </span>
                            <!--end::Input-->
                            <!--begin::Spinner-->
                            <span class="position-absolute top-50 end-0 translate-middle-y lh-0 d-none me-5"
                                  data-kt-search-element="spinner">
															<span
                                                                class="spinner-border h-15px w-15px align-middle text-gray-400"></span>
														</span>
                            <!--end::Spinner-->
                            <!--begin::Reset-->
                            <span
                                class="btn btn-flush btn-active-color-primary position-absolute top-50 end-0 translate-middle-y lh-0 me-5 d-none"
                                data-kt-search-element="clear">
															<!--begin::Svg Icon | path: icons/duotone/Navigation/Close.svg-->
															<span class="svg-icon svg-icon-2 svg-icon-lg-1 me-0">
																<svg xmlns="http://www.w3.org/2000/svg"
                                                                     xmlns:xlink="http://www.w3.org/1999/xlink"
                                                                     width="24px" height="24px" viewBox="0 0 24 24"
                                                                     version="1.1">
																	<g transform="translate(12.000000, 12.000000) rotate(-45.000000) translate(-12.000000, -12.000000) translate(4.000000, 4.000000)"
                                                                       fill="#000000">
																		<rect fill="#000000" x="0" y="7" width="16"
                                                                              height="2" rx="1"/>
																		<rect fill="#000000" opacity="0.5"
                                                                              transform="translate(8.000000, 8.000000) rotate(-270.000000) translate(-8.000000, -8.000000)"
                                                                              x="0" y="7" width="16" height="2" rx="1"/>
																	</g>
																</svg>
															</span>
                                <!--end::Svg Icon-->
														</span>
                            <!--end::Reset-->
                        </form>
                        <!--end::Form-->
                        <!--begin::Wrapper-->
                        <div class="py-5">
                            <!--begin::Suggestions-->
{{--                            <div data-kt-search-element="suggestions">--}}
{{--                                <!--begin::Heading-->--}}
{{--                                <h3 class="fw-bold mb-5">Recently searched:</h3>--}}
{{--                                <!--end::Heading-->--}}
{{--                                <!--begin::Users-->--}}
{{--                                <div class="mh-375px scroll-y me-n7 pe-7">--}}
{{--                                    <!--begin::User-->--}}
{{--                                    <a href="#"--}}
{{--                                       class="d-flex align-items-center p-3 rounded bg-state-light bg-state-opacity-50 mb-1">--}}
{{--                                        <!--begin::Avatar-->--}}
{{--                                        <div class="symbol symbol-35px symbol-circle me-5">--}}
{{--                                            <img alt="Pic" src="/start/assets/media/avatars/150-1.jpg"/>--}}
{{--                                        </div>--}}
{{--                                        <!--end::Avatar-->--}}
{{--                                        <!--begin::Info-->--}}
{{--                                        <div class="fw-bold">--}}
{{--                                            <span class="fs-6 text-gray-800 me-2">Emma Smith</span>--}}
{{--                                            <span class="badge badge-light">Art Director</span>--}}
{{--                                        </div>--}}
{{--                                        <!--end::Info-->--}}
{{--                                    </a>--}}
{{--                                    <!--end::User-->--}}
{{--                                    <!--begin::User-->--}}
{{--                                    <a href="#"--}}
{{--                                       class="d-flex align-items-center p-3 rounded bg-state-light bg-state-opacity-50 mb-1">--}}
{{--                                        <!--begin::Avatar-->--}}
{{--                                        <div class="symbol symbol-35px symbol-circle me-5">--}}
{{--                                            <span class="symbol-label bg-light-danger text-danger fw-bold">M</span>--}}
{{--                                        </div>--}}
{{--                                        <!--end::Avatar-->--}}
{{--                                        <!--begin::Info-->--}}
{{--                                        <div class="fw-bold">--}}
{{--                                            <span class="fs-6 text-gray-800 me-2">Melody Macy</span>--}}
{{--                                            <span class="badge badge-light">Marketing Analytic</span>--}}
{{--                                        </div>--}}
{{--                                        <!--end::Info-->--}}
{{--                                    </a>--}}
{{--                                    <!--end::User-->--}}
{{--                                    <!--begin::User-->--}}
{{--                                    <a href="#"--}}
{{--                                       class="d-flex align-items-center p-3 rounded bg-state-light bg-state-opacity-50 mb-1">--}}
{{--                                        <!--begin::Avatar-->--}}
{{--                                        <div class="symbol symbol-35px symbol-circle me-5">--}}
{{--                                            <img alt="Pic" src="/start/assets/media/avatars/150-2.jpg"/>--}}
{{--                                        </div>--}}
{{--                                        <!--end::Avatar-->--}}
{{--                                        <!--begin::Info-->--}}
{{--                                        <div class="fw-bold">--}}
{{--                                            <span class="fs-6 text-gray-800 me-2">Max Smith</span>--}}
{{--                                            <span class="badge badge-light">Software Enginer</span>--}}
{{--                                        </div>--}}
{{--                                        <!--end::Info-->--}}
{{--                                    </a>--}}
{{--                                    <!--end::User-->--}}
{{--                                    <!--begin::User-->--}}
{{--                                    <a href="#"--}}
{{--                                       class="d-flex align-items-center p-3 rounded bg-state-light bg-state-opacity-50 mb-1">--}}
{{--                                        <!--begin::Avatar-->--}}
{{--                                        <div class="symbol symbol-35px symbol-circle me-5">--}}
{{--                                            <img alt="Pic" src="/start/assets/media/avatars/150-4.jpg"/>--}}
{{--                                        </div>--}}
{{--                                        <!--end::Avatar-->--}}
{{--                                        <!--begin::Info-->--}}
{{--                                        <div class="fw-bold">--}}
{{--                                            <span class="fs-6 text-gray-800 me-2">Sean Bean</span>--}}
{{--                                            <span class="badge badge-light">Web Developer</span>--}}
{{--                                        </div>--}}
{{--                                        <!--end::Info-->--}}
{{--                                    </a>--}}
{{--                                    <!--end::User-->--}}
{{--                                    <!--begin::User-->--}}
{{--                                    <a href="#"--}}
{{--                                       class="d-flex align-items-center p-3 rounded bg-state-light bg-state-opacity-50 mb-1">--}}
{{--                                        <!--begin::Avatar-->--}}
{{--                                        <div class="symbol symbol-35px symbol-circle me-5">--}}
{{--                                            <img alt="Pic" src="/start/assets/media/avatars/150-15.jpg"/>--}}
{{--                                        </div>--}}
{{--                                        <!--end::Avatar-->--}}
{{--                                        <!--begin::Info-->--}}
{{--                                        <div class="fw-bold">--}}
{{--                                            <span class="fs-6 text-gray-800 me-2">Brian Cox</span>--}}
{{--                                            <span class="badge badge-light">UI/UX Designer</span>--}}
{{--                                        </div>--}}
{{--                                        <!--end::Info-->--}}
{{--                                    </a>--}}
{{--                                    <!--end::User-->--}}
{{--                                </div>--}}
{{--                                <!--end::Users-->--}}
{{--                            </div>--}}
                            <!--end::Suggestions-->
                        @if(!empty($search_result))
                            <!--begin::Results(add d-none to below element to hide the users list by default)-->
                                <div data-kt-search-element="results" class="">
                                    @foreach($search_result as $result)


                                        <a href="{{route('questions.show_with_slug',([$result->id,$result->slug]))}}" class="d-flex align-items-center p-3 rounded bg-state-light bg-state-opacity-50 mb-1">
                                            <!--begin::Avatar-->
                                            <div class="symbol symbol-35px symbol-circle me-5">
                                                <img alt="Pic" src="{{$result->user->profile_photo ?? ""}}">
                                            </div>
                                            <!--end::Avatar-->
                                            <!--begin::Info-->
                                            <div class="fw-bold">
                                                <span class="badge badge-light">{{$result->user->full_name ?? ""}}</span>
                                                <span class="fs-6 text-gray-800 me-2">{{$result->title}}</span>
                                            </div>
                                            <!--end::Info-->
                                        </a>


                                    @endforeach
                                </div>
                                <!--end::Results-->
                        @endif
                        <!--begin::Empty-->
                            <div data-kt-search-element="empty" class="text-center d-none">
                                <!--begin::Message-->
                                <div class="fw-bold py-10">
                                    <div class="text-gray-600 fs-3 mb-2">No users found</div>
                                    <div class="text-gray-400 fs-6">Try to search by username, full name or email...
                                    </div>
                                </div>
                                <!--end::Message-->
                                <!--begin::Illustration-->
                                <div class="text-center px-4">
                                    <img src="/start/assets/media/illustrations/alert-2.png" alt=""
                                         class="mw-100 mh-200px"/>
                                </div>
                                <!--end::Illustration-->
                            </div>
                            <!--end::Empty-->
                        </div>
                        <!--end::Wrapper-->
                    </div>
                    <!--end::Search-->
                </div>
                <!--end::Modal body-->
            </div>
            <!--end::Modal content-->
        </div>
        <!--end::Modal dialog-->
    </div>
    <!--end::Modal - Users Search-->


</div>
