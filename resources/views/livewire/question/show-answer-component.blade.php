<div>
    <div class="card mb-5
    @if($answer->accepted)
        best-answer bg-light-success border border-dark-success border-active active
    @endif
        "
         id="answer_{{$answer->id}}"
    >
        <div class="card-body position-relative">
            <!--begin::Message-->
            <div class="mb-3">
                <div class="d-flex py-6 flex-column flex-md-row flex-lg-column flex-xxl-row justify-content-between">
                    <div class="d-flex align-items-center">
                        <!--begin::Symbol-->
                        <div class="symbol symbol-40px me-4">
												<span class="symbol-label bg-light">
													<img src="{{$answer->user->profile_photo}}"
                                                         class="rounded h-100 align-self-end" alt="">
												</span>
                        </div>
                        <!--end::Symbol-->
                        <div class="d-flex flex-column flex-grow-1 flex-wrap me-2">
                            <div class="d-flex align-items-center flex-wrap">
                                <a href="#"
                                   class="fs-6 fw-bolder text-gray-800 text-hover-primary me-2">{{$answer->user->full_name}}</a>
                                <div class="fw-bold fs-7 text-muted">
                                    <span
                                        class="bullet bullet-dot bg-primary w-6px h-6px me-2"></span>{{ $answer->ago_time() }}
                                </div>
                                @if(auth()->check() && $answer->created_by == auth()->user()->id)
                                    <livewire:question.edit-answer :answer="$answer->id" :type="'answer'"
                                                                   :wire:key="'user-answer-edit-one-'.$answer->id"/>
                                @endif


                                @if (Auth::check())
                                    <button class="btn btn-icon btn-active-light-primary btn-sm ms-2"
                                            wire:click="open_answer_modal({{$answer->id}})">
                                        <i class="fas fa-reply fs-6"></i>
                                    </button>
                                @endif

                            </div>
                        </div>
                    </div>
                    <div
                        class="d-flex my-2 my-xxl-0 align-items-md-center align-items-lg-start align-items-xxl-center flex-column flex-md-row flex-lg-column flex-xxl-row">
                        <div class="fw-bold text-muted mx-2"> {{jdate($answer->updated_at)->format('Y/m/d H:i')}}</div>
                        @if($answer->accepted)
                            <div class="my-2">
                                <span class="badge badge-gradient-success ms-5 text-white px-6 py-4 fw-bolder fs-5">برترین پاسخ</span>
                            </div>
                        @elseif( auth()->check() && isset($creator) &&  auth()->user()->id == $creator )
                            <button wire:click="accept_answer()">برترین پاسخ</button>
                        @endif
                        {{-- votes --}}
                        <livewire:question.vote-component
                            :data="['vote_id' => $answer->id, 'vote_category' => 'ANSWER_VOTE' ]"/>
                        <br/>

                        <button wire:click="send_report_component({{$answer->id}},'Question\\QuestionAnswer')">گزارش
                        </button>


                    </div>
                </div>
                <div class="py-3 lh-xl text-justify">


                    {!! $answer->content !!}
                </div>
            </div>
            <!--end::Message-->
        </div>
    </div>


    <livewire:question.answer-comment-component :data="$answer" :wire:key="'answer'.$answer->id"/>

    <!--begin::Reply-->
    <div class="reply position-relative mb-5 p">
    @foreach($answer->children as $comment)

        <!--begin::Body-->
            <div class="card mb-5" id="answer_{{$comment->id}}">
                <div class="card-body position-relative py-1">
                    <!--begin::Message-->
                    <div class="mb-3">
                        <div
                            class="d-flex py-1 flex-column flex-md-row flex-lg-column flex-xxl-row justify-content-between">
                            <div class="d-flex align-items-center">
                                <!--begin::Symbol-->
                                <div class="symbol symbol-40px me-4">
                                                            <span class="symbol-label bg-light">
                                                                <img src="{{$comment->user->profile_photo}}"
                                                                     class="rounded h-100 align-self-end" alt="">
                                                            </span>
                                </div>
                                <!--end::Symbol-->
                                <div class="d-flex flex-column flex-grow-1 flex-wrap me-2">
                                    <div class="d-flex align-items-center flex-wrap">
                                        <a href="#"
                                           class="fs-6 fw-bolder text-gray-800 text-hover-primary me-2">{{$comment->user->full_name}}</a>
                                        <div class="fw-bold fs-7 text-muted">
                                            <span
                                                class="bullet bullet-dot bg-primary w-6px h-6px me-2"></span>{{$comment->ago_time()}}
                                        </div>
                                    </div>
                                    @if(auth()->check() && $comment->created_by == auth()->user()->id)
                                        <livewire:question.edit-answer :answer="$comment->id" :type="'comment'"
                                                                       :wire:key="'user-comment-answer-edit-one-'.$comment->id"/>
                                    @endif

                                </div>
                            </div>
                            <div
                                class="d-flex my-2 my-xxl-0 align-items-md-center align-items-lg-start align-items-xxl-center flex-column flex-md-row flex-lg-column flex-xxl-row">
                                <div
                                    class="fw-bold text-muted mx-2"> {{jdate($comment->updated_at)->format('Y/m/d H:i')}}</div>
                                {{-- votes --}}
                                <livewire:question.vote-component
                                    :data="['vote_id' => $comment->id, 'vote_category' => 'COMMENT_VOTE' ]"
                                    :wire:key="$comment->id"/>

                                <br/>

                                <button wire:click="send_report_component({{$comment->id}},'Question\\QuestionAnswer')">
                                    گزارش
                                </button>
                            </div>
                        </div>
                        <div class="py-3 lh-xl text-justify">
                            {!! $comment->content !!}
                        </div>
                    </div>
                    <!--end::Message-->
                </div>
            </div>
            <!--end::Body-->
        @endforeach
    </div>
    <!--end::Reply-->




</div>
