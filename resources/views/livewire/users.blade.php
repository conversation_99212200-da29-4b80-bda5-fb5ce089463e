<div>
    {{-- show update modal --}}
    @include('livewire.admin.users.update')
    @if (session()->has('toast_message'))
        <div class="toast position-fixed bottom-0 start-0 mr-3 bg-primary hide"
             style="z-index: 500;left:15px!important;" role="alert" aria-live="assertive" aria-atomic="true">
            {{--            <div class="toast-header">--}}

            {{--                <strong class="me-auto">اطلاع</strong>--}}
            {{--                <small>تایم</small>--}}
            {{--                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>--}}
            {{--            </div>--}}
            <div class="toast-body" style="color:#fff">
                {{session('toast_message')}}
            </div>
        </div>


    @endif

    <button wire:ignore type="button" class="d-none" id="fire_livewire" wire:click="edit(3)">ویرایش کاربر</button>
    <script type="text/javascript">
        if (window.livewire !== undefined) {

        }
    </script>

</div>

