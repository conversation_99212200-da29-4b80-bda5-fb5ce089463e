<div>
    @if(!$verify_mode)
        <form id="kt_login_signup_form">
            <div class="fv-row mb-5">
                <label class="label-required form-label fs-6 fw-bolder text-dark pt-5">نام</label>
                <input class="danger-border direction-rtl form-control form-control-lg form-control-solid" type="text"
                       placeholder="" wire:model="first_name" wire:ignore
                       name="first_name" autocomplete="off"/>
                @if ($errors->has('first_name'))
                    <span class="invalid-feedback d-block" role="alert">
                        <strong>{{ $errors->first('first_name') }}</strong>
                    </span>
                @endif
            </div>


            <div class="fv-row mb-5">
                <label class="label-required form-label fs-6 fw-bolder text-dark pt-5">نام خانوادگی</label>
                <input class="direction-rtl form-control form-control-lg form-control-solid" type="text"
                       placeholder="" wire:model="last_name" wire:ignore
                       name="last_name" autocomplete="off" value="{{old('last_name')}}"/>
                @if ($errors->has('last_name'))
                    <span class="invalid-feedback d-block" role="alert">
                      <strong>{{ $errors->first('last_name') }}</strong>
                </span>
                @endif
            </div>


            <!--begin::Form group-->
            <div class="fv-row mb-5">
                <label class="label-required form-label fs-6 fw-bolder text-dark pt-5">شماره تماس</label>
                {{-- FIXME: default value dont work correctly --}}
                <input class="direction-ltr convertor form-control form-control-lg form-control-solid" type="text"
                       placeholder="" wire:model="phone_number" wire:ignore
                       name="phone_number" autocomplete="off"
                    {{-- value="{{ empty(old('phone_number')) ? '98' : old('phone_number') }}" --}}/>
                @if ($errors->has('phone_number'))
                    <span class="invalid-feedback d-block" role="alert">
                                            <strong>{{ $errors->first('phone_number') }}</strong>
                                </span>
                @endif
            </div>
            <!--begin::Form group-->
            <div class="fv-row mb-10">
                <div class="form-check form-check-custom form-check-solid mb-5" wire:ignore>
                    <input name="agree" class="form-check-input" type="checkbox" id="kt_login_toc_agree"
                           value="1"/>
                    <label class="form-check-label fw-bold text-gray-600" for="kt_login_toc_agree">با
                        <a href="#" class="ms-1">شرایط و قوانین</a> موافق هستم.</label>
                </div>
            </div>
            <!--end::Form group-->

        </form>
        <!--begin::Form group-->
        <div class="justify-content-end d-flex flex-wrap pb-lg-0 pb-5">
            <button id="" wire:key="first-step-register" wire:click="register_new_user()"
                    wire:loading.class="disabled" class="btn btn-primary fw-bolder fs-6 px-8 py-4 my-3 me-4"
                    wire:ignore.self>
                <span wire:loading.remove wire:target="register_new_user">ثبت نام</span>
                <span wire:loading wire:target="register_new_user">منتظر بمانید ...
                <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
            </button>
            <a href="{{route("loginPhone")}}" type="button"
               class="btn btn-light-primary fw-bolder fs-6 px-8 py-4 my-3"> حساب کاربری دارم
            </a>
        </div>
        <!--end::Form group-->

    @else
        <div>
            <!--begin::Form group-->
            <div class="fv-row mb-10">
                <label class="label-required form-label fs-6 fw-bolder text-dark">کد ارسالی</label>
                <label2>
                    کد تایید به {{ $phone_number }} ارسال شده است. <br/>
                </label2>

                <!--end::Action-->
                <div class="fv-row mb-10">
                    <form onsubmit="event.preventDefault();" onload="FocusOnInput">
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12">
                                <div class="otp-password flex-stack flex-column" style="display: flex;">
                                    <div id="inputOtp" name="otp" input-key="otp" data-vv-as="کد تایید"
                                         is-input-num="true"
                                         value="" style="display: flex; direction: ltr; width: 100%; max-width: 300px"
                                         wire:ignore>
                                        @php
                                            $size = '9';
                                           for($i=0;$i<$token_size;$i++){
                                               $size.='9';
                                           }
                                        @endphp
                                        <input type="text" data-inputmask="'mask': '{{$size}}'"
                                               id="register-submit-token"
                                               class="form-control form-control-solid otp-input text-align-center w-full"
                                               wire:model.debounce="registerNewToken">
                                        {{--                                    @for($i=0;$i<$token_size;$i++)--}}
                                        {{--                                        <input min="0" max="9" pattern="[0-9]" type="tel"--}}
                                        {{--                                               class="form-control form-control-solid otp-input text-align-center btn btn-icon ms-3 ms-lg-6"--}}
                                        {{--                                               maxlength="1">--}}
                                        {{--                                    @endfor--}}
                                    </div>
                                    <button tabindex="5"
                                            class="disabled fw-bolder fs-6 px-8 py-2 my-3 me-3 w-full  d-flex justify-center gap-5 align-items-center btn btn-outline btn-outline-dashed btn-outline-primary btn-active-light-primary"
                                            wire:click="refresh_token()">
                                        <span class="js-timeout fs-1 fw-bolder" wire:ignore>{{$count_down}}</span>
                                        ارسال مجدد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!--end::Signin Form-->

                @if ($errors->has('token_from_user'))
                    <div class="fv-plugins-message-container">
                        <div data-field="token_from_user" data-validator="token_from_user" class="fv-help-block">
                            {{ $errors->first('token_from_user') }}
                        </div>
                    </div>
                @endif
                @if ($errors->has('registerNewToken'))
                    <div class="fv-plugins-message-container">
                        <div data-field="token_from_user" data-validator="token_from_user" class="fv-help-block">
                            {{ $errors->first('registerNewToken') }}
                        </div>
                    </div>
                @endif
                @if ($errors->has('alert'))
                    <div class="fv-plugins-message-container">
                        <div data-field="alert" data-validator="alert" class="fv-help-block">
                            {{ $errors->first('alert') }}
                        </div>
                    </div>
                @endif
            </div>
            <!--end::Form group-->
        </div>

        <!--begin::Action-->
        <div class="text-align-left pb-lg-0 pb-5">
            <button tabindex="4"
                    wire:key="second-step-check-"
                    class="btn btn-primary fw-bolder fs-6 px-8 py-4 my-3 me-3" wire:click="check_token()"
                    wire:loading.class="disabled">
                <span wire:loading.remove wire:target="check_token">ثبت کد</span>
                <span wire:loading wire:target="check_token">منتظر بمانید ...
                <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
            </button>


            <button tabindex="5" class="btn btn-light-primary fw-bolder px-8 py-4 my-3 fs-6" wire:click="back_to_form()"
                    wire:key="cancel_verify">
                ویرایش اطلاعات
            </button>
        </div>
        <!--end::Action-->

        @push('scripts')

        @endpush

    @endif
    @script
    <script>
        window.interval;
        clearInterval(window.interval);

        function countdown() {
            clearInterval(window.interval);
            window.interval = setInterval(function () {
                var timer = $('.js-timeout').html();

                if (timer !== undefined) {
                    timer = timer.split(':');
                    if (timer.length == 1) {
                        var seconds = parseInt(toEnglishDigits(timer[0].toString()), 10);
                        var minutes = 0
                    } else {
                        console.log(toEnglishDigits(timer[0].toString()));
                        var minutes = parseInt(toEnglishDigits(timer[0].toString()), 10);
                        var seconds = parseInt(toEnglishDigits(timer[1].toString()), 10);
                    }

                    seconds -= 1;
                    if (minutes < 0) return;
                    else if (seconds < 0 && minutes != 0) {
                        minutes -= 1;
                        seconds = 59;
                    } else if (seconds < 10 && length.seconds != 2) seconds = '0' + seconds;

                    $('.js-timeout').html(toPersianDigit(minutes.toString()) + ':' + toPersianDigit(seconds.toString()));

                }

                if (minutes == 0 && seconds == 0) {
                    $('.js-timeout').parent().removeClass('disabled')
                    clearInterval(window.interval);
                }
            }, 1000);
        }


        function run_count_down(time) {
            var sec = time;
            var minnum = Math.floor(sec / 60);
            var secnum = sec - (minnum * 60)
            if (secnum >= 0 && secnum < 10) {
                secnum = "0" + `${secnum}`
            }
            $('.js-timeout').text(toPersianDigit(`${minnum}:${secnum}`));
            countdown();
        }

        run_count_down({{$count_down}})
        // END OF TIMER

        Livewire.on('refresh_timer', () => {
            run_count_down({{$static_count_down}})
        });
        // END OF TIMER

        Livewire.on('run_validation', () => {
            KTSignup.init().validate();
        });
    </script>
    <script>
        Livewire.on('start_count_down', () => {
            countdown()
        });
    </script>
    @endscript
</div>


@push('scripts')
    <script>
        $(document).ready(function () {
            $('.js-select').select2().on('change', function (e) {
                let livewire = $(this).data('livewire')
                eval(livewire).set('user_how_to_meet', $(this).val());
            });
        })

        //  Livewire.on('signup_validation', () => {
        //     KTSignup.init().validate()
        // });
    </script>

    <script>

        //        Livewire.hook('element.updated', () => {
        //            $('[data-mask]').inputmask(); /// Rebind the script after each component update
        //            alert("dsfds");
        //        });

    </script>

@endpush
