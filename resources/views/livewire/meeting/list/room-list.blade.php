<div>
    <!--begin::Table Widget 2-->
    <div class="card card-stretch mb-5 mb-xxl-8">
        <!--begin::Header-->
        <div class="card-header border-0 pt-5">
            <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bolder text-dark fs-3">اتاق های شما</span>
                <span class="text-muted mt-2 fw-bold fs-6">مجموعا  {{$roomCount}} اتاق </span>
            </h3>
            <div class="card-toolbar">
                <ul class="nav nav-pills nav-pills-sm nav-light">
                    <li class="nav-item">
                        <a class="nav-link btn btn-active-primary btn-color-muted py-2 px-4 fw-bolder
                         @if($activeTab === 3) active @endif
                        "
                           data-bs-toggle="tab" href="#kt_tab_pane_2_3">همه</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-active-primary btn-color-muted py-2 px-4
                         @if($activeTab === 1) active @endif
                         fw-bolder me-2"
                           data-bs-toggle="tab" href="#kt_tab_pane_2_1">فعال</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-active-primary btn-color-muted py-2 px-4 fw-bolder me-2
                         @if($activeTab === 2) active @endif
                        "
                           data-bs-toggle="tab" href="#kt_tab_pane_2_2">در انتظار</a>
                    </li>

                </ul>
            </div>
            <div class="card-toolbar">
                @if(false)
                    <!--begin::Dropdown-->
                    <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                            data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                        <!--begin::Svg Icon | path: icons/duotune/general/gen024.svg-->
                        <span class="svg-icon svg-icon-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px"
                             height="24px" viewBox="0 0 24 24">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                             <rect x="5" y="5" width="5" height="5" rx="1" fill="#000000"></rect>
                                <rect x="14" y="5" width="5" height="5" rx="1" fill="#000000" opacity="0.3"></rect>
                                <rect x="5" y="14" width="5" height="5" rx="1" fill="#000000" opacity="0.3"></rect>
                                <rect x="14" y="14" width="5" height="5" rx="1" fill="#000000" opacity="0.3"></rect>
                            </g>
                        </svg>
                    </span>
                        <!--end::Svg Icon-->
                    </button>
                    <!--begin::Form-->
                    <div class="menu menu-sub menu-sub-dropdown menu-column w-300px w-lg-350px p-5"
                         data-kt-menu="true">
                        <!--begin::Input-->
                        <div class="input-group input-group-solid mb-5">
                            <div class="input-group-prepend">
            <span class="input-group-text">
                <!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5"
                              x="17.0365"
                              y="15.1223"
                              width="8.15546"
                              height="2" rx="1"
                              transform="rotate(45 17.0365 15.1223)"
                              fill="black"></rect>
                        <path
                            d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                            fill="black"></path>
                    </svg>
                </span>
                <!--end::Svg Icon-->
            </span>
                            </div>
                            <input type="text" class="form-control ps-0" name="search" value=""
                                   wire:model.blur="roomSearch"
                                   placeholder="جستجو در اتاق ها">
                        </div>
                        <!--end::Input-->

                        <!--begin::Tabs-->
                        <ul class="nav nav-line-tabs nav-line-tabs-2x border-light fw-bold mb-5">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#kt_dropdown_2_tab_1">Today</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#kt_dropdown_2_tab_2">Last
                                    Week</a>
                            </li>
                        </ul>
                        <!--end::Tabs-->

                        <!--begin::Tab Content-->
                        <div class="tab-content">
                            <!--begin::Tab Pane-->
                            <div class="tab-pane active" id="kt_dropdown_2_tab_1">
                                <ul class="menu menu-custom menu-column menu-rounded menu-title-gray-600 menu-icon-muted menu-hover-bg-light-primary menu-active-bg-light-primary fw-bold">
                                    <li class="menu-item py-1">
                                        <a href="#" class="menu-link px-3">
                        <span class="menu-icon">
                            <!--begin::Svg Icon | path: icons/duotune/general/gen002.svg-->
<span class="svg-icon svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                       fill="none">
<path opacity="0.3"
      d="M4.05424 15.1982C8.34524 7.76818 13.5782 3.26318 20.9282 2.01418C21.0729 1.98837 21.2216 1.99789 21.3618 2.04193C21.502 2.08597 21.6294 2.16323 21.7333 2.26712C21.8372 2.37101 21.9144 2.49846 21.9585 2.63863C22.0025 2.7788 22.012 2.92754 21.9862 3.07218C20.7372 10.4222 16.2322 15.6552 8.80224 19.9462L4.05424 15.1982ZM3.81924 17.3372L2.63324 20.4482C2.58427 20.5765 2.5735 20.7163 2.6022 20.8507C2.63091 20.9851 2.69788 21.1082 2.79503 21.2054C2.89218 21.3025 3.01536 21.3695 3.14972 21.3982C3.28408 21.4269 3.42387 21.4161 3.55224 21.3672L6.66524 20.1802L3.81924 17.3372ZM16.5002 5.99818C16.2036 5.99818 15.9136 6.08615 15.6669 6.25097C15.4202 6.41579 15.228 6.65006 15.1144 6.92415C15.0009 7.19824 14.9712 7.49984 15.0291 7.79081C15.0869 8.08178 15.2298 8.34906 15.4396 8.55884C15.6494 8.76862 15.9166 8.91148 16.2076 8.96935C16.4986 9.02723 16.8002 8.99753 17.0743 8.884C17.3484 8.77046 17.5826 8.5782 17.7474 8.33153C17.9123 8.08486 18.0002 7.79485 18.0002 7.49818C18.0002 7.10035 17.8422 6.71882 17.5609 6.43752C17.2796 6.15621 16.8981 5.99818 16.5002 5.99818Z"
      fill="black"></path>
<path
    d="M4.05423 15.1982L2.24723 13.3912C2.15505 13.299 2.08547 13.1867 2.04395 13.0632C2.00243 12.9396 1.9901 12.8081 2.00793 12.679C2.02575 12.5498 2.07325 12.4266 2.14669 12.3189C2.22013 12.2112 2.31752 12.1219 2.43123 12.0582L9.15323 8.28918C7.17353 10.3717 5.4607 12.6926 4.05423 15.1982ZM8.80023 19.9442L10.6072 21.7512C10.6994 21.8434 10.8117 21.9129 10.9352 21.9545C11.0588 21.996 11.1903 22.0083 11.3195 21.9905C11.4486 21.9727 11.5718 21.9252 11.6795 21.8517C11.7872 21.7783 11.8765 21.6809 11.9402 21.5672L15.7092 14.8442C13.6269 16.8245 11.3061 18.5377 8.80023 19.9442ZM7.04023 18.1832L12.5832 12.6402C12.7381 12.4759 12.8228 12.2577 12.8195 12.032C12.8161 11.8063 12.725 11.5907 12.5653 11.4311C12.4057 11.2714 12.1901 11.1803 11.9644 11.1769C11.7387 11.1736 11.5205 11.2583 11.3562 11.4132L5.81323 16.9562L7.04023 18.1832Z"
    fill="black"></path>
</svg></span>
                            <!--end::Svg Icon-->                        </span>
                                            <span class="menu-title">
                            Web &amp; App History
                        </span>
                                        </a>
                                    </li>
                                    <li class="menu-item py-1">
                                        <a href="#" class="menu-link px-3">
                        <span class="menu-icon">
                            <!--begin::Svg Icon | path: icons/duotune/communication/com010.svg-->
<span class="svg-icon svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                       fill="none">
<path
    d="M6 8.725C6 8.125 6.4 7.725 7 7.725H14L18 11.725V12.925L22 9.725L12.6 2.225C12.2 1.925 11.7 1.925 11.4 2.225L2 9.725L6 12.925V8.725Z"
    fill="black"></path>
<path opacity="0.3"
      d="M22 9.72498V20.725C22 21.325 21.6 21.725 21 21.725H3C2.4 21.725 2 21.325 2 20.725V9.72498L11.4 17.225C11.8 17.525 12.3 17.525 12.6 17.225L22 9.72498ZM15 11.725H18L14 7.72498V10.725C14 11.325 14.4 11.725 15 11.725Z"
      fill="black"></path>
</svg></span>
                            <!--end::Svg Icon-->                        </span>
                                            <span class="menu-title">
                            Activity and Timeline
                        </span>
                                            <span class="menu-badge badge badge-light-danger fw-bold">
                            new
                        </span>
                                        </a>
                                    </li>
                                    <li class="menu-item py-1">
                                        <a href="#" class="menu-link px-3">
                        <span class="menu-icon">
                            <!--begin::Svg Icon | path: icons/duotune/files/fil017.svg-->
<span class="svg-icon svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                       fill="none">
<path opacity="0.3" d="M10 4H21C21.6 4 22 4.4 22 5V7H10V4Z" fill="black"></path>
<path opacity="0.3" d="M13 14.4V9C13 8.4 12.6 8 12 8C11.4 8 11 8.4 11 9V14.4H13Z" fill="black"></path>
<path
    d="M10.4 3.60001L12 6H21C21.6 6 22 6.4 22 7V19C22 19.6 21.6 20 21 20H3C2.4 20 2 19.6 2 19V4C2 3.4 2.4 3 3 3H9.20001C9.70001 3 10.2 3.20001 10.4 3.60001ZM13 14.4V9C13 8.4 12.6 8 12 8C11.4 8 11 8.4 11 9V14.4H8L11.3 17.7C11.7 18.1 12.3 18.1 12.7 17.7L16 14.4H13Z"
    fill="black"></path>
</svg></span>
                            <!--end::Svg Icon-->                        </span>
                                            <span class="menu-title">
                            Business Features
                        </span>
                                        </a>
                                    </li>
                                    <li class="menu-item py-1">
                                        <a href="#" class="menu-link px-3">
                        <span class="menu-icon">
                            <!--begin::Svg Icon | path: icons/duotune/abstract/abs021.svg-->
<span class="svg-icon svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                       fill="none">
<path
    d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM14.5 4.5C10.4 4.5 7 7.9 7 12C7 16.1 10.4 19.5 14.5 19.5C18.6 19.5 22 16.1 22 12C22 7.9 18.6 4.5 14.5 4.5Z"
    fill="black"></path>
<path opacity="0.3"
      d="M22 12C22 16.1 18.6 19.5 14.5 19.5C10.4 19.5 7 16.1 7 12C7 7.9 10.4 4.5 14.5 4.5C18.6 4.5 22 7.9 22 12ZM12 7C9.2 7 7 9.2 7 12C7 14.8 9.2 17 12 17C14.8 17 17 14.8 17 12C17 9.2 14.8 7 12 7Z"
      fill="black"></path>
</svg></span>
                            <!--end::Svg Icon-->                        </span>
                                            <span class="menu-title">
                            Accessibility Settings
                        </span>
                                        </a>
                                    </li>
                                    <li class="menu-item py-1">
                                        <a href="#" class="menu-link px-3">
                        <span class="menu-icon">
                            <!--begin::Svg Icon | path: icons/duotune/abstract/abs038.svg-->
<span class="svg-icon svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                       fill="none">
<path
    d="M12.0444 17.9444V12.1444L17.0444 15.0444C18.6444 15.9444 19.1445 18.0444 18.2445 19.6444C17.3445 21.2444 15.2445 21.7444 13.6445 20.8444C12.6445 20.2444 12.0444 19.1444 12.0444 17.9444ZM7.04445 15.0444L12.0444 12.1444L7.04445 9.24445C5.44445 8.34445 3.44444 8.84445 2.44444 10.4444C1.54444 12.0444 2.04445 14.0444 3.64445 15.0444C4.74445 15.6444 6.04445 15.6444 7.04445 15.0444ZM12.0444 6.34444V12.1444L17.0444 9.24445C18.6444 8.34445 19.1445 6.24444 18.2445 4.64444C17.3445 3.04444 15.2445 2.54445 13.6445 3.44445C12.6445 4.04445 12.0444 5.14444 12.0444 6.34444Z"
    fill="black"></path>
<path opacity="0.3"
      d="M7.04443 9.24445C6.04443 8.64445 5.34442 7.54444 5.34442 6.34444C5.34442 4.54444 6.84444 3.04443 8.64444 3.04443C10.4444 3.04443 11.9444 4.54444 11.9444 6.34444V12.1444L7.04443 9.24445ZM17.0444 15.0444C18.0444 15.6444 19.3444 15.6444 20.3444 15.0444C21.9444 14.1444 22.4444 12.0444 21.5444 10.4444C20.6444 8.84444 18.5444 8.34445 16.9444 9.24445L11.9444 12.1444L17.0444 15.0444ZM7.04443 15.0444C6.04443 15.6444 5.34442 16.7444 5.34442 17.9444C5.34442 19.7444 6.84444 21.2444 8.64444 21.2444C10.4444 21.2444 11.9444 19.7444 11.9444 17.9444V12.1444L7.04443 15.0444Z"
      fill="black"></path>
</svg></span>
                            <!--end::Svg Icon-->                        </span>
                                            <span class="menu-title">
                            Data &amp; Personalisation
                        </span>
                                        </a>
                                    </li>
                                    <li class="menu-item py-1">
                                        <a href="#" class="menu-link px-3">
                        <span class="menu-icon">
                            <!--begin::Svg Icon | path: icons/duotune/general/gen007.svg-->
<span class="svg-icon svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                       fill="none">
<path opacity="0.3"
      d="M12 22C13.6569 22 15 20.6569 15 19C15 17.3431 13.6569 16 12 16C10.3431 16 9 17.3431 9 19C9 20.6569 10.3431 22 12 22Z"
      fill="black"></path>
<path
    d="M19 15V18C19 18.6 18.6 19 18 19H6C5.4 19 5 18.6 5 18V15C6.1 15 7 14.1 7 13V10C7 7.6 8.7 5.6 11 5.1V3C11 2.4 11.4 2 12 2C12.6 2 13 2.4 13 3V5.1C15.3 5.6 17 7.6 17 10V13C17 14.1 17.9 15 19 15ZM11 10C11 9.4 11.4 9 12 9C12.6 9 13 8.6 13 8C13 7.4 12.6 7 12 7C10.3 7 9 8.3 9 10C9 10.6 9.4 11 10 11C10.6 11 11 10.6 11 10Z"
    fill="black"></path>
</svg></span>
                            <!--end::Svg Icon-->                        </span>
                                            <span class="menu-title">
                            General Preference
                        </span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <!--end::Tab Pane-->

                            <!--begin::Tab Pane-->
                            <div class="tab-pane" id="kt_dropdown_2_tab_2">
                                <ul class="menu menu-custom menu-column menu-rounded menu-title-gray-600 menu-icon-muted menu-hover-bg-light-primary menu-active-bg-light-primary fw-bold">
                                    <li class="menu-item py-1">
                                        <a href="#" class="menu-link active px-3">
                        <span class="menu-icon">
                            <!--begin::Svg Icon | path: icons/duotune/communication/com010.svg-->
<span class="svg-icon svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                       fill="none">
<path
    d="M6 8.725C6 8.125 6.4 7.725 7 7.725H14L18 11.725V12.925L22 9.725L12.6 2.225C12.2 1.925 11.7 1.925 11.4 2.225L2 9.725L6 12.925V8.725Z"
    fill="black"></path>
<path opacity="0.3"
      d="M22 9.72498V20.725C22 21.325 21.6 21.725 21 21.725H3C2.4 21.725 2 21.325 2 20.725V9.72498L11.4 17.225C11.8 17.525 12.3 17.525 12.6 17.225L22 9.72498ZM15 11.725H18L14 7.72498V10.725C14 11.325 14.4 11.725 15 11.725Z"
      fill="black"></path>
</svg></span>
                            <!--end::Svg Icon-->                        </span>
                                            <span class="menu-title">
                            Activity and Timeline
                        </span>
                                            <span class="menu-badge badge badge-danger fw-bold">
                            new
                        </span>
                                        </a>
                                    </li>

                                    <li class="menu-item py-1">
                                        <a href="#" class="menu-link px-3">
                        <span class="menu-icon">
                            <!--begin::Svg Icon | path: icons/duotune/files/fil017.svg-->
<span class="svg-icon svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                       fill="none">
<path opacity="0.3" d="M10 4H21C21.6 4 22 4.4 22 5V7H10V4Z" fill="black"></path>
<path opacity="0.3" d="M13 14.4V9C13 8.4 12.6 8 12 8C11.4 8 11 8.4 11 9V14.4H13Z" fill="black"></path>
<path
    d="M10.4 3.60001L12 6H21C21.6 6 22 6.4 22 7V19C22 19.6 21.6 20 21 20H3C2.4 20 2 19.6 2 19V4C2 3.4 2.4 3 3 3H9.20001C9.70001 3 10.2 3.20001 10.4 3.60001ZM13 14.4V9C13 8.4 12.6 8 12 8C11.4 8 11 8.4 11 9V14.4H8L11.3 17.7C11.7 18.1 12.3 18.1 12.7 17.7L16 14.4H13Z"
    fill="black"></path>
</svg></span>
                            <!--end::Svg Icon-->                        </span>
                                            <span class="menu-title">
                            Business Features
                        </span>
                                        </a>
                                    </li>

                                    <li class="menu-item py-1">
                                        <a href="#" class="menu-link px-3">
                        <span class="menu-icon">
                            <!--begin::Svg Icon | path: icons/duotune/abstract/abs021.svg-->
<span class="svg-icon svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                       fill="none">
<path
    d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM14.5 4.5C10.4 4.5 7 7.9 7 12C7 16.1 10.4 19.5 14.5 19.5C18.6 19.5 22 16.1 22 12C22 7.9 18.6 4.5 14.5 4.5Z"
    fill="black"></path>
<path opacity="0.3"
      d="M22 12C22 16.1 18.6 19.5 14.5 19.5C10.4 19.5 7 16.1 7 12C7 7.9 10.4 4.5 14.5 4.5C18.6 4.5 22 7.9 22 12ZM12 7C9.2 7 7 9.2 7 12C7 14.8 9.2 17 12 17C14.8 17 17 14.8 17 12C17 9.2 14.8 7 12 7Z"
      fill="black"></path>
</svg></span>
                            <!--end::Svg Icon-->                        </span>
                                            <span class="menu-title">
                            Accessibility Settings
                        </span>
                                        </a>
                                    </li>

                                    <li class="menu-item py-1">
                                        <a href="#" class="menu-link px-3">
                        <span class="menu-icon">
                            <!--begin::Svg Icon | path: icons/duotune/abstract/abs038.svg-->
<span class="svg-icon svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                       fill="none">
<path
    d="M12.0444 17.9444V12.1444L17.0444 15.0444C18.6444 15.9444 19.1445 18.0444 18.2445 19.6444C17.3445 21.2444 15.2445 21.7444 13.6445 20.8444C12.6445 20.2444 12.0444 19.1444 12.0444 17.9444ZM7.04445 15.0444L12.0444 12.1444L7.04445 9.24445C5.44445 8.34445 3.44444 8.84445 2.44444 10.4444C1.54444 12.0444 2.04445 14.0444 3.64445 15.0444C4.74445 15.6444 6.04445 15.6444 7.04445 15.0444ZM12.0444 6.34444V12.1444L17.0444 9.24445C18.6444 8.34445 19.1445 6.24444 18.2445 4.64444C17.3445 3.04444 15.2445 2.54445 13.6445 3.44445C12.6445 4.04445 12.0444 5.14444 12.0444 6.34444Z"
    fill="black"></path>
<path opacity="0.3"
      d="M7.04443 9.24445C6.04443 8.64445 5.34442 7.54444 5.34442 6.34444C5.34442 4.54444 6.84444 3.04443 8.64444 3.04443C10.4444 3.04443 11.9444 4.54444 11.9444 6.34444V12.1444L7.04443 9.24445ZM17.0444 15.0444C18.0444 15.6444 19.3444 15.6444 20.3444 15.0444C21.9444 14.1444 22.4444 12.0444 21.5444 10.4444C20.6444 8.84444 18.5444 8.34445 16.9444 9.24445L11.9444 12.1444L17.0444 15.0444ZM7.04443 15.0444C6.04443 15.6444 5.34442 16.7444 5.34442 17.9444C5.34442 19.7444 6.84444 21.2444 8.64444 21.2444C10.4444 21.2444 11.9444 19.7444 11.9444 17.9444V12.1444L7.04443 15.0444Z"
      fill="black"></path>
</svg></span>
                            <!--end::Svg Icon-->                        </span>
                                            <span class="menu-title">
                            Data &amp; Personalisation
                        </span>
                                        </a>
                                    </li>

                                    <li class="menu-item py-1">
                                        <a href="#" class="menu-link px-3">
                        <span class="menu-icon">
                            <!--begin::Svg Icon | path: icons/duotune/general/gen007.svg-->
<span class="svg-icon svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                       fill="none">
<path opacity="0.3"
      d="M12 22C13.6569 22 15 20.6569 15 19C15 17.3431 13.6569 16 12 16C10.3431 16 9 17.3431 9 19C9 20.6569 10.3431 22 12 22Z"
      fill="black"></path>
<path
    d="M19 15V18C19 18.6 18.6 19 18 19H6C5.4 19 5 18.6 5 18V15C6.1 15 7 14.1 7 13V10C7 7.6 8.7 5.6 11 5.1V3C11 2.4 11.4 2 12 2C12.6 2 13 2.4 13 3V5.1C15.3 5.6 17 7.6 17 10V13C17 14.1 17.9 15 19 15ZM11 10C11 9.4 11.4 9 12 9C12.6 9 13 8.6 13 8C13 7.4 12.6 7 12 7C10.3 7 9 8.3 9 10C9 10.6 9.4 11 10 11C10.6 11 11 10.6 11 10Z"
    fill="black"></path>
</svg></span>
                            <!--end::Svg Icon-->                        </span>
                                            <span class="menu-title">
                            General Preference
                        </span>
                                        </a>
                                    </li>

                                    <li class="menu-item py-1">
                                        <a href="#" class="menu-link px-3">
                        <span class="menu-icon">
                            <!--begin::Svg Icon | path: icons/duotune/general/gen002.svg-->
<span class="svg-icon svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                       fill="none">
<path opacity="0.3"
      d="M4.05424 15.1982C8.34524 7.76818 13.5782 3.26318 20.9282 2.01418C21.0729 1.98837 21.2216 1.99789 21.3618 2.04193C21.502 2.08597 21.6294 2.16323 21.7333 2.26712C21.8372 2.37101 21.9144 2.49846 21.9585 2.63863C22.0025 2.7788 22.012 2.92754 21.9862 3.07218C20.7372 10.4222 16.2322 15.6552 8.80224 19.9462L4.05424 15.1982ZM3.81924 17.3372L2.63324 20.4482C2.58427 20.5765 2.5735 20.7163 2.6022 20.8507C2.63091 20.9851 2.69788 21.1082 2.79503 21.2054C2.89218 21.3025 3.01536 21.3695 3.14972 21.3982C3.28408 21.4269 3.42387 21.4161 3.55224 21.3672L6.66524 20.1802L3.81924 17.3372ZM16.5002 5.99818C16.2036 5.99818 15.9136 6.08615 15.6669 6.25097C15.4202 6.41579 15.228 6.65006 15.1144 6.92415C15.0009 7.19824 14.9712 7.49984 15.0291 7.79081C15.0869 8.08178 15.2298 8.34906 15.4396 8.55884C15.6494 8.76862 15.9166 8.91148 16.2076 8.96935C16.4986 9.02723 16.8002 8.99753 17.0743 8.884C17.3484 8.77046 17.5826 8.5782 17.7474 8.33153C17.9123 8.08486 18.0002 7.79485 18.0002 7.49818C18.0002 7.10035 17.8422 6.71882 17.5609 6.43752C17.2796 6.15621 16.8981 5.99818 16.5002 5.99818Z"
      fill="black"></path>
<path
    d="M4.05423 15.1982L2.24723 13.3912C2.15505 13.299 2.08547 13.1867 2.04395 13.0632C2.00243 12.9396 1.9901 12.8081 2.00793 12.679C2.02575 12.5498 2.07325 12.4266 2.14669 12.3189C2.22013 12.2112 2.31752 12.1219 2.43123 12.0582L9.15323 8.28918C7.17353 10.3717 5.4607 12.6926 4.05423 15.1982ZM8.80023 19.9442L10.6072 21.7512C10.6994 21.8434 10.8117 21.9129 10.9352 21.9545C11.0588 21.996 11.1903 22.0083 11.3195 21.9905C11.4486 21.9727 11.5718 21.9252 11.6795 21.8517C11.7872 21.7783 11.8765 21.6809 11.9402 21.5672L15.7092 14.8442C13.6269 16.8245 11.3061 18.5377 8.80023 19.9442ZM7.04023 18.1832L12.5832 12.6402C12.7381 12.4759 12.8228 12.2577 12.8195 12.032C12.8161 11.8063 12.725 11.5907 12.5653 11.4311C12.4057 11.2714 12.1901 11.1803 11.9644 11.1769C11.7387 11.1736 11.5205 11.2583 11.3562 11.4132L5.81323 16.9562L7.04023 18.1832Z"
    fill="black"></path>
</svg></span>
                            <!--end::Svg Icon-->                        </span>
                                            <span class="menu-title">
                            Web &amp; App History
                        </span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <!--end::Tab Pane-->
                        </div>
                        <!--end::Tab Content-->
                    </div>
                    <!--end::Form-->
                    <!--end::Dropdown-->
                @endif
            </div>
        </div>
        <!--end::Header-->

        <!--begin::Body-->
        <div class="card-body pt-3 pb-0 mt-n3">
            <div class="tab-content mt-4" id="myTabTables2">
                <!--begin::Tap pane-->
                <div class="tab-pane fade show
                 @if($activeTab === 1) active @endif
                 @if($tagLastItem) tagLastItem @endif
                " id="kt_tab_pane_2_1" role="tabpanel"
                     aria-labelledby="kt_tab_pane_2_1">
                    <!--begin::Table-->
                    <div class="table-responsive">
                        <livewire:meeting.list.active-rooms/>
                    </div>
                    <!--end::Table-->
                </div>
                <!--end::Tap pane-->
                <!--begin::Tap pane-->
                <div class="tab-pane fade show
                 @if($activeTab === 2) active @endif
                 @if($tagLastItem) tagLastItem @endif
                " id="kt_tab_pane_2_2" role="tabpanel"
                     aria-labelledby="kt_tab_pane_2_2">
                    <!--begin::Table-->
                    <div class="table-responsive">
                        <livewire:meeting.list.inactive-rooms/>
                    </div>
                    <!--end::Table-->
                </div>
                <!--end::Tap pane-->
                <!--begin::Tap pane-->
                <div class="tab-pane fade show
                 @if($activeTab === 3) active @endif
                 @if($tagLastItem) tagLastItem @endif
                " id="kt_tab_pane_2_3" role="tabpanel"
                     aria-labelledby="kt_tab_pane_2_3">
                    <!--begin::Table-->
                    <div class="table-responsive">
                        <livewire:meeting.list.all-rooms/>
                    </div>
                    <!--end::Table-->
                </div>
                <!--end::Tap pane-->
                @if(false)
                    <!--begin::Tap pane-->
                    <div class="tab-pane fade" id="kt_tab_pane_2_2" role="tabpanel"
                         aria-labelledby="kt_tab_pane_2_2">
                        <!--begin::Table-->
                        <div class="table-responsive">
                            <table class="table table-borderless align-middle">
                                <thead>
                                <tr>
                                    <th class="p-0 w-50px"></th>
                                    <th class="p-0 min-w-150px"></th>
                                    <th class="p-0 min-w-120px"></th>
                                    <th class="p-0 min-w-70px"></th>
                                    <th class="p-0 min-w-70px"></th>
                                    <th class="p-0 min-w-50px"></th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td class="p-0 py-3">
                                        <div class="symbol symbol-55px mt-1 me-5">
                                        <span class="symbol-label bg-light-warning align-items-end">
                                            <img alt="Logo" src="/start/assets/media/svg/avatars/047-girl-25.svg"
                                                 class="mh-40px">
                                        </span>
                                        </div>
                                    </td>
                                    <td class="px-0">
                                        <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6">Lebron
                                            Wayde</a>
                                        <span class="text-muted fw-bold d-block mt-1">Awesome Users</span>
                                    </td>
                                    <td></td>
                                    <td class="text-end">
                                    <span class="text-gray-800 fw-bolder d-block fs-6">
                                        $3,400,000
                                    </span>
                                        <span class="text-muted fw-bold d-block mt-1 fs-7">
                                        Paid
                                    </span>
                                    </td>
                                    <td class="text-end">
                                    <span class="fw-bolder text-warning">
                                        +34%
                                    </span>
                                    </td>
                                    <td class="text-end pe-0">
                                        <a href="#"
                                           class="btn btn-icon btn-bg-light btn-active-primary btn-sm">
                                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                            <span class="svg-icon svg-icon-4"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                    height="24" viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="black"></rect>
<path
    d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
    fill="black"></path>
</svg></span>
                                            <!--end::Svg Icon-->                                    </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 py-3">
                                        <div class="symbol symbol-55px mt-1">
                                        <span class="symbol-label bg-light-success align-items-end">
                                            <img alt="Logo" src="/start/assets/media/svg/avatars/043-boy-18.svg"
                                                 class="mh-40px">
                                        </span>
                                        </div>
                                    </td>
                                    <td class="px-0">
                                        <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6">Kevin
                                            Leonard</a>
                                        <span class="text-muted fw-bold d-block mt-1">Awesome Userss</span>
                                    </td>
                                    <td></td>
                                    <td class="text-end">
                                    <span class="text-gray-800 fw-bolder d-block fs-6">
                                        $35,600,000
                                    </span>
                                        <span class="text-muted fw-bold d-block mt-1 fs-7">
                                        Paid
                                    </span>
                                    </td>
                                    <td class="text-end">
                                    <span class="fw-bolder text-success">
                                        +230%
                                    </span>
                                    </td>
                                    <td class="text-end pe-0">
                                        <a href="#"
                                           class="btn btn-icon btn-bg-light btn-active-primary btn-sm">
                                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                            <span class="svg-icon svg-icon-4"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                    height="24" viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="black"></rect>
<path
    d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
    fill="black"></path>
</svg></span>
                                            <!--end::Svg Icon-->                                    </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 py-3">
                                        <div class="symbol symbol-55px mt-1">
                                        <span class="symbol-label bg-light-info align-items-end">
                                            <img alt="Logo" src="/start/assets/media/svg/avatars/024-boy-9.svg"
                                                 class="mh-40px">
                                        </span>
                                        </div>
                                    </td>
                                    <td class="px-0">
                                        <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6">Randy
                                            Trent</a>
                                        <span
                                            class="text-muted fw-bold d-block mt-1">Business Analyst</span>
                                    </td>
                                    <td></td>
                                    <td class="text-end">
                                    <span class="text-gray-800 fw-bolder d-block fs-6">
                                        $45,200,000
                                    </span>
                                        <span class="text-muted fw-bold d-block mt-1 fs-7">
                                        Paid
                                    </span>
                                    </td>
                                    <td class="text-end">
                                    <span class="fw-bolder text-info">
                                        +340%
                                    </span>
                                    </td>
                                    <td class="text-end pe-0">
                                        <a href="#"
                                           class="btn btn-icon btn-bg-light btn-active-primary btn-sm">
                                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                            <span class="svg-icon svg-icon-4"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                    height="24" viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="black"></rect>
<path
    d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
    fill="black"></path>
</svg></span>
                                            <!--end::Svg Icon-->                                    </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 py-3">
                                        <div class="symbol symbol-55px me-5 mt-1">
                                        <span class="symbol-label bg-light-primary align-items-end">
                                            <img alt="Logo" src="/start/assets/media/svg/avatars/001-boy.svg"
                                                 class="mh-40px">
                                        </span>
                                        </div>
                                    </td>
                                    <td class="px-0">
                                        <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6">Brad
                                            Simmons</a>
                                        <span
                                            class="text-muted fw-bold d-block mt-1">HTML, CSS Coding</span>
                                    </td>
                                    <td></td>
                                    <td class="text-end">
                                    <span class="text-gray-800 fw-bolder d-block fs-6">
                                        $1,200,000
                                    </span>
                                        <span class="text-muted fw-bold d-block mt-1 fs-7">
                                        Paid
                                    </span>
                                    </td>
                                    <td class="text-end">
                                    <span class="fw-bolder text-primary">
                                        +28%
                                    </span>
                                    </td>
                                    <td class="text-end pe-0">
                                        <a href="#"
                                           class="btn btn-icon btn-bg-light btn-active-primary btn-sm">
                                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                            <span class="svg-icon svg-icon-4"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                    height="24" viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="black"></rect>
<path
    d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
    fill="black"></path>
</svg></span>
                                            <!--end::Svg Icon-->                                    </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 py-3">
                                        <div class="symbol symbol-55px mt-1">
                                        <span class="symbol-label bg-light-danger align-items-end">
                                            <img alt="Logo" src="/start/assets/media/svg/avatars/018-girl-9.svg"
                                                 class="mh-40px">
                                        </span>
                                        </div>
                                    </td>
                                    <td class="px-0">
                                        <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6">Jessie
                                            Clarcson</a>
                                        <span class="text-muted fw-bold d-block mt-1">Most Successful</span>
                                    </td>
                                    <td></td>
                                    <td class="text-end">
                                    <span class="text-gray-800 fw-bolder d-block fs-6">
                                        $1,200,000
                                    </span>
                                        <span class="text-muted fw-bold d-block mt-1 fs-7">
                                        Paid
                                    </span>
                                    </td>
                                    <td class="text-end">
                                    <span class="fw-bolder text-danger">
                                        +52%
                                    </span>
                                    </td>
                                    <td class="text-end pe-0">
                                        <a href="#"
                                           class="btn btn-icon btn-bg-light btn-active-primary btn-sm">
                                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                            <span class="svg-icon svg-icon-4"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                    height="24" viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="black"></rect>
<path
    d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
    fill="black"></path>
</svg></span>
                                            <!--end::Svg Icon-->                                    </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--end::Table-->
                    </div>
                    <!--end::Tap pane-->

                    <!--begin::Tap pane-->
                    <div class="tab-pane fade" id="kt_tab_pane_2_3" role="tabpanel"
                         aria-labelledby="kt_tab_pane_2_3">
                        <!--begin::Table-->
                        <div class="table-responsive">
                            <table class="table table-borderless align-middle">
                                <thead>
                                <tr>
                                    <th class="p-0 w-50px"></th>
                                    <th class="p-0 min-w-150px"></th>
                                    <th class="p-0 min-w-120px"></th>
                                    <th class="p-0 min-w-70px"></th>
                                    <th class="p-0 min-w-70px"></th>
                                    <th class="p-0 min-w-50px"></th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td class="p-0 pb-3 pt-1">
                                        <div class="symbol symbol-55px mt-3 me-5">
                                        <span class="symbol-label bg-light-danger align-items-end">
                                            <img alt="Logo" src="/start/assets/media/svg/avatars/018-girl-9.svg"
                                                 class="mh-40px">
                                        </span>
                                        </div>
                                    </td>
                                    <td class="px-0">
                                        <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6">Jessie
                                            Clarcson</a>
                                        <span class="text-muted fw-bold d-block mt-1">Most Successful</span>
                                    </td>
                                    <td></td>
                                    <td class="text-end">
                                    <span class="text-gray-800 fw-bolder d-block fs-6">
                                        $1,200,000
                                    </span>
                                        <span class="text-muted fw-bold d-block mt-1 fs-7">
                                        Paid
                                    </span>
                                    </td>
                                    <td class="text-end">
                                    <span class="fw-bolder text-danger">
                                        +52%
                                    </span>
                                    </td>
                                    <td class="text-end pe-0">
                                        <a href="#"
                                           class="btn btn-icon btn-bg-light btn-active-primary btn-sm">
                                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                            <span class="svg-icon svg-icon-4"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                    height="24" viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="black"></rect>
<path
    d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
    fill="black"></path>
</svg></span>
                                            <!--end::Svg Icon-->                                    </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 pb-3 pt-1">
                                        <div class="symbol symbol-55px mt-3">
                                        <span class="symbol-label bg-light-warning align-items-end">
                                            <img alt="Logo" src="/start/assets/media/svg/avatars/047-girl-25.svg"
                                                 class="mh-40px">
                                        </span>
                                        </div>
                                    </td>
                                    <td class="px-0">
                                        <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6">Lebron
                                            Wayde</a>
                                        <span class="text-muted fw-bold d-block mt-1">Awesome Users</span>
                                    </td>
                                    <td></td>
                                    <td class="text-end">
                                    <span class="text-gray-800 fw-bolder d-block fs-6">
                                        $3,400,000
                                    </span>
                                        <span class="text-muted fw-bold d-block mt-1 fs-7">
                                        Paid
                                    </span>
                                    </td>
                                    <td class="text-end">
                                    <span class="fw-bolder text-warning">
                                        +34%
                                    </span>
                                    </td>
                                    <td class="text-end pe-0">
                                        <a href="#"
                                           class="btn btn-icon btn-bg-light btn-active-primary btn-sm">
                                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                            <span class="svg-icon svg-icon-4"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                    height="24" viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="black"></rect>
<path
    d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
    fill="black"></path>
</svg></span>
                                            <!--end::Svg Icon-->                                    </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 pb-3 pt-1">
                                        <div class="symbol symbol-55px mt-3">
                                        <span class="symbol-label  bg-light-success align-items-end">
                                            <img alt="Logo" src="/start/assets/media/svg/avatars/043-boy-18.svg"
                                                 class="mh-40px">
                                        </span>
                                        </div>
                                    </td>
                                    <td class="px-0">
                                        <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6">Kevin
                                            Leonard</a>
                                        <span class="text-muted fw-bold d-block mt-1">Awesome Userss</span>
                                    </td>
                                    <td></td>
                                    <td class="text-end">
                                    <span class="text-gray-800 fw-bolder d-block fs-6">
                                        $35,600,000
                                    </span>
                                        <span class="text-muted fw-bold d-block mt-1 fs-7">
                                        Paid
                                    </span>
                                    </td>
                                    <td class="text-end">
                                    <span class="fw-bolder text-success">
                                        +230%
                                    </span>
                                    </td>
                                    <td class="text-end pe-0">
                                        <a href="#"
                                           class="btn btn-icon btn-bg-light btn-active-primary btn-sm">
                                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                            <span class="svg-icon svg-icon-4"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                    height="24" viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="black"></rect>
<path
    d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
    fill="black"></path>
</svg></span>
                                            <!--end::Svg Icon-->                                    </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 pb-3 pt-1">
                                        <div class="symbol symbol-55px me-5 mt-3">
                                        <span class="symbol-label bg-light-primary align-items-end">
                                            <img alt="Logo" src="/start/assets/media/svg/avatars/001-boy.svg"
                                                 class="mh-40px">
                                        </span>
                                        </div>
                                    </td>
                                    <td class="px-0">
                                        <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6">Brad
                                            Simmons</a>
                                        <span
                                            class="text-muted fw-bold d-block mt-1">HTML, CSS Coding</span>
                                    </td>
                                    <td></td>
                                    <td class="text-end">
                                    <span class="text-gray-800 fw-bolder d-block fs-6">
                                        $1,200,000
                                    </span>
                                        <span class="text-muted fw-bold d-block mt-1 fs-7">
                                        Paid
                                    </span>
                                    </td>
                                    <td class="text-end">
                                    <span class="fw-bolder text-primary">
                                        +28%
                                    </span>
                                    </td>
                                    <td class="text-end pe-0">
                                        <a href="#"
                                           class="btn btn-icon btn-bg-light btn-active-primary btn-sm">
                                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                            <span class="svg-icon svg-icon-4"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                    height="24" viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="black"></rect>
<path
    d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
    fill="black"></path>
</svg></span>
                                            <!--end::Svg Icon-->                                    </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 pb-3 pt-1">
                                        <div class="symbol symbol-55px mt-3">
                                        <span class="symbol-label bg-light-info align-items-end">
                                            <img alt="Logo" src="/start/assets/media/svg/avatars/024-boy-9.svg"
                                                 class="mh-40px">
                                        </span>
                                        </div>
                                    </td>
                                    <td class="px-0">
                                        <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6">Randy
                                            Trent</a>
                                        <span
                                            class="text-muted fw-bold d-block mt-1">Business Analyst</span>
                                    </td>
                                    <td></td>
                                    <td class="text-end">
                                    <span class="text-gray-800 fw-bolder d-block fs-6">
                                        $45,200,000
                                    </span>
                                        <span class="text-muted fw-bold d-block mt-1 fs-7">
                                        Paid
                                    </span>
                                    </td>
                                    <td class="text-end">
                                    <span class="fw-bolder text-info">
                                        +340%
                                    </span>
                                    </td>
                                    <td class="text-end pe-0">
                                        <a href="#"
                                           class="btn btn-icon btn-bg-light btn-active-primary btn-sm">
                                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                            <span class="svg-icon svg-icon-4"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                    height="24" viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="black"></rect>
<path
    d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
    fill="black"></path>
</svg></span>
                                            <!--end::Svg Icon-->                                    </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--end::Table-->
                    </div>
                    <!--end::Tap pane-->
                @endif
            </div>
        </div>
        <!--end::Body-->
    </div>
    <!--end::Table Widget 2-->
</div>
