@php
    if(auth()->user()->email === config('app.admin_email')){
        $isActive = true;
    }else{
        $isActive = $meeting?->licenceUser?->hasRemindDaysLeft;
    }
@endphp


@include('components.action-btn.meetingLinkCopy',['actionLink' => route('room.index',$meeting->meeting_id),'isActive'=>$isActive ])

@include('components.action-btn.setting',['actionLink' => route('room.index',$meeting->meeting_id),'isActive'=>$isActive])
@include('components.action-btn.user',['actionLink' => route('room.users',$meeting->meeting_id),'isActive'=>$isActive])
@include('components.action-btn.file',['actionLink' =>route('room.files',$meeting->meeting_id) ,'isActive'=>$isActive])
@include('components.action-btn.audit',['actionLink' =>route('room.audits',$meeting->meeting_id),'isActive'=>$isActive ])
@if(!$meeting->is_invited === true)
    @include('components.action-btn.enterprise',['actionLink' =>route('room.enterprise',$meeting->meeting_id),'isActive'=>$isActive ])
@else


@endif
