<div>
    {{-- Care about people's approval and you will be their prisoner. --}}
    <table class="table table-borderless align-middle">
        <thead>
        <tr>
            <th class="p-0 w-50px"></th>
            <th class="p-0 min-w-70px"></th>
        </tr>
        </thead>
        <tbody>
        @forelse($inactiveMeetingList as $meeting)

            <tr>
                <td class="px-0 py-3">
                    {!! \App\Enums\MeetingStatusEnum::badge($meeting->status,true) !!}
                </td>
                <td class="px-0 text-align-right">
                    <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6">
                        {{$meeting->settings->title ?? " "}}
                    </a>
                    <span class="text-muted fw-bold d-block mt-1">
                        وضعیت: {{\App\Enums\MeetingStatusEnum::tryFrom($meeting->status)->name ?? " "}}
                    </span>
                </td>
                <td class="text-end pe-0 roomSettings">
                    @include('livewire.meeting.list.meeting-links-component')
                </td>
            </tr>
        @empty
            @include('components.emptyList')
        @endforelse
        </tbody>
    </table>
</div>
