<div class="card card-stretch mb-5 mb-xxl-8">
    <!--begin::Header-->
    <div class="card-header border-0 pt-5">
        <h3 class="card-title align-items-start flex-column">
            <span class="card-label fw-bolder text-dark fs-3">لایسنس ها</span>
            <span class="text-muted mt-2 fw-bold fs-6">به میزان دلخواه میتوانید لایسنس تهیه کنید</span>
        </h3>
        <div class="card-toolbar">
            <ul class="nav nav-pills nav-pills-sm nav-light">
                <li class="nav-item d-none">
                    <a class="nav-link btn btn-active-light btn-color-muted py-2 px-4 active fw-bolder me-2"
                       data-bs-toggle="tab" href="#kt_tab_pane_1_1">فعال</a>
                </li>
                <li>
                    <a href="{{route('licence.index')}}" class="btn btn-sm btn-light-primary fw-bolder">
                        <i class="las la-plus"></i>
                        خرید لایسنس جدید
                    </a>
                </li>
            </ul>
        </div>
    </div>
    <!--end::Header-->

    <!--begin::Body-->
    <div class="card-body pt-2 pb-0 mt-n3 overflow-auto">
        <div class="tab-content mt-5" id="myTabTables1">
            <!--begin::Tap pane-->
            <div class="tab-pane fade show active" id="kt_tab_pane_1_1" role="tabpanel"
                 aria-labelledby="kt_tab_pane_1_1">
                <!--begin::Table-->
                <div class="table-responsive">
                    <table class="table table-borderless align-middle">
                        <thead>
                        <tr>
                            <th class="p-0 w-50px"></th>
                            <th class="p-0 min-w-200px"></th>
                            <th class="p-0 min-w-100px"></th>
                            <th class="p-0 min-w-40px"></th>
                        </tr>
                        </thead>
                        <tbody>

                        @forelse($licences as $licence)

                            <tr>
                                <th class="px-0 py-3">
                                    <div class="symbol symbol-65px  me-3">
                                        {!!$licence->detail->icon!!}
                                    </div>
                                </th>
                                <td class="ps-0 text-align-right">
                                    <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6">
                                        {{$licence->detail->title}} (کد: {{$licence->hash_id}})
                                    </a>
                                    <span class="text-muted fw-bold d-block mt-1">
                                            ثبت شده در
                                        {{\Morilog\Jalali\Jalalian::fromDateTime($licence->created_at)->format('d %B ساعت H')}}

                                        </span>
                                </td>
                                <td>
                                    <div class="d-flex flex-column w-100 me-3">
                                        <div class="d-flex flex-stack mb-2">
                                            <span class="text-dark me-2 fs-6 fw-bolder">
                                                @if($licence->used_days_percentage >=100)
                                                    منقضی شده!
                                                @else
                                                    زمان استفاده شده
                                                @endif
                                            </span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div class="progress h-6px  w-100 bg-light-{{$licence->htmlClass}}">
                                                <div class="progress-bar bg-{{$licence->htmlClass}}"
                                                     role="progressbar"
                                                     style="width:  {{$licence->usedDaysPercentage}}%;"
                                                     aria-valuenow="50"
                                                     aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                            <span class="text-muted fs-7 fw-bold ps-3">
                                                 {{$licence->used_days_percentage}}%
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                    <td class="text-end pe-0">
                                        <a href="{{route('licence.show',['licence'=> $licence->hash_id])}}"
                                           class="btn btn-bg-light btn-active-primary btn-sm">
                                            @if($licence->used_days_percentage > 90)
                                            تمدید
                                            @else
                                                جزئیات
                                            @endif
                                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                            <span class="svg-icon svg-icon-4 m-0">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                     viewBox="0 0 24 24" fill="none">
                                                    <rect opacity="0.5" x="6" y="11" width="13" height="2" rx="1"
                                                          fill="black"/>
                                                    <path
                                                        d="M8.56569 11.4343L12.75 7.25C13.1642 6.83579 13.1642 6.16421 12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75L5.70711 11.2929C5.31658 11.6834 5.31658 12.3166 5.70711 12.7071L11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25C13.1642 17.8358 13.1642 17.1642 12.75 16.75L8.56569 12.5657C8.25327 12.2533 8.25327 11.7467 8.56569 11.4343Z"
                                                        fill="black"/>
                                                </svg>
                                            </span>
                                            <!--end::Svg Icon-->
                                        </a>
                                    </td>
                            </tr>
                        @empty
                            @include('components.emptyList',[
                                                             'emptyText'  => 'شما لایسنس ثبت شده ای ندارید!',
                                                             'emptyLink' => [
                                                                 'icon'  => ' <i class="las la-plus"></i>',
                                                                 'title'=> 'لایسنس جدید دریافت کنید',
                                                                 'address'   => route('licence.index')
                                                             ]
                                                         ])

                        @endforelse

                        </tbody>
                    </table>
                </div>
                <!--end::Table-->
            </div>
            <!--end::Tap pane-->
        </div>
    </div>
</div>
<!--end::Table Widget 1-->

