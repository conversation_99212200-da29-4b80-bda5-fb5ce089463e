<div>
    @if(count($licences) < 1)
        <h3 class="text-dark text-center fs-1 fw-bolder pt-15 lh-lg">برای شروع یک لایسنس تهیه کنید</h3>
        <br/>
        <div class="text-center">
            <a href="{{route('licence.index')}}" class="btn btn-sm btn-primary fw-bolder">
                <i class="las la-plus"></i>
                خرید لایسنس جدید
            </a>

        </div>
    @else
        <!--begin::Text-->
        <h3 class="text-dark text-center fs-1 fw-bolder pt-15 lh-lg">
            @if($wrapperRoomCount<1)
                شروع کنید و<br>
                اولین اتاق  خود را بسازید
            @else
                اتاق  دلخواه خود را
                <br>
                الان ایجاد کنید
            @endif
        </h3>
        <!--end::Text-->

        <!--begin::Action-->
        <div class="text-center pt-7">


            @if(count($licences) > 0)
                <a href="#" class="btn btn-primary fw-bolder fs-6 px-7 py-3" data-bs-toggle="modal"
                   data-bs-target="#kt_modal_create_app">ساخت اتاق </a>
            @endif
            <br/>
        </div>
        <!--end::Action-->
    @endif




    {{-- Stop trying to control. --}}

    <!--begin::Modal - Create App-->
    <div class="modal fade" id="kt_modal_create_app" tabindex="-1" aria-hidden="true" wire:ignore.self>
        <!--begin::Modal dialog-->
        <div class="modal-dialog modal-dialog-centered mw-900px" wire:ignore.self>
            <!--begin::Modal content-->
            <div class="modal-content">
                <!--begin::Modal header-->
                <div class="modal-header">
                    <!--begin::Modal title-->
                    <h2>ساخت اتاق جدید</h2>
                    <!--end::Modal title-->

                    <!--begin::Close-->
                    <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                        <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
                        <span class="svg-icon svg-icon-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24"
                                 height="24" viewBox="0 0 24 24" fill="none">
                                <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1"
                                      transform="rotate(-45 6 17.3137)"
                                      fill="black"></rect>
                                <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)"
                                      fill="black"></rect>
                                </svg>
                        </span>
                        <!--end::Svg Icon-->
                    </div>
                    <!--end::Close-->
                </div>
                <!--end::Modal header-->

                <!--begin::Modal body-->
                <div class="modal-body py-lg-10 px-lg-10">
                    <!--begin::Stepper-->
                    <div class="stepper stepper-pills stepper-column d-flex flex-column flex-xl-row flex-row-fluid"
                         id="kt_modal_create_app_stepper" data-kt-stepper="true" wire:ignore.self>
                        <!--begin::Aside-->
                        <div
                            class="d-flex justify-content-center justify-content-xl-start flex-row-auto w-100 w-xl-300px">
                            <!--begin::Nav-->
                            <div class="stepper-nav ps-lg-10 " wire:ignore>
                                <!--begin::Step 1-->
                                <div class="stepper-item current" data-kt-stepper-element="nav">
                                    <!--begin::Line-->
                                    <div class="stepper-line w-40px"></div>
                                    <!--end::Line-->

                                    <!--begin::Icon-->
                                    <div class="stepper-icon w-40px h-40px">
                                        <i class="stepper-check fas fa-check"></i>
                                        <span class="stepper-number">1</span>
                                    </div>
                                    <!--end::Icon-->

                                    <!--begin::Label-->
                                    <div class="stepper-label">
                                        <h3 class="stepper-title">
                                            نوع اتاق
                                        </h3>

                                        <div class="stepper-desc">
                                            اتاق خود را انتخاب کنید
                                        </div>
                                    </div>
                                    <!--end::Label-->
                                </div>
                                <!--end::Step 1-->

                                <!--begin::Step 2-->
                                <div class="stepper-item" data-kt-stepper-element="nav">
                                    <!--begin::Line-->
                                    <div class="stepper-line w-40px"></div>
                                    <!--end::Line-->

                                    <!--begin::Icon-->
                                    <div class="stepper-icon w-40px h-40px">
                                        <i class="stepper-check fas fa-check"></i>
                                        <span class="stepper-number">2</span>
                                    </div>
                                    <!--begin::Icon-->

                                    <!--begin::Label-->
                                    <div class="stepper-label">
                                        <h3 class="stepper-title">
                                            لایسنس
                                        </h3>

                                        <div class="stepper-desc">
                                            لایسنس دلخواه شما
                                        </div>
                                    </div>
                                    <!--begin::Label-->
                                </div>
                                <!--end::Step 2-->

                                <!--begin::Step 3-->
                                <div class="stepper-item" data-kt-stepper-element="nav">
                                    <!--begin::Line-->
                                    <div class="stepper-line w-40px"></div>
                                    <!--end::Line-->

                                    <!--begin::Icon-->
                                    <div class="stepper-icon w-40px h-40px">
                                        <i class="stepper-check fas fa-check"></i>
                                        <span class="stepper-number">3</span>
                                    </div>
                                    <!--end::Icon-->

                                    <!--begin::Label-->
                                    <div class="stepper-label">
                                        <h3 class="stepper-title">
                                            نمایش
                                        </h3>

                                        <div class="stepper-desc">
                                            تنظبمات صفحه عمومی
                                        </div>
                                    </div>
                                    <!--end::Label-->
                                </div>
                                <!--end::Step 3-->

                                <!--begin::Step 3-->
                                <div class="stepper-item" data-kt-stepper-element="nav">
                                    <!--begin::Line-->
                                    <div class="stepper-line w-40px"></div>
                                    <!--end::Line-->

                                    <!--begin::Icon-->
                                    <div class="stepper-icon w-40px h-40px">
                                        <i class="stepper-check fas fa-check"></i>
                                        <span class="stepper-number">4</span>
                                    </div>
                                    <!--end::Icon-->

                                    <!--begin::Label-->
                                    <div class="stepper-label">
                                        <h3 class="stepper-title">
                                            دسترسی
                                        </h3>

                                        <div class="stepper-desc">
                                            تنظبمات ورود
                                        </div>
                                    </div>
                                    <!--end::Label-->
                                </div>
                                <!--end::Step 3-->

                                <!--begin::Step 4-->
                                <div class="stepper-item" data-kt-stepper-element="nav">
                                    <!--begin::Line-->
                                    <div class="stepper-line w-40px"></div>
                                    <!--end::Line-->

                                    <!--begin::Icon-->
                                    <div class="stepper-icon w-40px h-40px">
                                        <i class="stepper-check fas fa-check"></i>
                                        <span class="stepper-number">5</span>
                                    </div>
                                    <!--end::Icon-->

                                    <!--begin::Label-->
                                    <div class="stepper-label stepper-item-5">
                                        <h3 class="stepper-title">
                                            زمان بندی
                                        </h3>

                                        <div class="stepper-desc">
                                            تنظیمات
                                        </div>
                                    </div>
                                    <!--end::Label-->
                                </div>
                                <!--end::Step 4-->

                                <!--begin::Step 5-->
                                <div class="stepper-item" data-kt-stepper-element="nav">
                                    <!--begin::Line-->
                                    <div class="stepper-line w-40px"></div>
                                    <!--end::Line-->

                                    <!--begin::Icon-->
                                    <div class="stepper-icon w-40px h-40px">
                                        <i class="stepper-check fas fa-check"></i>
                                        <span class="stepper-number">6</span>
                                    </div>
                                    <!--end::Icon-->

                                    <!--begin::Label-->
                                    <div class="stepper-label">
                                        <h3 class="stepper-title">
                                            ساخت
                                        </h3>

                                        <div class="stepper-desc">
                                            ساخت و انتشاراتاق
                                        </div>
                                    </div>
                                    <!--end::Label-->
                                </div>
                                <!--end::Step 5-->
                            </div>
                            <!--end::Nav-->
                        </div>
                        <!--begin::Aside-->

                        <!--begin::Content-->
                        <div class="flex-row-fluid py-lg-5 px-lg-5 " wire:ignore.self>
                            <!--begin::Form-->
                            <form class="form fv-plugins-bootstrap5 fv-plugins-framework" novalidate="novalidate"
                                  id="kt_modal_create_app_form" wire:ignore.self>
                                <!--begin::Step 1-->
                                <div class="current" data-kt-stepper-element="content"
                                     wire:ignore.self>
                                    <div class="w-100">
                                        <!--begin::Input group-->
                                        <div class="fv-row mb-10 fv-plugins-icon-container">
                                            <!--begin::Label-->
                                            <label class="d-flex align-items-center fs-5 fw-bold mb-2">
                                                <span class="required">عنوان اتاق </span>
                                                <i class="fas fa-exclamation-circle ms-2 fs-7"
                                                   data-bs-toggle="tooltip" title=""
                                                   data-bs-original-title="یک عنوان دلخواه وارد کنید"
                                                   aria-label="یک عنوان دلخواه وارد کنید"></i>
                                            </label>
                                            <!--end::Label-->

                                            <!--begin::Input-->
                                            <input type="text"
                                                   wire:model.blur="data.title"
                                                   class="form-control form-control-lg form-control-solid"
                                                   name="name" placeholder="مثلا جلسه مدیر محصول" value="">
                                            <!--end::Input-->
                                            <div class="fv-plugins-message-container invalid-feedback"></div>
                                        </div>
                                        <!--end::Input group-->

                                        <!--begin::Input group-->
                                        <div class="fv-row">
                                            <!--begin::Label-->
                                            <label class="d-flex align-items-center fs-5 fw-bold mb-4">


                                                <span class="required">نوع اتاق </span>
                                                <i class="fas fa-exclamation-circle ms-2 fs-7"
                                                   data-bs-toggle="tooltip" title=""
                                                   data-bs-original-title="این تنظیمات را بعدا هر زمان میتوانید تغییر دهید"
                                                   aria-label="این تنظیمات را بعدا هر زمان میتوانید تغییر دهید"></i>
                                            </label>
                                            <!--end::Label-->

                                            <!--begin:Options-->
                                            <div class="fv-row fv-plugins-icon-container">
                                                <!--begin:Option-->
                                                <label class="d-flex flex-stack mb-5 cursor-pointer">
                                                    <!--begin:Label-->
                                                    <span class="d-flex align-items-center me-2">
                                                        <!--begin:Icon-->
                                                        <span class="symbol symbol-50px me-6">
                                                            <span class="symbol-label bg-light-primary">
                                                                <!--begin::Svg Icon | path: icons/duotune/maps/map004.svg-->
                                                                <span class="svg-icon svg-icon-1 svg-icon-primary"><svg
                                                                        xmlns="http://www.w3.org/2000/svg" width="24"
                                                                        height="24"
                                                                        viewBox="0 0 24 24" fill="none">
                                                                <path opacity="0.3"
                                                                      d="M18.4 5.59998C21.9 9.09998 21.9 14.8 18.4 18.3C14.9 21.8 9.2 21.8 5.7 18.3L18.4 5.59998Z"
                                                                      fill="black"></path>
                                                                <path
                                                                    d="M12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2ZM19.9 11H13V8.8999C14.9 8.6999 16.7 8.00005 18.1 6.80005C19.1 8.00005 19.7 9.4 19.9 11ZM11 19.8999C9.7 19.6999 8.39999 19.2 7.39999 18.5C8.49999 17.7 9.7 17.2001 11 17.1001V19.8999ZM5.89999 6.90002C7.39999 8.10002 9.2 8.8 11 9V11.1001H4.10001C4.30001 9.4001 4.89999 8.00002 5.89999 6.90002ZM7.39999 5.5C8.49999 4.7 9.7 4.19998 11 4.09998V7C9.7 6.8 8.39999 6.3 7.39999 5.5ZM13 17.1001C14.3 17.3001 15.6 17.8 16.6 18.5C15.5 19.3 14.3 19.7999 13 19.8999V17.1001ZM13 4.09998C14.3 4.29998 15.6 4.8 16.6 5.5C15.5 6.3 14.3 6.80002 13 6.90002V4.09998ZM4.10001 13H11V15.1001C9.1 15.3001 7.29999 16 5.89999 17.2C4.89999 16 4.30001 14.6 4.10001 13ZM18.1 17.1001C16.6 15.9001 14.8 15.2 13 15V12.8999H19.9C19.7 14.5999 19.1 16.0001 18.1 17.1001Z"
                                                                    fill="black"></path>
                                                                </svg></span>
                                                                <!--end::Svg Icon-->
                                                            </span>
                                                        </span>
                                                        <!--end:Icon-->

                                                        <!--begin:Info-->
                                                        <span class="d-flex flex-column text-align-right">
                                                            <span class="fw-bolder fs-6">رویداد معمولی</span>

                                                            <span class="fs-7 text-muted">یک اتاق  برای رویداد های بدون برنامه قبلی و قابل استفاده در هر زمان</span>
                                                        </span>
                                                        <!--end:Info-->
                                                    </span>
                                                    <!--end:Label-->

                                                    <!--begin:Input-->
                                                    <span class="form-check form-check-custom form-check-solid">
                                                        <input class="form-check-input" wire:model="data.kind"
                                                               type="radio" value="NORMAL">
                                                    </span>
                                                    <!--end:Input-->
                                                </label>
                                                <!--end::Option-->

                                                <!--begin:Option-->
                                                <label class="d-flex flex-stack mb-5 cursor-pointer">
                                                    <!--begin:Label-->
                                                    <span class="d-flex align-items-center me-2">
                        <!--begin:Icon-->
                        <span class="symbol symbol-50px me-6">
                            <span class="symbol-label bg-light-danger  ">
                                <!--begin::Svg Icon | path: icons/duotune/general/gen024.svg-->
<span class="svg-icon svg-icon-1 svg-icon-danger"><svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                                                       viewBox="0 0 24 24">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
     <rect x="5" y="5" width="5" height="5" rx="1" fill="#000000"></rect>
        <rect x="14" y="5" width="5" height="5" rx="1" fill="#000000" opacity="0.3"></rect>
        <rect x="5" y="14" width="5" height="5" rx="1" fill="#000000" opacity="0.3"></rect>
        <rect x="14" y="14" width="5" height="5" rx="1" fill="#000000" opacity="0.3"></rect>
    </g>
</svg></span>
                                <!--end::Svg Icon-->                            </span>
                        </span>
                                                        <!--end:Icon-->

                                                        <!--begin:Info-->
                        <span class="d-flex flex-column text-align-right">
                            <span class="fw-bolder fs-6">رویداد عمومی</span>

                            <span class="fs-7 text-muted"> اتاق  دارای صفحه عمومی و نمایش محتوای ذخیره شده به استفاده کنندگان</span>
                        </span>
                                                        <!--end:Info-->
                    </span>
                                                    <!--end:Label-->

                                                    <!--begin:Input-->
                                                    <span class="form-check form-check-custom form-check-solid">
                                                        <input class="form-check-input" wire:model="data.kind"
                                                               type="radio" value="PUBLIC">
                                                    </span>
                                                    <!--end:Input-->
                                                </label>
                                                <!--end::Option-->

                                                <!--begin:Option-->
                                                <label class="d-flex flex-stack cursor-pointer">
                                                    <!--begin:Label-->
                                                    <span class="d-flex align-items-center me-2">
                        <!--begin:Icon-->
                        <span class="symbol symbol-50px me-6">
                            <span class="symbol-label bg-light-success">
                                <!--begin::Svg Icon | path: icons/duotune/general/gen013.svg-->
<span class="svg-icon svg-icon-1 svg-icon-success"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none">
<path opacity="0.3"
      d="M20.9 12.9C20.3 12.9 19.9 12.5 19.9 11.9C19.9 11.3 20.3 10.9 20.9 10.9H21.8C21.3 6.2 17.6 2.4 12.9 2V2.9C12.9 3.5 12.5 3.9 11.9 3.9C11.3 3.9 10.9 3.5 10.9 2.9V2C6.19999 2.5 2.4 6.2 2 10.9H2.89999C3.49999 10.9 3.89999 11.3 3.89999 11.9C3.89999 12.5 3.49999 12.9 2.89999 12.9H2C2.5 17.6 6.19999 21.4 10.9 21.8V20.9C10.9 20.3 11.3 19.9 11.9 19.9C12.5 19.9 12.9 20.3 12.9 20.9V21.8C17.6 21.3 21.4 17.6 21.8 12.9H20.9Z"
      fill="black"></path>
<path
    d="M16.9 10.9H13.6C13.4 10.6 13.2 10.4 12.9 10.2V5.90002C12.9 5.30002 12.5 4.90002 11.9 4.90002C11.3 4.90002 10.9 5.30002 10.9 5.90002V10.2C10.6 10.4 10.4 10.6 10.2 10.9H9.89999C9.29999 10.9 8.89999 11.3 8.89999 11.9C8.89999 12.5 9.29999 12.9 9.89999 12.9H10.2C10.4 13.2 10.6 13.4 10.9 13.6V13.9C10.9 14.5 11.3 14.9 11.9 14.9C12.5 14.9 12.9 14.5 12.9 13.9V13.6C13.2 13.4 13.4 13.2 13.6 12.9H16.9C17.5 12.9 17.9 12.5 17.9 11.9C17.9 11.3 17.5 10.9 16.9 10.9Z"
    fill="black"></path>
</svg></span>
                                <!--end::Svg Icon-->                            </span>
                        </span>
                                                        <!--end:Icon-->

                                                        <!--begin:Info-->
                        <span class="d-flex flex-column text-align-right">
                            <span class="fw-bolder fs-6">رویداد زمان بندی شده</span>

                            <span class="fs-7 text-muted">رویداد های زمان بندی شده با قابلیت اجرا بصورت متناوب</span>
                        </span>
                                                        <!--end:Info-->
                    </span>
                                                    <!--end:Label-->

                                                    <!--begin:Input-->
                                                    <span class="form-check form-check-custom form-check-solid">

                                                        <input class="form-check-input" wire:model="data.kind"
                                                               type="radio" value="RECURRENCE">
                                                    </span>
                                                    <!--end:Input-->
                                                </label>
                                                <!--end::Option-->
                                                <div class="fv-plugins-message-container invalid-feedback"></div>
                                            </div>
                                            <!--end:Options-->
                                        </div>
                                        <!--end::Input group-->
                                    </div>
                                </div>
                                <!--end::Step 1-->
                                <!--begin::Step 2-->
                                <div wire:ignore.self data-kt-stepper-element="content">
                                    <div class="w-100">
                                        <!--begin::Input group-->
                                        <div class="fv-row fv-plugins-icon-container">
                                            <!--begin::Label-->
                                            <label class="d-flex align-items-center fs-5 fw-bold mb-4">
                                                <span class="required">انتخاب لایسنس</span>
                                                <i class="fas fa-exclamation-circle ms-2 fs-7"
                                                   data-bs-toggle="tooltip" title=""
                                                   data-bs-original-title="با ساخت اتاق در یک لایسنس، تا زمان فعال بودن لایسنس به اتاق دسترسی خواهید داشت"
                                                   aria-label="با ساخت اتاق در یک لایسنس، تا زمان فعال بودن لایسنس به اتاق دسترسی خواهید داشت"></i>
                                            </label>


                                            <!--begin::Radio group-->
                                            <div data-kt-buttons="true" class="control-height-wizard">
                                                @forelse($licences as $licence)
                                                    @if(!$licence->isActive)
                                                        @continue
                                                    @endif
                                                    <!--begin::Radio button-->
                                                    <label
                                                        class="btn btn-outline btn-outline-dashed d-flex flex-stack text-start p-6 mb-5">
                                                        <!--end::Description-->
                                                        <div class="d-flex align-items-center me-2">
                                                            <!--begin::Radio-->
                                                            <div
                                                                class="form-check form-check-custom form-check-solid form-check-primary me-6">
                                                                <input class="form-check-input" type="radio" checked=""
                                                                       wire:model.blur="data.licence"
                                                                       value="{{$licence->id}}">
                                                            </div>
                                                            <!--end::Radio-->

                                                            <!--begin::Info-->
                                                            <div class="flex-grow-1">
                                                                <h2 class="d-flex align-items-center fs-3 fw-bolder flex-wrap">
                                                                    {{$licence->detail->title}}
                                                                </h2>
                                                                <div class="fw-bold opacity-50">
                                                                    امکان ساخت {{$licence->detail->max_room_count}} اتاق
                                                                    - نگه
                                                                    داری {{$licence->detail->max_keep_file}} روزه فایل
                                                                    ها
                                                                </div>
                                                            </div>
                                                            <!--end::Info-->
                                                        </div>
                                                        <!--end::Description-->

                                                        <!--begin::Price-->
                                                        <div class="ms-5">
                                                            <span class="mb-2"></span>
                                                            <span class="fs-2x fw-bolder">
                                                                    {{$licence->leftRooms}}
                                                                </span>
                                                            <span class="fs-7 opacity-50"> <br/>
                                                                <span
                                                                    data-kt-element="period">ظرفیت آزاد اتاق</span>
                                                            </span>


                                                        </div>
                                                        <!--end::Price-->
                                                    </label>
                                                    <!--end::Radio button-->
                                                @empty
                                                    لایسنس فعالی ندارید. ابتدا لایسنس تهیه کنید
                                                @endforelse

                                            </div>
                                            <!--end::Radio group-->


                                            <div class="fv-plugins-message-container invalid-feedback"></div>
                                        </div>
                                        <!--end::Input group-->
                                    </div>
                                </div>
                                <!--end::Step 2-->
                                <!--begin::Step 3-->
                                <div wire:ignore.self data-kt-stepper-element="content">
                                    <div class="w-100">
                                        <!--begin::Input group-->
                                        <div class="fv-row mb-10 fv-plugins-icon-container">
                                            <!--begin::Label-->
                                            <label class="d-flex align-items-center fs-5 fw-bold mb-4">
                                                <span class="required">آدرس صفحه عمومی</span>
                                            </label>
                                            <div class="fs-7 fw-bold text-muted mb-3">
                                                این لینک ثابت و اختصاصی اتاق شما خواهد بود.
                                            </div>
                                            <!--end::Label-->

                                            <!--begin::Input group-->

                                            <div class="input-group mb-5 direction-ltr">
                                                <span class="input-group-text"
                                                      id="basic-addon3">{{config('app.url')}}show/</span>
                                                <input type="text" class="form-control" id="basic-url"
                                                       aria-describedby="basic-addon3"
                                                       wire:model.blur="data.public_page_slug" value="lc1"
                                                />
                                                @error('data.public_page_slug')
                                                <div
                                                    class="fv-plugins-message-container invalid-feedback direction-rtl">
                                                    <div data-field="basic-addon3" class="align-right">
                                                        {{ $message }}
                                                    </div>
                                                </div>
                                                @enderror

                                            </div>
                                            <!--end::Input group-->

                                            {{--                                            <!--begin::Input-->--}}
                                            {{--                                            <input type="text"--}}
                                            {{--                                                   class="form-control form-control-lg form-control-solid"--}}
                                            {{--                                                   name="dbname" placeholder="" value="master_db">--}}
                                            <!--end::Input-->
                                            <div class="fv-plugins-message-container invalid-feedback"></div>
                                        </div>
                                        <!--end::Input group-->

                                        <!--begin::Input group-->
                                        <div class="fv-row fv-plugins-icon-container">
                                            <!--begin::Label-->
                                            <label class="d-flex align-items-center fs-5 fw-bold mb-4">
                                                <span class="required">تنظیمات صفحه عمومی</span>

                                                <i class="fas fa-exclamation-circle ms-2 fs-7"
                                                   data-bs-toggle="tooltip" title=""
                                                   data-bs-original-title="یک صفحه مخصوص وبینار شما با امکان نمایش اطلاعات وبینار"
                                                   aria-label="یک صفحه مخصوص وبینار شما با امکان نمایش اطلاعات وبینار"></i>
                                            </label>
                                            <div class="separator-dashed"></div>
                                            <!--end::Label-->

                                            <div class="d-flex flex-stack text-align-right mt-5">
                                                <!--begin::Label-->
                                                <div class="me-5">
                                                    <label class="fs-6 fw-bold form-label">نمایش صفحه عمومی به
                                                        دیگران؟</label>
                                                    <div class="fs-7 fw-bold text-muted">
                                                        با این گزینه کاربران راحت تر میتوانند وارد میتینگ شود و دچار
                                                        مشکلات تحریم نخواهند شد.
                                                    </div>
                                                </div>
                                                <!--end::Label-->

                                                <!--begin::Switch-->
                                                <label
                                                    class="form-check form-switch form-check-custom form-check-solid">
                                                    <input class="form-check-input" type="checkbox"
                                                           wire:model.live="data.public_page"/>
                                                    <span class="form-check-label fw-bold text-muted">

                                                    </span>
                                                </label>
                                                <!--end::Switch-->
                                            </div>


                                            <div class="d-flex flex-stack text-align-right mt-5">
                                                <!--begin::Label-->
                                                <div class="me-5">
                                                    <label class="fs-6 fw-bold form-label">امکان دانلود ویدئو های ضبط
                                                        شده</label>
                                                    <div class="fs-7 fw-bold text-muted">
                                                        اگر روشن باشد امکان دانلود ویدئو های اتاق در صفحه عمومی وجود
                                                        خواهد داشت
                                                    </div>
                                                </div>
                                                <!--end::Label-->

                                                <!--begin::Switch-->
                                                <label
                                                    class="form-check form-switch form-check-custom form-check-solid">
                                                    <input class="form-check-input" type="checkbox" value="1"
                                                           wire:model.live="data.download_access">
                                                    <span class="form-check-label fw-bold text-muted">

                                                    </span>
                                                </label>
                                                <!--end::Switch-->
                                            </div>


                                            <div class="d-flex flex-stack text-align-right mt-5">
                                                <!--begin::Label-->
                                                <div class="me-5">
                                                    <label class="fs-6 fw-bold form-label">امکان دانلود چت
                                                        کاربران</label>
                                                    <div class="fs-7 fw-bold text-muted">
                                                        اگر روشن باشد امکان دانلود تمامی چت های حین رویداد وجود خواهد
                                                        داشت
                                                    </div>
                                                </div>
                                                <!--end::Label-->

                                                <!--begin::Switch-->
                                                <label
                                                    class="form-check form-switch form-check-custom form-check-solid">
                                                    <input class="form-check-input" type="checkbox" value="1"
                                                           wire:model.live="data.chat_download_access">
                                                    <span class="form-check-label fw-bold text-muted">

                                                    </span>
                                                </label>
                                                <!--end::Switch-->
                                            </div>


                                            <div class="fv-plugins-message-container invalid-feedback"></div>
                                        </div>
                                        <!--end::Input group-->
                                    </div>
                                </div>
                                <!--end::Step 3-->
                                <!--begin::Step 4-->
                                <div wire:ignore.self data-kt-stepper-action="step" data-kt-stepper-element="content">
                                    <div class="w-100">

                                        <!--begin::Input group-->
                                        <div class="fv-row mb-10 fv-plugins-icon-container">
                                            <!--begin::Label-->
                                            <label class="d-flex align-items-center fs-5 fw-bold mb-4">
                                                <span class="required">ایمیل مدیر اصلی اتاق</span>
                                            </label>
                                            <div class="fs-7 fw-bold text-muted mb-2">
                                                ایمیل Gmail مدیر اصلی اتاق وارد شود.
                                            </div>
                                            <!--end::Label-->
                                            <div class="input-group mb-5 direction-ltr">
                                                <input type="text" class="form-control" id="basic-url"
                                                       aria-describedby="basic-addon3"
                                                       wire:model.blur="data.main_room_admin" value="lc1"
                                                />
                                                @error('data.main_room_admin')
                                                <div
                                                    class="fv-plugins-message-container invalid-feedback direction-rtl">
                                                    <div data-field="basic-addon3" class="align-right">
                                                        {{ $message }}
                                                    </div>
                                                </div>
                                                @enderror

                                            </div>


                                            <!--begin::Input group-->
                                            <div class="rounded border p-5 mb-5">
                                                <div class="d-flex flex-stack">
                                                    <!--begin::Label-->
                                                    <div class="me-5 text-align-right direction-rtl">
                                                        <label class="fs-6 fw-bold form-label">
                                                            یکی از مدیران باید قبل از سایرین وارد شود؟
                                                        </label>
                                                        <div class="fs-7 fw-bold text-muted">
                                                            تا زمان وارد شدن یکی از مدیران، سایرین منتظر خواهند ماند
                                                        </div>
                                                    </div>
                                                    <!--end::Label-->

                                                    <!--begin::Switch-->
                                                    <label
                                                        class="form-check form-switch form-check-custom form-check-solid">
                                                        <input class="form-check-input" type="checkbox"
                                                               wire:model.live="data.host_join_type" value="1"
                                                        />

                                                    </label>
                                                    <!--end::Switch-->
                                                </div>
                                            </div>
                                            <!--end::Input group-->
                                        </div>

                                        <!--begin::Input group-->
                                        <div class="fv-row fv-plugins-icon-container" wire:ignore>
                                            <!--begin::Label-->
                                            <label class="d-flex align-items-center fs-5 fw-bold mb-4">
                                                <span class="required">نحوه ورود کاربران</span>
                                                <i class="fas fa-exclamation-circle ms-2 fs-7"
                                                   data-bs-toggle="tooltip" title=""
                                                   data-bs-original-title="چگونه کاربران بتوانند وارد شوند"
                                                   aria-label="چگونه کاربران بتوانند وارد شوند"></i>
                                            </label>
                                            <!--end::Label-->

                                            <!--begin:Option-->
                                            <label class="d-flex flex-stack cursor-pointer mb-5">
                                                <!--begin:Label-->
                                                <span class="d-flex align-items-center me-2">
                                                    <!--begin:Icon-->
                                                    <span class="symbol symbol-50px me-6">
                                                        <span class="symbol-label bg-light-warning">
                                                            <!--begin::Svg Icon | path: assets/media/icons/duotune/technology/teh008.svg-->
                                                                <span
                                                                    class="svg-icon svg-icon-warning   text-warning svg-icon-2hx"><svg
                                                                        xmlns="http://www.w3.org/2000/svg" width="24"
                                                                        height="24" viewBox="0 0 24 24" fill="none">
                                                                <path opacity="0.3"
                                                                      d="M11 6.5C11 9 9 11 6.5 11C4 11 2 9 2 6.5C2 4 4 2 6.5 2C9 2 11 4 11 6.5ZM17.5 2C15 2 13 4 13 6.5C13 9 15 11 17.5 11C20 11 22 9 22 6.5C22 4 20 2 17.5 2ZM6.5 13C4 13 2 15 2 17.5C2 20 4 22 6.5 22C9 22 11 20 11 17.5C11 15 9 13 6.5 13ZM17.5 13C15 13 13 15 13 17.5C13 20 15 22 17.5 22C20 22 22 20 22 17.5C22 15 20 13 17.5 13Z"
                                                                      fill="black"/>
                                                                <path
                                                                    d="M17.5 16C17.5 16 17.4 16 17.5 16L16.7 15.3C16.1 14.7 15.7 13.9 15.6 13.1C15.5 12.4 15.5 11.6 15.6 10.8C15.7 9.99999 16.1 9.19998 16.7 8.59998L17.4 7.90002H17.5C18.3 7.90002 19 7.20002 19 6.40002C19 5.60002 18.3 4.90002 17.5 4.90002C16.7 4.90002 16 5.60002 16 6.40002V6.5L15.3 7.20001C14.7 7.80001 13.9 8.19999 13.1 8.29999C12.4 8.39999 11.6 8.39999 10.8 8.29999C9.99999 8.19999 9.20001 7.80001 8.60001 7.20001L7.89999 6.5V6.40002C7.89999 5.60002 7.19999 4.90002 6.39999 4.90002C5.59999 4.90002 4.89999 5.60002 4.89999 6.40002C4.89999 7.20002 5.59999 7.90002 6.39999 7.90002H6.5L7.20001 8.59998C7.80001 9.19998 8.19999 9.99999 8.29999 10.8C8.39999 11.5 8.39999 12.3 8.29999 13.1C8.19999 13.9 7.80001 14.7 7.20001 15.3L6.5 16H6.39999C5.59999 16 4.89999 16.7 4.89999 17.5C4.89999 18.3 5.59999 19 6.39999 19C7.19999 19 7.89999 18.3 7.89999 17.5V17.4L8.60001 16.7C9.20001 16.1 9.99999 15.7 10.8 15.6C11.5 15.5 12.3 15.5 13.1 15.6C13.9 15.7 14.7 16.1 15.3 16.7L16 17.4V17.5C16 18.3 16.7 19 17.5 19C18.3 19 19 18.3 19 17.5C19 16.7 18.3 16 17.5 16Z"
                                                                    fill="black"/>
                                                                </svg></span>
                                                            <!--end::Svg Icon-->
                                                        </span>
                                                    </span>
                                                    <!--end:Icon-->

                                                    <!--begin:Info-->
                                                    <span class="d-flex flex-column text-align-right">
                                                        <span class="fw-bolder fs-6">باز</span>
                                                        <span class="fs-7 text-muted">بدون هیچگونه تاییدی از سمت مدیر بتوانند وارد شوند</span>
                                                    </span>
                                                    <!--end:Info-->
                                                </span>
                                                <!--end:Label-->

                                                <!--begin:Input-->
                                                <span class="form-check form-check-custom form-check-solid">
                                                    <input class="form-check-input" type="radio" checked="checked"
                                                           wire:model.blur="data.access_type"
                                                           name="data.access_type"
                                                           value="{{\App\Enums\MeetingAccessTypeEnum::OPEN->value}}">
                                                </span>
                                                <!--end:Input-->
                                            </label>
                                            <!--end::Option-->

                                            <!--begin:Option-->
                                            <label class="d-flex flex-stack cursor-pointer mb-5">
                                                <!--begin:Label-->
                                                <span class="d-flex align-items-center me-2">
                                                    <!--begin:Icon-->
                                                    <span class="symbol symbol-50px me-6">
                                                        <span class="symbol-label bg-light-success">
                                                            <!--begin::Svg Icon | path: assets/media/icons/duotune/communication/com013.svg-->
                                                                <span class="svg-icon svg-icon-success svg-icon-2hx"><svg
                                                                        xmlns="http://www.w3.org/2000/svg" width="24"
                                                                        height="24" viewBox="0 0 24 24" fill="none">
                                                                <path
                                                                    d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z"
                                                                    fill="black"/>
                                                                <rect opacity="0.3" x="8" y="3" width="8" height="8"
                                                                      rx="4" fill="black"/>
                                                                </svg></span>
                                                            <!--end::Svg Icon-->
                                                        </span>
                                                    </span>
                                                    <!--end:Icon-->

                                                    <!--begin:Info-->
                                                    <span class="d-flex flex-column text-align-right">
                                                        <span class="fw-bolder fs-6">دعوت شده ها</span>
                                                        <span
                                                            class="fs-7 text-muted">دعوت شده ها بدون تایید وارد شوند</span>
                                                    </span>
                                                    <!--end:Info-->
                                                </span>
                                                <!--end:Label-->

                                                <!--begin:Input-->
                                                <span class="form-check form-check-custom form-check-solid">
                                                    <input class="form-check-input" type="radio"
                                                           name="data.access_type"
                                                           wire:model.blur="data.access_type"
                                                           value="{{\App\Enums\MeetingAccessTypeEnum::TRUSTED->value}}">
                                                </span>
                                                <!--end:Input-->
                                            </label>
                                            <!--end::Option-->
                                            <div class="fv-plugins-message-container invalid-feedback"></div>
                                        </div>
                                        <!--end::Input group-->

                                    </div>
                                </div>
                                <!--end::Step 4-->
                                <!--begin::Step 4-->
                                <div wire:ignore.self data-kt-stepper-action="step" data-kt-stepper-element="content">
                                    <div class="w-100">
                                        <div class="row">

                                            <h5 class="fw-bold mb-6">زمان بندی رویداد</h5>

                                        </div>


                                        <div class="mb-8 row">
                                            <input type="hidden" wire:model.live="data.start_date"
                                                   id="settings_start_date">
                                            <div class="row">
                                                <label
                                                    class="col-xl-3 col-lg-3 col-form-label fw-bold text-start text-lg-end">
                                                    تاریخ شروع
                                                </label>
                                                <div class="col-lg-9 col-xl-9">
                                                    <div class="row">

                                                        <div class="col-md-6">
                                                            <div
                                                                class="d-flex flex-row align-items-center justify-content-between">
                                                                <input
                                                                    wire:model.blur="data.start_date_minute"
                                                                    class="form-control form-control-solid text-align-center"
                                                                    type="number" max="59"
                                                                    style="-webkit-appearance: none;">
                                                                <span
                                                                    style=" font-size: 22px; margin: 0px 13px; ">:</span>
                                                                <input
                                                                    wire:model.blur="data.start_date_hour"
                                                                    class="form-control form-control-solid text-align-center"
                                                                    type="number" max="24"
                                                                    style="-webkit-appearance: none; -moz-appearance: textfield;">
                                                            </div>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <input type="text"
                                                                   wire:ignore.self
                                                                   class="direction-ltr form-control form-control-lg form-control-solid"
                                                                   onchange="changeLivewireModel('settings_start_date',$(this).val())"
                                                                   id="start-date-picker"
                                                                   value="{{$data->start_date}}"
                                                                   wire:loading.class="disabled"
                                                            />
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">

                                                    </div>
                                                </div>

                                                <label
                                                    class="col-xl-3 col-lg-3 col-form-label fw-bold text-start text-lg-end">
                                                    طول هر جلسه
                                                </label>
                                                <div class="col-lg-9 col-xl-9">
                                                    <div class="row">
                                                        <div class="col-md-12  position-relative">
                                                            <div
                                                                class="input-group input-group-solid mb-5 direction-ltr">
                                                                <span class="input-group-text"
                                                                      id="basic-addon2">دقیقه</span>
                                                                <input
                                                                    wire:model.live="data.meeting_duration"
                                                                    class="form-control form-control-solid text-align-center"
                                                                    type="number"
                                                                    style="-webkit-appearance: none; -moz-appearance: textfield;">


                                                            </div>

                                                        </div>
                                                    </div>

                                                </div>
                                            </div>

                                        </div>

                                        <!--begin::Input group-->
                                        <div class="rounded border p-5 mb-5">
                                            <div class="d-flex flex-stack">
                                                <!--begin::Label-->
                                                <div class="me-5 text-align-right direction-rtl">
                                                    <label class="fs-6 fw-bold form-label">
                                                        رویداد شما بصورت متناوب تکرار میشود؟
                                                    </label>
                                                    <div class="fs-7 fw-bold text-muted">
                                                        این تنظیمات را در آینده نیز میتوانید تغییر دهید.
                                                    </div>
                                                </div>
                                                <!--end::Label-->

                                                <!--begin::Switch-->
                                                <label
                                                    class="form-check form-switch form-check-custom form-check-solid">
                                                    <input class="form-check-input" type="checkbox"
                                                           wire:model.live="data.repeat_status" value="1"
                                                    />

                                                </label>
                                                <!--end::Switch-->
                                            </div>
                                        </div>
                                        <!--end::Input group-->

                                        <div id="check_repeat_status"
                                             @if($data->repeat_status != '1')
                                                 class="disabled hide"
                                            @endif
                                        >

                                            <div class="mb-8 row">
                                                <label
                                                    class="col-xl-3 col-lg-3 col-form-label fw-bold text-start text-lg-end">
                                                    تکرار شود هر
                                                </label>
                                                <div class="col-lg-9 col-xl-9">
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <input type="number" value="1"
                                                                   class="form-control form-control-solid"
                                                                   wire:model.live="data.recurrence_interval"/>
                                                        </div>
                                                        <div class="col-md-8">
                                                            <input type="hidden" id="settings_recurrence_frequency"
                                                                   wire:model.live="data.recurrence_frequency">
                                                            <select type="text"
                                                                    class="form-select form-select-lg form-select-solid"
                                                                    onchange="changeLivewireModel('settings_recurrence_frequency',$(this).val())"
                                                                    data-control="select2"
                                                            >
                                                                <option value="DAILY"
                                                                    {{$data->recurrence_frequency=== 'DAILY' ? "selected" : '' }}
                                                                >
                                                                    روز
                                                                </option>
                                                                <option value="WEEKLY"
                                                                    {{$data->recurrence_frequency=== 'WEEKLY' ? "selected" : '' }}
                                                                >هفته
                                                                </option>
                                                                <option value="MONTHLY"
                                                                    {{$data->recurrence_frequency=== 'MONTHLY' ? "selected" : '' }}
                                                                >ماه
                                                                </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">


                                                    </div>
                                                </div>
                                            </div>


                                            @if($data->recurrence_frequency === 'WEEKLY')
                                            <div class="mb-8 row">
                                                <label
                                                    class="col-xl-3 col-lg-3 col-form-label fw-bold text-start text-lg-end">
                                                    چه روزی از هفته؟
                                                </label>
                                                <div class="col-lg-9 col-xl-9">

                                                    <div
                                                        class="d-flex flex-wrap justify-content-between align-items-center">
                                                        <div
                                                            class="form-check form-check-custom form-check-solid mb-3 min-w-100px">
                                                            <input class="form-check-input" type="checkbox" checked=""
                                                                   id="kt_checkbox_1"
                                                                   wire:model.live="data.recurrence_days"
                                                                   value="SA">
                                                            <label class="form-check-label fw-bold text-gray-600"
                                                                   for="kt_checkbox_1">
                                                                شنبه
                                                            </label>
                                                        </div>
                                                        <div
                                                            class="form-check form-check-custom form-check-solid mb-3  min-w-100px">
                                                            <input class="form-check-input" type="checkbox"
                                                                   id="kt_checkbox_2"
                                                                   wire:model.live="data.recurrence_days"
                                                                   value="SU">
                                                            <label class="form-check-label fw-bold text-gray-600"
                                                                   for="kt_checkbox_2">
                                                                یک شنبه
                                                            </label>
                                                        </div>
                                                        <div
                                                            class="form-check form-check-custom form-check-solid mb-3 min-w-100px">
                                                            <input class="form-check-input" type="checkbox"
                                                                   id="kt_checkbox_3"
                                                                   wire:model.live="data.recurrence_days"
                                                                   value="MO">
                                                            <label class="form-check-label fw-bold text-gray-600"
                                                                   for="kt_checkbox_3">
                                                                دو شنبه
                                                            </label>
                                                        </div>
                                                        <div
                                                            class="form-check form-check-custom form-check-solid mb-3 min-w-100px">
                                                            <input class="form-check-input" type="checkbox"
                                                                   id="kt_checkbox_4"
                                                                   wire:model.live="data.recurrence_days"
                                                                   value="TU">
                                                            <label class="form-check-label fw-bold text-gray-600"
                                                                   for="kt_checkbox_4">
                                                                سه شنبه
                                                            </label>
                                                        </div>
                                                        <div
                                                            class="form-check form-check-custom form-check-solid mb-3 min-w-100px">
                                                            <input class="form-check-input" type="checkbox"
                                                                   id="kt_checkbox_5"
                                                                   wire:model.live="data.recurrence_days"
                                                                   value="WE">
                                                            <label class="form-check-label fw-bold text-gray-600"
                                                                   for="kt_checkbox_5">
                                                                جهار شنبه
                                                            </label>
                                                        </div>
                                                        <div
                                                            class="form-check form-check-custom form-check-solid mb-3 min-w-100px">
                                                            <input class="form-check-input" type="checkbox"
                                                                   id="kt_checkbox_6"
                                                                   wire:model.live="data.recurrence_days"
                                                                   value="TH">
                                                            <label class="form-check-label fw-bold text-gray-600"
                                                                   for="kt_checkbox_6">
                                                                پنج شنبه
                                                            </label>
                                                        </div>
                                                        <div
                                                            class="form-check form-check-custom form-check-solid mb-3 min-w-100px">
                                                            <input class="form-check-input" type="checkbox"
                                                                   id="kt_checkbox_7"
                                                                   wire:model.live="data.recurrence_days"
                                                                   value="FR">
                                                            <label class="form-check-label fw-bold text-gray-600"
                                                                   for="kt_checkbox_7">
                                                                جمعه
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            @endif
                                            <div class="mb-8 row">
                                                <label
                                                    class="col-xl-3 col-lg-3 col-form-label fw-bold text-start  text-lg-end">
                                                    پایان زمان تکرار تا
                                                </label>
                                                <div class="col-lg-9 col-xl-9">
                                                    <div class="form-check form-check-custom form-check-solid mb-3">
                                                        <input class="form-check-input" type="radio"
                                                               wire:model.live="data.recurrence_until_radio"

                                                               id="kt_checkbox_date4_old"
                                                               value="0">
                                                        <label class="form-check-label fw-bold text-gray-600"
                                                               for="kt_checkbox_date4">
                                                            همیشه باشد (حداکثر ۶ ماه تنظیم خواهد شد)
                                                        </label>
                                                    </div>


                                                    <input type="hidden" wire:model.live="data.recurrence_until"
                                                           id="settings_recurrence_until">

                                                    <div class="form-check form-check-custom form-check-solid mb-3">
                                                        <input class="form-check-input" type="radio"
                                                               wire:model.live="data.recurrence_until_radio"
                                                               id="kt_checkbox_data_old"
                                                               value="1">
                                                        <label class="form-check-label fw-bold text-gray-600"
                                                               for="kt_checkbox_data5">
                                                            تا
                                                        </label>
                                                        <div id="check_recurrence_until"
                                                             @if($data->recurrence_until_radio != '1')
                                                                 class="disabled"
                                                            @endif
                                                        >

                                                            <div class="ms-3" wire:ignore>
                                                                <input type="text"
                                                                       wire:ignore.self
                                                                       class="direction-ltr form-control form-control-lg form-control-solid"
                                                                       onchange="changeLivewireModel('settings_recurrence_until',$(this).val())"
                                                                       id="daterange-picker"
                                                                       value="{{now()->addMonths(3)->format('Y/m/d')}}"
                                                                       wire:loading.class="disabled"
                                                                />

                                                                @if ($errors->has('stopDate'))
                                                                    <span class="invalid-feedback d-block" role="alert">
                                                                         <strong>{{ $errors->first('stopDate') }}</strong>
                                                                    </span>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>


                                        </div>


                                    </div>
                                </div>
                                <!--end::Step 4-->
                                <!--begin::Step 5-->
                                <div wire:ignore.self data-kt-stepper-action="step" data-kt-stepper-element="content">
                                    <div class="w-100 text-center">


                                        <!--begin::Heading-->
                                        <h1 class="fw-bolder text-dark mb-3">تایید نهایی</h1>
                                        <!--end::Heading-->

                                        <div class="card">
                                            <!--begin::Card body-->
                                            <div class="card-body d-flex flex-center flex-column p-9">

                                                <!--begin::Name-->
                                                @if(isset($selectedLicence))
                                                    <table class="table table-borderless align-middle fw-bold d-none">
                                                        <tbody>
                                                        <tr>
                                                            <td class="text-gray-600 ps-0 text-align-right">لایسنس
                                                                انتخابی
                                                            </td>
                                                            <td class="text-dark pe-0  text-align-left"> {{$selectedLicence->detail->title}}</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="text-gray-600 ps-0 text-align-right">عنوان اتاق
                                                            </td>
                                                            <td class="text-dark pe-0 text-align-left">{{$data->title}}</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="text-gray-600 ps-0 text-align-right">ایمیل مدیر
                                                            </td>
                                                            <td class="text-dark pe-0 text-align-left">{{$data->main_room_admin}}
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                @endif

                                                <!--end::Name-->


                                            </div>
                                            <!--begin::Card body-->
                                        </div>
                                        <!--begin::Description-->
                                        <div class="text-muted fw-bold fs-3">

                                            <div
                                                class="alert alert-primary d-flex align-items-center text-align-right p-5 mb-10">
                                                <!--begin::Svg Icon | path: icons/duotune/general/gen048.svg-->
                                                <span class="svg-icon svg-icon-2hx svg-icon-success me-4"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24"
                                                        height="24"
                                                        viewBox="0 0 24 24" fill="none">
<path opacity="0.3"
      d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z"
      fill="black"></path>
<path
    d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z"
    fill="black"></path>
</svg></span>
                                                <!--end::Svg Icon-->
                                                <div class="d-flex flex-column">
                                                    <!--begin::Title-->
                                                    <h4 class="mb-1 text-dark">اتاق شما آمادست اما!</h4>
                                                    <!--end::Title-->
                                                    <!--begin::Content-->
                                                    <span class="fs-6">
                                                        ما نیاز داریم برخی تنظیمات مهم مرتبط با اتاق را برایتان انجام بدهیم و به کمی زمان نیاز داریم. به محض اماده شدن به شما از طریق ایمیل و یا پیامک اطلاع رسانی خواهیم کرد. این پروسه در کمتر از ۳۰ دقیقه آتی انجام خواهد شد.
                                                    </span>
                                                    <!--end::Content-->

                                                </div>
                                            </div>


                                            <div
                                                class="alert alert-success d-flex align-items-center text-align-right p-5 mb-10">
                                                <!--begin::Svg Icon | path: icons/duotune/general/gen048.svg-->
                                                <span class="svg-icon svg-icon-2hx svg-icon-primary me-4"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24"
                                                        height="24"
                                                        viewBox="0 0 24 24" fill="none">
<path opacity="0.3"
      d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z"
      fill="black"></path>
<path
    d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z"
    fill="black"></path>
</svg></span>
                                                <!--end::Svg Icon-->
                                                <div class="d-flex flex-column">
                                                    <h4 class="mb-1 text-primary">
                                                        راهنمای استفاده
                                                    </h4>
                                                    <span class="fs-6">
                                                         توجه کنید در صورتیکه نحوه کار با گوگل میت و آی روم را نمیدانید. حتما راهنمایی استفاده را مطالعه کنید. همیشه در منو اصلی سایت میتوانید به بخش <b>راهنما</b> دسترسی داشته باشید:
                                                    </span>
                                                    <a href="{{route('instruction.index')}}" target="_blank"
                                                       style=" border-color: #290f57; "
                                                       class="btn btn-outline btn-active-info m-2">مشاهده راهنمای
                                                        استفاده</a>
                                                </div>
                                            </div>


                                        </div>
                                        <!--end::Description-->

                                    </div>
                                </div>
                                <!--end::Step 5-->


                                <!--begin::Actions-->
                                <div class="d-flex flex-stack pt-10 sticky-bottom" wire:ignore>
                                    <!--begin::Wrapper-->
                                    <div class="me-2" wire:ignore>
                                        <button type="button" class="btn btn-lg btn-light-primary me-3"
                                                data-kt-stepper-action="previous" wire:ignore>

                                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                            <span class="svg-icon svg-icon-3 ms-1 me-0"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="black"></rect>
<path
    d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
    fill="black"></path>
</svg></span>
                                            <!--end::Svg Icon-->
                                            قبلی
                                        </button>
                                    </div>
                                    <!--end::Wrapper-->

                                    <!--begin::Wrapper-->
                                    <div>
                                        <button type="button" class="btn btn-lg btn-primary"
                                                data-kt-stepper-action="submit"
                                                wire:loading.class="disabled" wire:target="saveMeeting"
                                                wire:click="saveMeeting">

                                            <div wire:loading.remove wire:target="saveMeeting">ذخیره تغییرات</div>
                                            <div wire:loading wire:target="saveMeeting">
                                                در حال ذخیره سازی...
                                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                            </div>

                                        </button>

                                        <button type="button" class="btn btn-lg btn-primary"
                                                data-kt-stepper-action="next">
                                            مرحله بعد

                                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr063.svg-->
                                            <span class="svg-icon svg-icon-3 me-1"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none">
                                                    <rect opacity="0.5" x="6" y="11" width="13" height="2" rx="1"
                                                          fill="black"></rect>
                                                    <path
                                                        d="M8.56569 11.4343L12.75 7.25C13.1642 6.83579 13.1642 6.16421 12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75L5.70711 11.2929C5.31658 11.6834 5.31658 12.3166 5.70711 12.7071L11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25C13.1642 17.8358 13.1642 17.1642 12.75 16.75L8.56569 12.5657C8.25327 12.2533 8.25327 11.7467 8.56569 11.4343Z"
                                                        fill="black"></path>
                                                    </svg>
                                            </span>
                                            <!--end::Svg Icon-->
                                        </button>
                                    </div>
                                    <!--end::Wrapper-->
                                </div>
                                <!--end::Actions-->
                                <div></div>
                                <div></div>
                                <div></div>
                                <div></div>
                            </form>
                            <!--end::Form-->
                        </div>
                        <!--end::Content-->
                    </div>
                    <!--end::Stepper-->
                </div>
                <!--end::Modal body-->
            </div>
            <!--end::Modal content-->
        </div>
        <!--end::Modal dialog-->
    </div>
    <!--end::Modal - Create App-->

</div>
@push('scripts')

    <script src="{{ asset('assets/js/custom/modals/create-app.js') }}"></script>
@endpush
@script
<script>
    window.changeWire = (name, value) => {
        $wire.set(name, value);
    }

    let disabledStep = {{$disabledStep}};
    $('.stepper-label').removeClass('disabled')
    $('.stepper-item-' + disabledStep).addClass('disabled')
    let components = Livewire.getByName('meeting.creator');
    console.log(components);
    window.changeStep = function (step, next) {
        if (step == disabledStep && next == disabledStep + 1) {
            window.stepper.goTo(disabledStep - 1);
        }

        if (step == disabledStep && next == disabledStep - 1) {
            window.stepper.goTo(disabledStep + 1);
        }

        window.changeWire('step', step);
        window.changeWire('next', next);
    }

    Livewire.on('previewStep', () => {
        window.stepper.goPrevious();
    });
    Livewire.on('nextStep', () => {
        window.stepper.goNext();
    });

    Livewire.on('goTo', (data) => {
        let step = data[0]
        window.stepper.goTo(step);
    });

    Livewire.on('disableStep', (data) => {
        let step = data[0]
        $('.stepper-label').removeClass('disabled')
        $('.stepper-item-' + step).addClass('disabled')
        disabledStep = step;
    });


    $('#daterange-picker').attr("readonly", true)
    $('#daterange-picker').persianDatepicker({
        locale: 'fa',
        format: 'L',
        autoClose: true,
        onSelect: function (unix) {
            $('#daterange-picker').trigger('change')
        }
    });

    $('#start-date-picker').attr("readonly", true)
    $('#start-date-picker').persianDatepicker({
        locale: 'fa',
        format: 'L',
        initialValueType: 'gregorian',
        autoClose: true,
        onSelect: function (unix) {
            $('#start-date-picker').trigger('change')
        }
    });

    Inputmask({}).mask("#kt_inputmask_time");

</script>

@endscript

