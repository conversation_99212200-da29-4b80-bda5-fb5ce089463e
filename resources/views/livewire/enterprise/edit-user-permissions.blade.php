<div>
    <div class="d-flex flex-wrap flex-column justify-content-around align-items-center">
        <img src="{{asset('assets/media/icon_v2/search_400x400.svg')}}" alt="" style="max-width: 240px;margin-top: -50px; ">
       <div class="d-flex flex-wrap gap-6">
           <div class="flex justify-between w-1/2 item-center">
               <div class="fv-row mb-5">
                   <div class="form-check form-switch form-check-custom form-check-solid mb-3">
                       <input class="form-check-input" type="checkbox" wire:model="filesAccess" id="filesAccess"/>
                       <label class="form-check-label fw-semibold text-gray-800" for="filesAccess">
                           دسترسی به فایل ها
                       </label>
                   </div>
               </div>

               <div class="fv-row mb-5">
                   <div class="form-check form-switch form-check-custom form-check-solid mb-3">
                       <input class="form-check-input" type="checkbox" wire:model="settingsAccess"
                              id="settingsAccess"/>
                       <label class="form-check-label fw-semibold text-gray-800" for="settingsAccess">
                           دسترسی به تنظیمات
                       </label>
                   </div>
               </div>
           </div>
           <div class="flex">
               <div class="fv-row mb-5 w-1/2">
                   <div class="form-check form-switch form-check-custom form-check-solid mb-3">
                       <input class="form-check-input" type="checkbox" wire:model="usersAccess" id="usersAccess"/>
                       <label class="form-check-label fw-semibold text-gray-800" for="usersAccess">
                           دسترسی به کاربران
                       </label>
                   </div>
               </div>

               <div class="fv-row mb-5">
                   <div class="form-check form-switch form-check-custom form-check-solid mb-3">
                       <input class="form-check-input" type="checkbox" wire:model="reportsAccess"
                              id="reportsAccess"/>
                       <label class="form-check-label fw-semibold text-gray-800" for="reportsAccess">
                           دسترسی به گزارشات
                       </label>
                   </div>
               </div>
           </div>
       </div>
    </div>

    <div class="text-center pt-5">
        <button type="button" class="btn btn-light me-3" data-bs-dismiss="modal">
            انصراف
        </button>

        <button type="button" class="btn btn-primary" wire:click="updatePermissions">
            <span wire:loading.remove>
                ذخیره تغییرات
            </span>
            <span wire:loading>
                <span class="spinner-border spinner-border-sm" role="status"></span>
                در حال ذخیره...
            </span>
        </button>
    </div>
</div>
