<div class="card shadow-sm direction-rtl w-100">
    <div class="card-header border-0 pt-6">
        <div class="card-title">
            <h3 class="fw-bolder">دعوت به سازمان {{ $inviteCompanyName }}</h3>
        </div>
    </div>

    <div class="card-body">
        @if($error)
            <div class="notice d-flex bg-light-danger rounded border-danger border border-dashed p-6">
                <span class="svg-icon svg-icon-2tx svg-icon-danger me-4">
                    <i class="bi bi-exclamation-circle fs-2"></i>
                </span>
                <div class="d-flex flex-stack flex-grow-1">
                    <div class="fw-bold">
                        <h4 class="text-danger fw-bolder">خطا</h4>
                        <div class="fs-6 text-danger">{{ $error }}</div>
                    </div>
                </div>
            </div>
        @else
            @if($hasInvite)
                <div class="notice d-flex bg-light-primary rounded border-primary border border-dashed p-6 mb-6">
                    <span class="svg-icon svg-icon-2tx svg-icon-primary me-4">
                        <i class="bi bi-envelope-check fs-2"></i>
                    </span>
                    <div class="d-flex flex-stack flex-grow-1">
                        <div>
                            <h4 class="text-gray-900 fw-bolder">پیغام</h4>
                            <div class="fs-6 text-gray-700">
                                <b>{{$inviteMessage}}</b>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="py-5">
                    <div class="d-flex flex-column mb-10">
                        <h4 class="mb-5">دسترسی‌های اعطا شده:</h4>

                        <div class="row g-5">
                            @if($filesAccess)
                                <div class="col-xxl-3 col-xl-4 col-sm-6">
                                    <div class="d-flex align-items-center bg-light-success rounded p-5">
                                    <span class="svg-icon svg-icon-success me-5">
                                        <i class="bi bi-file-earmark fs-2"></i>
                                    </span>
                                        <div class="flex-grow-1">
                                            <span class="text-gray-800 fw-bolder fs-6">دسترسی به فایل‌ها</span>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($settingsAccess)
                                <div class="col-xxl-3 col-xl-4 col-sm-6">
                                    <div class="d-flex align-items-center bg-light-primary rounded p-5">
                                    <span class="svg-icon svg-icon-primary me-5">
                                        <i class="bi bi-gear fs-2"></i>
                                    </span>
                                        <div class="flex-grow-1">
                                            <span class="text-gray-800 fw-bolder fs-6">دسترسی به تنظیمات</span>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($usersAccess)
                                <div class="col-xxl-3 col-xl-4 col-sm-6">
                                    <div class="d-flex align-items-center bg-light-info rounded p-5">
                                    <span class="svg-icon svg-icon-info me-5">
                                        <i class="bi bi-people fs-2"></i>
                                    </span>
                                        <div class="flex-grow-1">
                                            <span class="text-gray-800 fw-bolder fs-6">دسترسی به کاربران</span>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($reportsAccess)
                                <div class="col-xxl-3 col-xl-4 col-sm-6">
                                    <div class="d-flex align-items-center bg-light-warning rounded p-5">
                                    <span class="svg-icon svg-icon-warning me-5">
                                        <i class="bi bi-graph-up fs-2"></i>
                                    </span>
                                        <div class="flex-grow-1">
                                            <span class="text-gray-800 fw-bolder fs-6">دسترسی به گزارش‌ها</span>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="separator separator-dashed my-8"></div>


                    @auth
                        <div class="text-center">
                            <button wire:click="accept"
                                    class="btn btn-primary btn-lg px-8"
                                    wire:loading.attr="disabled">
                                <span wire:loading.remove>
                                    <i class="bi bi-check-lg fs-2 me-2"></i>
                                    پذیرش دعوت و پیوستن به سازمان
                                </span>
                                <span wire:loading>
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    در حال پردازش...
                                </span>
                            </button>
                        </div>
                    @else
                        <div
                            class="notice d-flex bg-light-warning rounded border-warning border border-dashed mb-10 p-6">
                            <!--begin::Icon-->
                            <!--begin::Svg Icon | path: /var/www/preview.keenthemes.com/keenthemes/start/docs/core/html/src/media/icons/duotune/general/gen051.svg-->
                            <span class="svg-icon svg-icon-warning svg-icon-2hx"><svg width="24" height="24"
                                                                                      viewBox="0 0 24 24" fill="none"
                                                                                      xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3"
      d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z"
      fill="currentColor"></path>
<path
    d="M14.854 11.321C14.7568 11.2282 14.6388 11.1818 14.4998 11.1818H14.3333V10.2272C14.3333 9.61741 14.1041 9.09378 13.6458 8.65628C13.1875 8.21876 12.639 8 12 8C11.361 8 10.8124 8.21876 10.3541 8.65626C9.89574 9.09378 9.66663 9.61739 9.66663 10.2272V11.1818H9.49999C9.36115 11.1818 9.24306 11.2282 9.14583 11.321C9.0486 11.4138 9 11.5265 9 11.6591V14.5227C9 14.6553 9.04862 14.768 9.14583 14.8609C9.24306 14.9536 9.36115 15 9.49999 15H14.5C14.6389 15 14.7569 14.9536 14.8542 14.8609C14.9513 14.768 15 14.6553 15 14.5227V11.6591C15.0001 11.5265 14.9513 11.4138 14.854 11.321ZM13.3333 11.1818H10.6666V10.2272C10.6666 9.87594 10.7969 9.57597 11.0573 9.32743C11.3177 9.07886 11.6319 8.9546 12 8.9546C12.3681 8.9546 12.6823 9.07884 12.9427 9.32743C13.2031 9.57595 13.3333 9.87594 13.3333 10.2272V11.1818Z"
    fill="currentColor"></path>
</svg>
</span>
                            <!--end::Svg Icon-->

                            <i class="ki-duotone ki-information fs-2tx text-warning me-4"><span
                                    class="path1"></span><span class="path2"></span><span class="path3"></span></i>
                            <!--end::Icon-->

                            <!--begin::Wrapper-->
                            <div class="d-flex flex-stack flex-grow-1 ">
                                <!--begin::Content-->
                                <div class=" fw-semibold">

                                    <div class="fs-6 text-gray-700 ">
                                        <div class="fw-bold text-gray-900 pb-2">توجه:</div>
                                        برای تایید دسترسی نیاز است که ابتدا به حساب کاربری خود در سایت آی روم وارد شوید
                                        <div class="fw-bold text-gray-900 pb-2">بعد از ورود مجددا لینک دعوت خود را باز
                                            کنید
                                        </div>
                                    </div>
                                </div>
                                <!--end::Content-->

                            </div>
                            <!--end::Wrapper-->
                        </div>
                        <div class="text-center">
                            <a href="{{ route('loginPhone', ['redirect' => url()->current()]) }}"
                               class="btn btn-primary btn-lg px-8">
                                <i class="bi bi-box-arrow-in-right fs-2 me-2"></i>
                                ورود به حساب کاربری
                            </a>
                        </div>
                    @endauth

                </div>
            @endif
        @endif
    </div>
</div>

@push('scripts')
    <script>
        Livewire.on('alert', params => {
            Swal.fire({
                text: params[0].message,
                icon: params[0].type,
                buttonsStyling: false,
                confirmButtonText: "باشه",
                customClass: {
                    confirmButton: "btn btn-primary"
                }
            });
        });
    </script>
@endpush
