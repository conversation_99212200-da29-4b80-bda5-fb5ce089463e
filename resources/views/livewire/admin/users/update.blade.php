<!-- Modal -->

<div wire:ignore.self class="modal fade direction-rtl  modal-full-height" tabindex="-1" id="edit_users">
    <div class="modal-dialog ">
        <div class="modal-content">


            <div class="modal-header">
                <h5 class="modal-title">ویرایش کاربر</h5>

                <!--begin::Close-->
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                     aria-label="Close">
                    <span class="svg-icon svg-icon-2x">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px"
                             height="24px" viewBox="0 0 24 24" version="1.1">
                            <g transform="translate(12.000000, 12.000000) rotate(-45.000000) translate(-12.000000, -12.000000) translate(4.000000, 4.000000)"
                               fill="#000000">
                                <rect fill="#000000" x="0" y="7" width="16" height="2" rx="1"/>
                                <rect fill="#000000" opacity="0.5"
                                      transform="translate(8.000000, 8.000000) rotate(-270.000000) translate(-8.000000, -8.000000)"
                                      x="0" y="7" width="16" height="2" rx="1"/>
                            </g>
                        </svg>
                    </span>
                </div>
                <!--end::Close-->
            </div>

            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <input type="hidden" wire:model.live="user_id">

                        @error('name') <span class="text-danger">{{ $message }}</span>@enderror
                    </div>


                    <!--begin::Form group-->
                    <div class="fv-row mb-5">
                        <label class="form-label fs-6 fw-bolder text-dark pt-5">نام کاربری</label>
                        <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                               name="user_name" autocomplete="off" wire:model.live="user_name"/>
                        @if ($errors->has('user_name'))
                            <span class="invalid-feedback d-block" role="alert">
                                        <strong>{{ $errors->first('user_name') }}</strong>
                            </span>
                        @endif
                    </div>
                    <!--end::Form group-->
                    <!--begin::Form group-->
                    <div class="fv-row mb-5">
                        <label class="form-label fs-6 fw-bolder text-dark pt-5">ایمیل</label>
                        <input class="form-control form-control-lg form-control-solid" type="email" placeholder=""
                               name="email" autocomplete="off" wire:model.live="email"/>
                        @if ($errors->has('email'))
                            <span class="invalid-feedback d-block" role="alert">
                                        <strong>{{ $errors->first('email') }}</strong>
                                    </span>
                        @endif

                    </div>
                    <!--end::Form group-->

                    <div class="fv-row mb-5">
                        <label class="form-label fs-6 fw-bolder text-dark pt-5">کد ملی</label>
                        <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                               name="national_id" autocomplete="off" wire:model.live="national_id"/>
                        @if ($errors->has('national_id'))
                            <span class="invalid-feedback d-block" role="alert">
                                        <strong>{{ $errors->first('national_id') }}</strong>
                            </span>
                        @endif
                    </div>

                    <div class="fv-row mb-5">
                        <label class="form-label fs-6 fw-bolder text-dark pt-5">شماره تماس</label>
                        <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                               name="phone_number" autocomplete="off" wire:model.live="phone_number"/>
                        @if ($errors->has('phone_number'))
                            <span class="invalid-feedback d-block" role="alert">
                                        <strong>{{ $errors->first('phone_number') }}</strong>
                            </span>
                        @endif
                    </div>


                    <div class="fv-row mb-5">
                        <label class="form-label fs-6 fw-bolder text-dark pt-5">نقش های کاربری</label>
                        <select  class="form-select form-select-lg form-select-solid"
                                data-control="select2"
                                data-placeholder="نقش کابری را انتخاب نمایید" data-allow-clear="true"

                                multiple="multiple" name="roles[]" id="selected_role_selection"
                                 wire:model.live="selected_role">
                            @foreach($roles as $role)
                                @if(isset($old_roles) && in_array($role->id,$old_roles))
                                    <option value="{{$role->id}}" selected>{{$role->title}}</option>
                                @else
                                    <option value="{{$role->id}}">{{$role->title}}</option>
                                @endif

                            @endforeach
                        </select>

                        @if ($errors->has('roles'))
                            <span class="invalid-feedback d-block" role="alert">
                                        <strong>{{ $errors->first('roles') }}</strong>
                            </span>
                        @endif
                    </div>


                    <!--begin::Form group-->
                    <div class="fv-row mb-5">
                        <label class="form-label fs-6 fw-bolder text-dark pt-5">نام</label>
                        <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                               name="first_name" autocomplete="off" wire:model.live="first_name">
                        @if ($errors->has('first_name'))
                            <span class="invalid-feedback d-block" role="alert">
                                        <strong>{{ $errors->first('first_name') }}</strong>
                            </span>
                        @endif
                    </div>
                    <div class="fv-row mb-5">
                        <label class="form-label fs-6 fw-bolder text-dark pt-5"> نام خانوادگی</label>
                        <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                               name="last_name" autocomplete="off" wire:model.live="last_name"/>
                        @if ($errors->has('last_name'))
                            <span class="invalid-feedback d-block" role="alert">
                                        <strong>{{ $errors->first('last_name') }}</strong>
                            </span>
                        @endif
                    </div>


                    <div class="fv-row mb-5">
                        <label class="form-label fs-6 fw-bolder text-dark pt-5"> نام خانوادگی به انگلیسی</label>
                        <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                               name="english_name" autocomplete="off" wire:model.live="english_name"/>
                        @if ($errors->has('english_name'))
                            <span class="invalid-feedback d-block" role="alert">
                                        <strong>{{ $errors->first('english_name') }}</strong>
                            </span>
                        @endif
                    </div>

                    <div class="fv-row mb-5">
                        <label class="form-label fs-6 fw-bolder text-dark pt-5"> کشور</label>
                        <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                               name="country" autocomplete="off" wire:model.live="country"/>
                        @if ($errors->has('country'))
                            <span class="invalid-feedback d-block" role="alert">
                                        <strong>{{ $errors->first('country') }}</strong>
                            </span>
                        @endif
                    </div>

                    <div class="fv-row mb-5">
                        <label class="form-label fs-6 fw-bolder text-dark pt-5"> سال تولد</label>
                        <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                               name="birth_date" autocomplete="off" wire:model.live="birth_date"/>
                        @if ($errors->has('birth_date'))
                            <span class="invalid-feedback d-block" role="alert">
                                        <strong>{{ $errors->first('birth_date') }}</strong>
                            </span>
                        @endif
                    </div>
                    <div class="fv-row mb-5">
                        <label class="form-label fs-6 fw-bolder text-dark pt-5">میزان تحصیلات</label>
                        <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                               name="education" autocomplete="off" wire:model.live="education"/>
                        @if ($errors->has('education'))
                            <span class="invalid-feedback d-block" role="alert">
                                        <strong>{{ $errors->first('education') }}</strong>
                            </span>
                        @endif
                    </div>
                    <div class="fv-row mb-5">
                        <label class="form-label fs-6 fw-bolder text-dark pt-5">موقعیت شغلی</label>
                        <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                               name="job_position" autocomplete="off" wire:model.live="job_position"/>
                        @if ($errors->has('job_position'))
                            <span class="invalid-feedback d-block" role="alert">
                                        <strong>{{ $errors->first('job_position') }}</strong>
                            </span>
                        @endif
                    </div>
                    <!--end::Form group-->
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" wire:click.prevent="cancel()" class="btn btn-light" data-bs-dismiss="modal">
                    انصراف
                </button>
                <button type="button" wire:click.prevent="update()" class="btn btn-primary ">ذخیره تغییرات</button>
            </div>
        </div>
    </div>
</div>


@push('scripts')
    <script>
        $(document).ready(function () {

            $('.form-select').on('change', function (e) {
                var data = $(this).select2("val");
                 @this.set('selected_role', data);

            });
        });
    </script>
@endpush
