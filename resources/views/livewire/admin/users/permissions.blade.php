<div class="card">


    <div class="card-body direction-rtl p-10 p-lg-15">

        <p>
            <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#add_permission"
                    aria-expanded="false" aria-controls="add_permission">
                اضافه کردن دسترسی جدید
            </button>
        </p>
        <div wire:ignore.self class="collapse" id="add_permission">
            <div class="card card-body">
                <form>
                    <!--begin::Form group-->
                    <div class="fv-row row mb-10">
                        <div class="col-lg-4 col-md-4">
                            <label class="form-label fs-6 fw-bolder text-dark pt-5">نام لاتین دسترسی</label>
                            <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                                   name="name" autocomplete="off" wire:model.live="name"/>
                            @if ($errors->has('name'))
                                <span class="invalid-feedback d-block" role="alert">
                                            <strong>{{ $errors->first('name') }}</strong>
                                </span>
                            @endif
                        </div>
                        <div class="col-lg-4 col-md-4">
                            <label class="form-label fs-6 fw-bolder text-dark pt-5">عنوان فارسی دسترسی</label>
                            <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                                   name="title" autocomplete="off" wire:model.live="title"/>
                            @if ($errors->has('title'))
                                <span class="invalid-feedback d-block" role="alert">
                                        <strong>{{ $errors->first('title') }}</strong>
                            </span>
                            @endif
                        </div>
                        <div class="col-lg-4 col-md-4" wire:ignore>
                            <label class="form-label fs-6 fw-bolder text-dark pt-5">موضوع</label>
                            <input id="permission_category" name="permission_category" type="text" class="d-none"
                                   wire:model.live="permission_category"/>

                            <select data-livewire="@this" wire:ignore
                                    class="form-select form-select-solid direction-rtl js-select" data-control="select22"
                                    data-placeholder="انتخاب کنید" id="permission_category_selection">
                                <option></option>
                                @foreach($permission_category_list as $category)
                                    <option value="{{$category->id}}">{{$category->name}}</option>
                                @endforeach
                            </select>
                        </div>
                    @if ($errors->has('permission_category'))
                        <span class="invalid-feedback d-block" role="alert">
                                        <strong>{{ $errors->first('permission_category') }}</strong>
                            </span>
                    @endif
                    </div>
                </form>
                <div class="fv-row mb-5 direction-rtl">

                    <button type="button" wire:click.prevent="store()" class="btn btn-primary ">ذخیره تغییرات</button>

                    <button type="button" wire:click.prevent="cancel()" class="btn btn-light" data-bs-toggle="collapse"
                            href="#add_permission" role="button" aria-expanded="false" aria-controls="add_permission">
                        انصراف
                    </button>
                </div>
            </div>
        </div>

        <div class="separator my-10"></div>

        @foreach($permissions as $category)

            <h1 class="anchor fw-bolder mb-5" id="content-blocking">
                <a href="#c{{$category->first()->permission_category->id}}"></a> {{$category->first()->permission_category->name}}
            </h1>

            <ul class="list-group list-group-horizontal mb-6 clearfix">

                @foreach($category as $permission)
                    <li class="list-group-item">
                        <a  class="btn btn-icon btn-bg-white btn-active-bg-accent btn-sm me-2"
                           wire:click="$dispatch('triggerDelete',{{ $permission->id }})">
                            <!--begin::Svg Icon | path: assets/media/icons/duotone/Interface/Close-Square.svg-->
                            <span class="svg-icon svg-icon-muted svg-icon-2hx">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                     width="24" height="24"
                                     viewBox="0 0 24 24" fill="none">
                                <path opacity="0.25" fill-rule="evenodd" clip-rule="evenodd"
                                      d="M2.36899 6.54184C2.65912 4.34504 4.34504 2.65912 6.54184 2.36899C8.05208 2.16953 9.94127 2 12 2C14.0587 2 15.9479 2.16953 17.4582 2.36899C19.655 2.65912 21.3409 4.34504 21.631 6.54184C21.8305 8.05208 22 9.94127 22 12C22 14.0587 21.8305 15.9479 21.631 17.4582C21.3409 19.655 19.655 21.3409 17.4582 21.631C15.9479 21.8305 14.0587 22 12 22C9.94127 22 8.05208 21.8305 6.54184 21.631C4.34504 21.3409 2.65912 19.655 2.36899 17.4582C2.16953 15.9479 2 14.0587 2 12C2 9.94127 2.16953 8.05208 2.36899 6.54184Z"
                                      fill="#12131A"/>
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M8.29289 8.29289C8.68342 7.90237 9.31658 7.90237 9.70711 8.29289L12 10.5858L14.2929 8.29289C14.6834 7.90237 15.3166 7.90237 15.7071 8.29289C16.0976 8.68342 16.0976 9.31658 15.7071 9.70711L13.4142 12L15.7071 14.2929C16.0976 14.6834 16.0976 15.3166 15.7071 15.7071C15.3166 16.0976 14.6834 16.0976 14.2929 15.7071L12 13.4142L9.70711 15.7071C9.31658 16.0976 8.68342 16.0976 8.29289 15.7071C7.90237 15.3166 7.90237 14.6834 8.29289 14.2929L10.5858 12L8.29289 9.70711C7.90237 9.31658 7.90237 8.68342 8.29289 8.29289Z"
                                      fill="#12131A"/>
                                </svg>
                            </span>
                            <!--end::Svg Icon-->
                        </a>
                        {{$permission->title}}
                    </li>
                @endforeach
            </ul>
        @endforeach
    </div>
</div>


<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
         Livewire.on('triggerDelete', id => {
            swal.fire({
                text: "از بابت حذف مطمئن هستید ؟",
                icon: "error",
                buttonsStyling: false,
                showCancelButton: true,
                confirmButtonText: "تایید",
                cancelButtonText: "انصراف",
                customClass: {
                    confirmButton: "btn fw-bold btn-light-primary",
                    cancelButton: 'btn btn-danger'
                }
            }).then((result) => {
                //if user clicks on delete
                if (result.value) {
                    // calling destroy method to delete
                    @this.call('destroy', id)
                    // success response
                } else {

                }
            });
        });
    })
</script>
