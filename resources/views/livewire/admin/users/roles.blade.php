<div class="card">


    <div class="card-body direction-rtl p-10 p-lg-15">

{{--        @can('role_manager')--}}
        <p>
            <button class="btn btn-primary" type="button" data-bs-toggle="modal" data-bs-target="#add_roles" aria-controls="add_roles">
                اضافه کردن نقش کاربری جدید
            </button>
        </p>
        <div wire:ignore.self class="modal fade modal-full-height modal-center" tab-index="-1" id="add_roles" aria-hidden="true">
            <div class="card card-body modal-dialog">
                <div class="modal-content">
                    <div class="modal-header direction-rtl">
                        <h5 class="modal-title">اضافه کردن نقش کاربری جدید</h5>
                        <!--begin::Close-->
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g transform="translate(12.000000, 12.000000) rotate(-45.000000) translate(-12.000000, -12.000000) translate(4.000000, 4.000000)" fill="#000000">
                                        <rect fill="#000000" x="0" y="7" width="16" height="2" rx="1"/>
                                        <rect fill="#000000" opacity="0.5" transform="translate(8.000000, 8.000000) rotate(-270.000000) translate(-8.000000, -8.000000)" x="0" y="7" width="16" height="2" rx="1"/>
                                    </g>
                                </svg>
                            </span>
                        </div>
                        <!--end::Close-->
                    </div>
                    <div class="modal-body">
                        <form>
                            <!--begin::Form group-->
                            <div class="fv-row mb-5">
                                <label class="form-label fs-6 fw-bolder text-dark pt-5">نام لاتین نقش</label>
                                <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                                    name="name" autocomplete="off" wire:model.live="name"/>
                                @if ($errors->has('name'))
                                    <span class="invalid-feedback d-block" role="alert">
                                                <strong>{{ $errors->first('name') }}</strong>
                                    </span>
                                @endif
                            </div>


                            <!--begin::Form group-->
                            <div class="fv-row mb-5">
                                <label class="form-label fs-6 fw-bolder text-dark pt-5">عنوان فارسی نقش</label>
                                <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                                    name="title" autocomplete="off" wire:model.live="title"/>
                                @if ($errors->has('title'))
                                    <span class="invalid-feedback d-block" role="alert">
                                                <strong>{{ $errors->first('title') }}</strong>
                                    </span>
                                @endif
                            </div>

                            <!--begin::Form group-->
                            <div class="fv-row mb-5">
                                <label class="form-label fs-6 fw-bolder text-dark pt-5">توضیحات</label>
                                <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                                    name="description" autocomplete="off" wire:model.live="description"/>
                                @if ($errors->has('description'))
                                    <span class="invalid-feedback d-block" role="alert">
                                                <strong>{{ $errors->first('description') }}</strong>
                                    </span>
                                @endif
                            </div>

                            <div class="fv-row mb-5 user-select-none">
                                <label class="form-label fs-6 fw-bolder text-dark pt-5">دسترسی ها</label>
                                @foreach($permissions as $group)
                                    <h5 class="mb-5">{{$group->first()->permission_category->name}}</h5>
                                    <div class="mb-10 clearfix">
                                        @foreach($group as $role)
                                                <div class="me-2 mb-2 float-right form-check form-check-custom form-check-solid select-box-form">
                                                    <input class="form-check-input" type="checkbox" wire:model.live="permissions_selected"  wire:key="{{ $role->id }}" value="{{ $role->id }}"  name="permissions_selected[]" id="permissions_selected_{{$role->id}}"/>
                                                    <label class="form-check-label" for="permissions_selected_{{$role->id}}">
                                                        {{$role->title}}
                                                    </label>
                                                </div>
                                        @endforeach
                                    </div>
                                @endforeach
                            </div>
                            @if ($errors->has('permissions_selected'))
                                <span class="invalid-feedback d-block" role="alert">
                                                <strong>{{ $errors->first('permissions_selected') }}</strong>
                                    </span>
                            @endif


                        </form>
                    </div>
                    <div class="modal-footer">
                        <div class="fv-row direction-rtl">
                            <button type="button" wire:click.prevent="cancel()" class="btn btn-light me-3" data-bs-toggle="modal" href="#add_roles" role="button" aria-expanded="false" aria-controls="add_roles">
                                انصراف
                            </button>
                            <button type="button" wire:click.prevent="store()" class="btn btn-primary ">ذخیره تغییرات</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
{{--        @endcan--}}
        <div class="separator my-10"></div>


        <table id="kt_datatable_example_2"
               class="table table-rounded table-striped border gy-7 gs-7 bg-white direction-rtl datatable">
            <thead>
            <tr class="fw-bold fs-6 text-gray-800">
                <th>کد</th>
                <th>نام</th>
                <th>عنوان</th>
                <th>توضیحات</th>
                <th>زمان ساخت</th>
                <th>اقدامات</th>
            </tr>
            </thead>
            <tbody>

            @foreach($roles_array as $role)
                <tr>
                    <td>{{$role->id}}</td>
                    <td>{{$role->name}}</td>
                    <td>{{$role->title}}</td>
                    <td class="td-width">{{$role->description}}</td>
                    <td>{{jdate($role->create_at)->format('Y/m/d')}}</td>
                    <td>
                        <div class="icon-group">
                            <a href="#" class="btn btn-icon btn-sm me-2"
                                wire:click="$dispatch('triggerDelete_role',{{ $role->id }})">
                                <!--begin::Svg Icon | path: assets/media/icons/duotone/Interface/Close-Square.svg-->
                                <span class="svg-icon svg-icon-muted svg-icon-2x">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                    <path opacity="0.25" fill-rule="evenodd" clip-rule="evenodd"
                                        d="M2.36899 6.54184C2.65912 4.34504 4.34504 2.65912 6.54184 2.36899C8.05208 2.16953 9.94127 2 12 2C14.0587 2 15.9479 2.16953 17.4582 2.36899C19.655 2.65912 21.3409 4.34504 21.631 6.54184C21.8305 8.05208 22 9.94127 22 12C22 14.0587 21.8305 15.9479 21.631 17.4582C21.3409 19.655 19.655 21.3409 17.4582 21.631C15.9479 21.8305 14.0587 22 12 22C9.94127 22 8.05208 21.8305 6.54184 21.631C4.34504 21.3409 2.65912 19.655 2.36899 17.4582C2.16953 15.9479 2 14.0587 2 12C2 9.94127 2.16953 8.05208 2.36899 6.54184Z"
                                        fill="#12131A"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M8.29289 8.29289C8.68342 7.90237 9.31658 7.90237 9.70711 8.29289L12 10.5858L14.2929 8.29289C14.6834 7.90237 15.3166 7.90237 15.7071 8.29289C16.0976 8.68342 16.0976 9.31658 15.7071 9.70711L13.4142 12L15.7071 14.2929C16.0976 14.6834 16.0976 15.3166 15.7071 15.7071C15.3166 16.0976 14.6834 16.0976 14.2929 15.7071L12 13.4142L9.70711 15.7071C9.31658 16.0976 8.68342 16.0976 8.29289 15.7071C7.90237 15.3166 7.90237 14.6834 8.29289 14.2929L10.5858 12L8.29289 9.70711C7.90237 9.31658 7.90237 8.68342 8.29289 8.29289Z"
                                        fill="#12131A"/>
                                    </svg>
                                </span>
                                <!--end::Svg Icon-->
                            </a>

                            <a href="#" onclick="event.preventDefault();" class="btn btn-icon btn-sm me-2"
                                wire:click.stop="edit({{ $role->id }})">
                                <!--begin::Svg Icon | path: assets/media/icons/duotone/Design/Pencil.svg-->
                                <span class="svg-icon svg-icon-muted svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M3.69601 15.8112L2.15894 19.91C1.70676 21.1158 2.88429 22.2934 4.09012 21.8412L8.18896 20.3041C8.72361 20.1036 9.20914 19.791 9.6129 19.3872L10 19L5 14L4.6129 14.3872C4.20914 14.791 3.8965 15.2765 3.69601 15.8112Z" fill="#12131A"/>
                                <path opacity="0.25" d="M5 14L10 19L19.5 9.5L14.5 4.5L5 14Z" fill="#12131A"/>
                                <path d="M20.8787 8.12136L19.5 9.5L14.5 4.5L15.8787 3.12135C17.0503 1.94978 18.9497 1.94978 20.1213 3.12136L20.8787 3.87872C22.0503 5.05029 22.0503 6.94978 20.8787 8.12136Z" fill="#12131A"/>
                                </svg></span>
                                <!--end::Svg Icon-->
                            </a>
                        </div>


                    </td>
                </tr>
            @endforeach

            </tbody>
            <tfoot>
            <tr class="border-top fw-bold fs-6 text-gray-800">
                <th>کد</th>
                <th>نام</th>
                <th>عنوان</th>
                <th>توضیحات</th>
                <th>زمان ساخت</th>
                <th>اقدامات</th>
            </tr>
            </tfoot>
        </table>

    </div>
</div>


<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
         Livewire.on('triggerDelete_role', id => {
            swal.fire({
                text: "از بابت حذف مطمئن هستید ؟",
                icon: "error",
                buttonsStyling: false,
                showCancelButton: true,
                confirmButtonText: "تایید",
                cancelButtonText: "انصراف",
                customClass: {
                    confirmButton: "btn fw-bold btn-light-primary",
                    cancelButton: 'btn btn-danger'
                }
            }).then((result) => {
                //if user clicks on delete
                if (result.value) {
                    // calling destroy method to delete
                @this.call('destroy', id)
                    // success response


                } else {

                }
            });
        });

         Livewire.on('start_edit', param => {
            $('#add_roles').modal(param['state']);
        });
    })
</script>
