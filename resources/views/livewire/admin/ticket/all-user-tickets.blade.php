<div>
    <div class="card-header card-header-stretch">
        <ul class="nav nav-stretch nav-line-tabs border-bottom-0 justify-content-center" wire:ignore>
            <li class="nav-item " title="" data-bs-toggle="tooltip"
                data-bs-container="body" data-bs-original-title="همه تیکت ها">
                <a class="nav-link justify-content-center w-60px active cursor-pointer py-4"  data-bs-toggle="tab" wire:click="show_all_tickets()">
															<span class="nav-icon m-0">
																<!--begin::Svg Icon | path: icons/duotone/Communication/Mail-heart.svg-->
																<span class="svg-icon svg-icon-1">
																	<svg xmlns="http://www.w3.org/2000/svg" width="24px"
                                                                         height="24px" viewBox="0 0 24 24"
                                                                         version="1.1">
																		<path
                                                                            d="M6,2 L18,2 C18.5522847,2 19,2.44771525 19,3 L19,13 C19,13.5522847 18.5522847,14 18,14 L6,14 C5.44771525,14 5,13.5522847 5,13 L5,3 C5,2.44771525 5.44771525,2 6,2 Z M13.8,4 C13.1562,4 12.4033,4.72985286 12,5.2 C11.5967,4.72985286 10.8438,4 10.2,4 C9.0604,4 8.4,4.88887193 8.4,6.02016349 C8.4,7.27338783 9.6,8.6 12,10 C14.4,8.6 15.6,7.3 15.6,6.1 C15.6,4.96870845 14.9396,4 13.8,4 Z"
                                                                            fill="#000000" opacity="0.3"></path>
																		<path
                                                                            d="M3.79274528,6.57253826 L12,12.5 L20.2072547,6.57253826 C20.4311176,6.4108595 20.7436609,6.46126971 20.9053396,6.68513259 C20.9668779,6.77033951 21,6.87277228 21,6.97787787 L21,17 C21,18.1045695 20.1045695,19 19,19 L5,19 C3.8954305,19 3,18.1045695 3,17 L3,6.97787787 C3,6.70173549 3.22385763,6.47787787 3.5,6.47787787 C3.60510559,6.47787787 3.70753836,6.51099993 3.79274528,6.57253826 Z"
                                                                            fill="#000000"></path>
																	</svg>
																</span>
                                                                <!--end::Svg Icon-->
															</span>
                </a>
            </li>
            <li class="nav-item" data-bs-toggle="tooltip" data-bs-container="body"
                data-bs-original-title="پاسخ نداده ها">
                <a class="nav-link justify-content-center w-60px ms-0 cursor-pointer py-4" data-bs-toggle="tab" wire:click="show_didnt_answers()">
															<span class="nav-icon m-0">
																<!--begin::Svg Icon | path: icons/duotone/Communication/Send.svg-->
																<span class="svg-icon svg-icon-1">
																	<svg xmlns="http://www.w3.org/2000/svg" width="24px"
                                                                         height="24px" viewBox="0 0 24 24"
                                                                         version="1.1">
																		<path
                                                                            d="M3,13.5 L19,12 L3,10.5 L3,3.7732928 C3,3.70255344 3.01501031,3.63261921 3.04403925,3.56811047 C3.15735832,3.3162903 3.45336217,3.20401298 3.70518234,3.31733205 L21.9867539,11.5440392 C22.098181,11.5941815 22.1873901,11.6833905 22.2375323,11.7948177 C22.3508514,12.0466378 22.2385741,12.3426417 21.9867539,12.4559608 L3.70518234,20.6826679 C3.64067359,20.7116969 3.57073936,20.7267072 3.5,20.7267072 C3.22385763,20.7267072 3,20.5028496 3,20.2267072 L3,13.5 Z"
                                                                            fill="#000000"></path>
																	</svg>
																</span>
                                                                <!--end::Svg Icon-->
															</span>
                </a>
            </li>
            <li class="nav-item" data-bs-toggle="tooltip" data-bs-container="body"
                data-bs-original-title="تیکت های بسته"  wire:click="show_close_tickets()">
                <a class="nav-link justify-content-center w-60px ms-0 cursor-pointer py-4" data-bs-toggle="tab">
															<span class="nav-icon m-0">
																<!--begin::Svg Icon | path: icons/duotone/General/Trash.svg-->
																<span class="svg-icon svg-icon-1">
																	<svg xmlns="http://www.w3.org/2000/svg"
                                                                         xmlns:xlink="http://www.w3.org/1999/xlink"
                                                                         width="24px" height="24px" viewBox="0 0 24 24"
                                                                         version="1.1">
																		<g stroke="none" stroke-width="1" fill="none"
                                                                           fill-rule="evenodd">
																			<rect x="0" y="0" width="24"
                                                                                  height="24"></rect>
																			<path
                                                                                d="M6,8 L6,20.5 C6,21.3284271 6.67157288,22 7.5,22 L16.5,22 C17.3284271,22 18,21.3284271 18,20.5 L18,8 L6,8 Z"
                                                                                fill="#000000"
                                                                                fill-rule="nonzero"></path>
																			<path
                                                                                d="M14,4.5 L14,4 C14,3.44771525 13.5522847,3 13,3 L11,3 C10.4477153,3 10,3.44771525 10,4 L10,4.5 L5.5,4.5 C5.22385763,4.5 5,4.72385763 5,5 L5,5.5 C5,5.77614237 5.22385763,6 5.5,6 L18.5,6 C18.7761424,6 19,5.77614237 19,5.5 L19,5 C19,4.72385763 18.7761424,4.5 18.5,4.5 L14,4.5 Z"
                                                                                fill="#000000" opacity="0.3"></path>
																		</g>
																	</svg>
																</span>
                                                                <!--end::Svg Icon-->
															</span>
                </a>
            </li>
            <li class="nav-item" data-bs-toggle="tooltip" data-bs-container="body"
                data-bs-original-title="مشارکت کرده" wire:click="only_related()">
                <a class="nav-link justify-content-center w-60px ms-0 cursor-pointer py-4" data-bs-toggle="tab" >
															<span class="nav-icon m-0">
																<!--begin::Svg Icon | path: icons/duotone/Design/Select.svg-->
																<span class="svg-icon svg-icon-1">
																	<svg xmlns="http://www.w3.org/2000/svg" width="24px"
                                                                         height="24px" viewBox="0 0 24 24"
                                                                         version="1.1">
																		<path
                                                                            d="M18.5,8 C17.1192881,8 16,6.88071187 16,5.5 C16,4.11928813 17.1192881,3 18.5,3 C19.8807119,3 21,4.11928813 21,5.5 C21,6.88071187 19.8807119,8 18.5,8 Z M18.5,21 C17.1192881,21 16,19.8807119 16,18.5 C16,17.1192881 17.1192881,16 18.5,16 C19.8807119,16 21,17.1192881 21,18.5 C21,19.8807119 19.8807119,21 18.5,21 Z M5.5,21 C4.11928813,21 3,19.8807119 3,18.5 C3,17.1192881 4.11928813,16 5.5,16 C6.88071187,16 8,17.1192881 8,18.5 C8,19.8807119 6.88071187,21 5.5,21 Z"
                                                                            fill="#000000" opacity="0.3"></path>
																		<path
                                                                            d="M5.5,8 C4.11928813,8 3,6.88071187 3,5.5 C3,4.11928813 4.11928813,3 5.5,3 C6.88071187,3 8,4.11928813 8,5.5 C8,6.88071187 6.88071187,8 5.5,8 Z M11,4 L13,4 C13.5522847,4 14,4.44771525 14,5 C14,5.55228475 13.5522847,6 13,6 L11,6 C10.4477153,6 10,5.55228475 10,5 C10,4.44771525 10.4477153,4 11,4 Z M11,18 L13,18 C13.5522847,18 14,18.4477153 14,19 C14,19.5522847 13.5522847,20 13,20 L11,20 C10.4477153,20 10,19.5522847 10,19 C10,18.4477153 10.4477153,18 11,18 Z M5,10 C5.55228475,10 6,10.4477153 6,11 L6,13 C6,13.5522847 5.55228475,14 5,14 C4.44771525,14 4,13.5522847 4,13 L4,11 C4,10.4477153 4.44771525,10 5,10 Z M19,10 C19.5522847,10 20,10.4477153 20,11 L20,13 C20,13.5522847 19.5522847,14 19,14 C18.4477153,14 18,13.5522847 18,13 L18,11 C18,10.4477153 18.4477153,10 19,10 Z"
                                                                            fill="#000000"></path>
																	</svg>
																</span>
                                                                <!--end::Svg Icon-->
															</span>
                </a>
            </li>
        </ul>
    </div>

    <!--begin::Card header-->
    <div class="card-header pt-7" id="kt_chat_contacts_header">
        <div class="d-flex align-items-center text-end my-2 direction-rtl justify-content-between">    
            <span class="d-flex">
                <!--begin::Arrow Buttons-->
                <a wire:click="next_page()"
                class="btn btn-icon btn-active-light-primary btn-sm me-2 @if($ticket_page == $all_page) disabled    @endif "
                data-bs-toggle="tooltip" title="" data-bs-container="body" data-bs-original-title="صفحه بعد"
                aria-describedby="tooltip873159">
                    <i class="fas fa-chevron-right fs-6"></i>
                </a>
                <a wire:click="prev_page()"
                class="btn btn-icon btn-active-light-primary btn-sm me-2 @if($ticket_page == 1) disabled    @endif  "
                data-bs-toggle="tooltip" title="" data-bs-container="body" data-bs-original-title="صفحه قبل">
                    <i class="fas fa-chevron-left fs-6"></i>
                </a>
                <!--end::Arrow Buttons--> 
                <!--begin::Per Page Dropdown-->
                <div class="d-flex align-items-center me-2">
                    <span class="text-muted fw-bold me-2">{{$ticket_page}}/{{$all_page}}</span>
                </div>
                <!--end::Per Page Dropdown-->
            </span>
            <span wire:loading wire:target="show_all_tickets,show_didnt_answers,only_related,show_close_tickets,next_page,prev_page">
                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
            </span>
        </div>
    </div>

    <!--end::Card header-->
    <!--begin::Card body-->
    <div class="card-body pt-5" id="kt_chat_contacts_body" wire:loading.class="opacity-05" wire:target="search_term,show_all_tickets,show_didnt_answers,only_related,show_close_tickets,next_page,prev_page">
        <!--begin::Form-->
        <form class="w-100 position-relative" autocomplete="off">
            <!--begin::Icon-->
            <!--begin::Svg Icon | path: icons/duotone/General/Search.svg-->
            <span
                class="svg-icon svg-icon-2 svg-icon-lg-1 svg-icon-gray-500 position-absolute top-50 ms-5 translate-middle-y">
														<svg xmlns="http://www.w3.org/2000/svg"
                                                             xmlns:xlink="http://www.w3.org/1999/xlink" width="24px"
                                                             height="24px" viewBox="0 0 24 24" version="1.1">
															<g stroke="none" stroke-width="1" fill="none"
                                                               fill-rule="evenodd">
																<rect x="0" y="0" width="24" height="24"></rect>
																<path
                                                                    d="M14.2928932,16.7071068 C13.9023689,16.3165825 13.9023689,15.6834175 14.2928932,15.2928932 C14.6834175,14.9023689 15.3165825,14.9023689 15.7071068,15.2928932 L19.7071068,19.2928932 C20.0976311,19.6834175 20.0976311,20.3165825 19.7071068,20.7071068 C19.3165825,21.0976311 18.6834175,21.0976311 18.2928932,20.7071068 L14.2928932,16.7071068 Z"
                                                                    fill="#000000" fill-rule="nonzero"
                                                                    opacity="0.3"></path>
																<path
                                                                    d="M11,16 C13.7614237,16 16,13.7614237 16,11 C16,8.23857625 13.7614237,6 11,6 C8.23857625,6 6,8.23857625 6,11 C6,13.7614237 8.23857625,16 11,16 Z M11,18 C7.13400675,18 4,14.8659932 4,11 C4,7.13400675 7.13400675,4 11,4 C14.8659932,4 18,7.13400675 18,11 C18,14.8659932 14.8659932,18 11,18 Z"
                                                                    fill="#000000" fill-rule="nonzero"></path>
															</g>
														</svg>
													</span>
            <!--end::Svg Icon-->
            <!--end::Icon-->
            <!--begin::Input-->
            <input type="text" class="form-control form-control-solid px-15" name="search" value=""
                   wire:model.live="search_term"
                   placeholder="جستجو در تیکت ها...">
            <span wire:loading wire:target="search_term" style=" position: absolute; top: 24%; left: 10px; ">
                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
            </span>

            <!--end::Input-->
        </form>
        <!--end::Form-->

        <div class="separator separator-dashed my-3"></div>
        <!--begin::List-->
        <div class="scroll-y pe-5 h-200px h-lg-auto" data-kt-scroll="true"
             data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto"
             data-kt-scroll-dependencies="#kt_header, #kt_toolbar, #kt_footer, #kt_chat_contacts_header"
             data-kt-scroll-wrappers="#kt_content, #kt_chat_contacts_body" data-kt-scroll-offset="0px"
             style="max-height: 554px;">


        {{-- Do your work, then step back. --}}


        @forelse($tickets as $ticket)
            <!--begin::User-->
                <div class="d-flex flex-stack py-4 cursor-pointer
                            @if($ticket->status == 5) opacity-50 opacity-100-hover @endif
                    " wire:click="show_ticket({{$ticket->id}})">

                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                        <!--begin::Avatar-->
                        <div class="symbol symbol-45px symbol-circle">
                            <span class="symbol-label bg-light-danger text-danger fs-6 fw-bolder"
                                  style="color: {{$ticket->statusColor()}} !important; background-color:#f2f2f2!important; font-size: 11px !important;">{{$ticket->statusName()}}</span>
                        </div>
                        <!--end::Avatar-->
                        <!--begin::Details-->
                        <div class="ms-5">
                            <a class="fs-5 fw-bolder text-gray-900 text-hover-primary mb-2">{{$ticket->subject}}</a>
                            <div class="fw-bold text-gray-400">{{ Str::limit($ticket->summary, 20) }}</div>
                        </div>
                        <!--end::Details-->
                    </div>
                    <!--end::Details-->
                    <!--begin::Lat seen-->
                    <div class="d-flex flex-column align-items-end ms-2">
                        <span class="text-muted fs-7 mb-1">{{$ticket->ago_time()}}</span>
                        <span class="badge badge-sm badge-circle badge-light-{{$ticket->priorityColor()}} "
                              data-bs-toggle="tooltip" title="{{$ticket->priorityName()}}"
                              data-bs-original-title="{{$ticket->priorityName()}}">{{Str::limit($ticket->priorityName(),1)}}</span>
                    </div>
                    <!--end::Lat seen-->
                </div>
                <!--end::User-->
                <!--begin::Separator-->
                <div class="separator separator-dashed d-none"></div>
                <!--end::Separator-->
            @empty

                تیکتی موجود نیست

            @endforelse
        </div>
        <!-- buttons go disable with this layer -->
        <div class="position-absolute w-100 h-100 top-0 end-0" wire:loading wire:target="search_term,show_all_tickets,show_didnt_answers,only_related,show_close_tickets,next_page,prev_page"></div>
    </div>
    <!--end::List-->
</div>
<!--end::Card body-->


<div>
</div>
