<div>
    {{-- Stop trying to control. --}}


            {{--        @can('role_manager')--}}
            <p>
                <button class="btn btn-primary" type="button" data-bs-toggle="modal" data-bs-target="#add_team" aria-controls="add_team">
                    اضافه کردن تیم جدید
                </button>
            </p>
            <div wire:ignore.self class="modal fade modal-full-height modal-center" tab-index="-1" id="add_team" aria-hidden="true">
                <div class="card card-body modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header direction-rtl">
                            <h5 class="modal-title">اضافه کردن تیم جدید</h5>
                            <!--begin::Close-->
                            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g transform="translate(12.000000, 12.000000) rotate(-45.000000) translate(-12.000000, -12.000000) translate(4.000000, 4.000000)" fill="#000000">
                                        <rect fill="#000000" x="0" y="7" width="16" height="2" rx="1"/>
                                        <rect fill="#000000" opacity="0.5" transform="translate(8.000000, 8.000000) rotate(-270.000000) translate(-8.000000, -8.000000)" x="0" y="7" width="16" height="2" rx="1"/>
                                    </g>
                                </svg>
                            </span>
                            </div>
                            <!--end::Close-->
                        </div>
                        <div class="modal-body">
                            <form>
                                <!--begin::Form group-->
                                <div class="fv-row mb-5">
                                    <label class="form-label fs-6 fw-bolder text-dark pt-5">نام فارسی تیم</label>
                                    <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                                           name="name" autocomplete="off" wire:model.live="name"/>
                                    @if ($errors->has('name'))
                                        <span class="invalid-feedback d-block" role="alert">
                                                <strong>{{ $errors->first('name') }}</strong>
                                    </span>
                                    @endif
                                </div>


                                <!--begin::Form group-->
                                <div class="fv-row mb-5">
                                    <label class="form-label fs-6 fw-bolder text-dark pt-5">ایمیل تیم</label>
                                    <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                                           name="email" autocomplete="off" wire:model.live="email"/>
                                    `@if ($errors->has('email'))
                                        <span class="invalid-feedback d-block" role="alert">
                                                <strong>{{ $errors->first('email') }}</strong>
                                    </span>
                                    @endif`
                                </div>

                                <!--begin::Form group-->
                                <div class="fv-row mb-5">
                                    <label class="form-label fs-6 fw-bolder text-dark pt-5">آدرس ارتباط با اسلک</label>
                                    <input class="form-control form-control-lg form-control-solid" type="text" placeholder=""
                                           name="slack_url" autocomplete="off" wire:model.live="slack_url"/>
                                    @if ($errors->has('slack_url'))
                                        <span class="invalid-feedback d-block" role="alert">
                                                <strong>{{ $errors->first('slack_url') }}</strong>
                                    </span>
                                    @endif
                                </div>



                            </form>
                        </div>
                        <div class="modal-footer">
                            <div class="fv-row direction-rtl">
                                <button type="button" wire:click.prevent="cancel()" class="btn btn-light me-3" data-bs-toggle="modal" href="#add_roles" role="button" aria-expanded="false" aria-controls="add_roles">
                                    انصراف
                                </button>
                                <button type="button" wire:click.prevent="store()" class="btn btn-primary ">ذخیره تغییرات</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {{--        @endcan--}}
            <div class="separator my-10"></div>



    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function () {
             Livewire.on('triggerDelete_role', id => {
                swal.fire({
                    text: "از بابت حذف مطمئن هستید ؟",
                    icon: "error",
                    buttonsStyling: false,
                    showCancelButton: true,
                    confirmButtonText: "تایید",
                    cancelButtonText: "انصراف",
                    customClass: {
                        confirmButton: "btn fw-bold btn-light-primary",
                        cancelButton: 'btn btn-danger'
                    }
                }).then((result) => {
                    //if user clicks on delete
                    if (result.value) {
                        // calling destroy method to delete
                    @this.call('destroy', id)
                        // success response
                    } else {

                    }
                });
            });

        })
    </script>

</div>
