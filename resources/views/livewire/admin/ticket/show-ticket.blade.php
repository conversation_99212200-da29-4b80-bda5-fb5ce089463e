<div>
{{-- Care about people's approval and you will be their prisoner. --}}
{{--@if(!empty($ticket))--}}

<!--begin::Modal - View Users-->
    <div class="modal fade" id="kt_modal_add_new_tag" tabindex="-1" aria-hidden="true" wire:ignore>
        <!--begin::Modal dialog-->
        <div class="modal-dialog mw-650px">
            <!--begin::Modal content-->
            <div class="modal-content">
                <!--begin::Modal header-->
                <div class="modal-header pb-0 border-0 justify-content-end">
                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-icon-primary" data-bs-dismiss="modal">
                        <!--begin::Svg Icon | path: icons/duotone/Interface/Close-Square.svg-->
                        <span class="svg-icon svg-icon-2x">
														<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                             viewBox="0 0 24 24" fill="none">
															<path opacity="0.25" fill-rule="evenodd" clip-rule="evenodd"
                                                                  d="M2.36899 6.54184C2.65912 4.34504 4.34504 2.65912 6.54184 2.36899C8.05208 2.16953 9.94127 2 12 2C14.0587 2 15.9479 2.16953 17.4582 2.36899C19.655 2.65912 21.3409 4.34504 21.631 6.54184C21.8305 8.05208 22 9.94127 22 12C22 14.0587 21.8305 15.9479 21.631 17.4582C21.3409 19.655 19.655 21.3409 17.4582 21.631C15.9479 21.8305 14.0587 22 12 22C9.94127 22 8.05208 21.8305 6.54184 21.631C4.34504 21.3409 2.65912 19.655 2.36899 17.4582C2.16953 15.9479 2 14.0587 2 12C2 9.94127 2.16953 8.05208 2.36899 6.54184Z"
                                                                  fill="#12131A"/>
															<path fill-rule="evenodd" clip-rule="evenodd"
                                                                  d="M8.29289 8.29289C8.68342 7.90237 9.31658 7.90237 9.70711 8.29289L12 10.5858L14.2929 8.29289C14.6834 7.90237 15.3166 7.90237 15.7071 8.29289C16.0976 8.68342 16.0976 9.31658 15.7071 9.70711L13.4142 12L15.7071 14.2929C16.0976 14.6834 16.0976 15.3166 15.7071 15.7071C15.3166 16.0976 14.6834 16.0976 14.2929 15.7071L12 13.4142L9.70711 15.7071C9.31658 16.0976 8.68342 16.0976 8.29289 15.7071C7.90237 15.3166 7.90237 14.6834 8.29289 14.2929L10.5858 12L8.29289 9.70711C7.90237 9.31658 7.90237 8.68342 8.29289 8.29289Z"
                                                                  fill="#12131A"/>
														</svg>
													</span>
                        <!--end::Svg Icon-->
                    </div>
                    <!--end::Close-->
                </div>
                <!--begin::Modal header-->
                <!--begin::Modal body-->
                <div class="modal-body scroll-y mx-5 mx-xl-18 pt-0 pb-15">
                    <!--begin::Heading-->
                    <div class="text-center mb-12">
                        <!--begin::Title-->
                        <div class="fs-2 fw-bolder mb-1">اضافه کردن تگ</div>
                        <!--end::Title-->
                        <!--begin::Description-->
                        <div class="text-gray-400 fw-bold fs-3">لطفا تگ های مرتبط با این تیکت را وارد نمایید.</div>
                        <!--end::Description-->
                    </div>
                    <!--end::Heading-->
                    <!--begin::Users-->
                    <div class="mb-15">

                        <input class="form-control form-control-solid" wire:modal="ticket_tags" id="kt_tagify__hamed"/>

                    </div>

                    <!--end::Users-->
                    <!--begin::Notice-->
                    <div class="d-flex justify-content-between">
                        <!--begin::Label-->
                        <div class="fw-bold">
                            <label class="fs-6">ساخت تگ های جدید</label>
                            <div class="fs-7 text-gray-400">در صورت فعال کردن این امکان میتوانید در لیست بالا تگ های
                                جدیدی بسازید که در کل سیستم ثبت شود
                            </div>
                        </div>
                        <!--end::Label-->
                        <!--begin::Switch-->
                        <label class="form-check form-switch form-check-custom form-check-solid">
                            <input class="form-check-input" type="checkbox" value=""/>
                            {{--                            <span class="form-check-label fw-bold text-gray-400">ساخت تگ جدید</span>--}}
                        </label>
                        <!--end::Switch-->
                    </div>
                    <!--end::Notice-->
                </div>
                <!--end::Modal body-->
            </div>
            <!--end::Modal content-->
        </div>
        <!--end::Modal dialog-->
    </div>
    <!--end::Modal - View Users-->
    <!--begin::Modal - View Users-->
    <div class="modal fade" id="kt_modal_change_priority" tabindex="-1" aria-hidden="true" wire:ignore>
        <!--begin::Modal dialog-->
        <div class="modal-dialog mw-650px">
            <!--begin::Modal content-->
            <div class="modal-content">
                <!--begin::Modal header-->
                <div class="modal-header pb-0 border-0 justify-content-end">
                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-icon-primary" data-bs-dismiss="modal">
                        <!--begin::Svg Icon | path: icons/duotone/Interface/Close-Square.svg-->
                        <span class="svg-icon svg-icon-2x">
														<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                             viewBox="0 0 24 24" fill="none">
															<path opacity="0.25" fill-rule="evenodd" clip-rule="evenodd"
                                                                  d="M2.36899 6.54184C2.65912 4.34504 4.34504 2.65912 6.54184 2.36899C8.05208 2.16953 9.94127 2 12 2C14.0587 2 15.9479 2.16953 17.4582 2.36899C19.655 2.65912 21.3409 4.34504 21.631 6.54184C21.8305 8.05208 22 9.94127 22 12C22 14.0587 21.8305 15.9479 21.631 17.4582C21.3409 19.655 19.655 21.3409 17.4582 21.631C15.9479 21.8305 14.0587 22 12 22C9.94127 22 8.05208 21.8305 6.54184 21.631C4.34504 21.3409 2.65912 19.655 2.36899 17.4582C2.16953 15.9479 2 14.0587 2 12C2 9.94127 2.16953 8.05208 2.36899 6.54184Z"
                                                                  fill="#12131A"/>
															<path fill-rule="evenodd" clip-rule="evenodd"
                                                                  d="M8.29289 8.29289C8.68342 7.90237 9.31658 7.90237 9.70711 8.29289L12 10.5858L14.2929 8.29289C14.6834 7.90237 15.3166 7.90237 15.7071 8.29289C16.0976 8.68342 16.0976 9.31658 15.7071 9.70711L13.4142 12L15.7071 14.2929C16.0976 14.6834 16.0976 15.3166 15.7071 15.7071C15.3166 16.0976 14.6834 16.0976 14.2929 15.7071L12 13.4142L9.70711 15.7071C9.31658 16.0976 8.68342 16.0976 8.29289 15.7071C7.90237 15.3166 7.90237 14.6834 8.29289 14.2929L10.5858 12L8.29289 9.70711C7.90237 9.31658 7.90237 8.68342 8.29289 8.29289Z"
                                                                  fill="#12131A"/>
														</svg>
													</span>
                        <!--end::Svg Icon-->
                    </div>
                    <!--end::Close-->
                </div>
                <!--begin::Modal header-->
                <!--begin::Modal body-->
                <div class="modal-body scroll-y mx-5 mx-xl-18 pt-0 pb-15">
                    <!--begin::Heading-->
                    <div class="text-center mb-12">
                        <!--begin::Title-->
                        <div class="fs-2 fw-bolder mb-1">تغییر اولویت</div>
                        <!--end::Title-->
                        <!--begin::Description-->
                        <div class="text-gray-400 fw-bold fs-3">اولویت این تیکت را میتوانید تغییر دهید.</div>
                        <!--end::Description-->
                    </div>
                    <!--end::Heading-->
                    <!--begin::Users-->
                    <div class="mb-15">
                    @if($priority)
                        <!--begin::Form Group-->
                            <div class="mb-10 position-relative">
                                <select class="select-arrow form-select form-select-lg form-select-solid"
                                        name="support_team" id="modal_support_team"
                                        dir="rtl"
                                        wire:ignore
                                        data-control="select2"
                                        wire:model.live="selected_priority"
                                        onchange="@this.selected_priority = $(this).val(); $(this).attr('disabled','true').addClass('disabled'); $('.select-loading-spinner').removeClass('d-none'); $('.select-down-arrow').addClass('d-none');"
                                        data-placeholder="انتخاب کنید">
                                    <option></option>
                                    @foreach($priority as $data)
                                        <option value="{{$data["id"]}}"
                                        >{{$data["name"]}}</option>
                                    @endforeach
                                </select>
                                <span
                                    class="select-loading-spinner spinner-border spinner-border-sm align-middle ms-2"></span>
                                <span class="select-down-arrow"><i class="fas fa-chevron-down fs-6"></i></span>
                                @if ($errors->has('selected_priority'))
                                    <span class="invalid-feedback d-block" role="alert">
                                     <strong>{{ $errors->first('selected_priority') }}</strong>
                                </span>
                                @endif
                            </div>
                        @endif

                    </div>

                    <!--end::Users-->
                    <!--begin::Notice-->
                    <div class="d-flex justify-content-center">
                        <!--begin::Label-->
                        <div class="fw-bold">

                            <div class="fs-7 text-gray-400">
                                قرار دادن اولولیت صحیح به هر تیکت میتواند باعث افزایش سرعت پاسخگویی شود.
                            </div>
                        </div>
                        <!--end::Label-->

                    </div>
                    <!--end::Notice-->
                </div>
                <!--end::Modal body-->
            </div>
            <!--end::Modal content-->
        </div>
        <!--end::Modal dialog-->
    </div>
    <!--end::Modal - View Users-->

    <!--begin::Modal - View Users-->
    <div class="modal fade" id="kt_modal_change_status" tabindex="-1" aria-hidden="true" wire:ignore>
        <!--begin::Modal dialog-->
        <div class="modal-dialog mw-650px">
            <!--begin::Modal content-->
            <div class="modal-content">
                <!--begin::Modal header-->
                <div class="modal-header pb-0 border-0 justify-content-end">
                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-icon-primary" data-bs-dismiss="modal">
                        <!--begin::Svg Icon | path: icons/duotone/Interface/Close-Square.svg-->
                        <span class="svg-icon svg-icon-2x">
														<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                             viewBox="0 0 24 24" fill="none">
															<path opacity="0.25" fill-rule="evenodd" clip-rule="evenodd"
                                                                  d="M2.36899 6.54184C2.65912 4.34504 4.34504 2.65912 6.54184 2.36899C8.05208 2.16953 9.94127 2 12 2C14.0587 2 15.9479 2.16953 17.4582 2.36899C19.655 2.65912 21.3409 4.34504 21.631 6.54184C21.8305 8.05208 22 9.94127 22 12C22 14.0587 21.8305 15.9479 21.631 17.4582C21.3409 19.655 19.655 21.3409 17.4582 21.631C15.9479 21.8305 14.0587 22 12 22C9.94127 22 8.05208 21.8305 6.54184 21.631C4.34504 21.3409 2.65912 19.655 2.36899 17.4582C2.16953 15.9479 2 14.0587 2 12C2 9.94127 2.16953 8.05208 2.36899 6.54184Z"
                                                                  fill="#12131A"/>
															<path fill-rule="evenodd" clip-rule="evenodd"
                                                                  d="M8.29289 8.29289C8.68342 7.90237 9.31658 7.90237 9.70711 8.29289L12 10.5858L14.2929 8.29289C14.6834 7.90237 15.3166 7.90237 15.7071 8.29289C16.0976 8.68342 16.0976 9.31658 15.7071 9.70711L13.4142 12L15.7071 14.2929C16.0976 14.6834 16.0976 15.3166 15.7071 15.7071C15.3166 16.0976 14.6834 16.0976 14.2929 15.7071L12 13.4142L9.70711 15.7071C9.31658 16.0976 8.68342 16.0976 8.29289 15.7071C7.90237 15.3166 7.90237 14.6834 8.29289 14.2929L10.5858 12L8.29289 9.70711C7.90237 9.31658 7.90237 8.68342 8.29289 8.29289Z"
                                                                  fill="#12131A"/>
														</svg>
													</span>
                        <!--end::Svg Icon-->
                    </div>
                    <!--end::Close-->
                </div>
                <!--begin::Modal header-->
                <!--begin::Modal body-->
                <div class="modal-body scroll-y mx-5 mx-xl-18 pt-0 pb-15">
                    <!--begin::Heading-->
                    <div class="text-center mb-12">
                        <!--begin::Title-->
                        <div class="fs-2 fw-bolder mb-1">تغییر وضعیت</div>
                        <!--end::Title-->
                        <!--begin::Description-->
                        <div class="text-gray-400 fw-bold fs-3">وضعیت این تیکت را میتوانید تغییر دهید.</div>
                        <!--end::Description-->
                    </div>
                    <!--end::Heading-->
                    <!--begin::Users-->
                    <div class="mb-15">
                    @if($status)
                        <!--begin::Form Group-->
                            <div class="mb-10 position-relative">
                                <select class="select-arrow form-select form-select-lg form-select-solid"
                                        name="selected_status" id="modal_selected_status"
                                        wire:ignore
                                        data-control="select2"
                                        wire:model.live="selected_status"
                                        onchange="@this.selected_status = $(this).val(); $(this).attr('disabled','true').addClass('disabled'); $('.select-loading-spinner').removeClass('d-none'); $('.select-down-arrow').addClass('d-none');"
                                        data-placeholder="انتخاب کنید"
                                        dir="rtl">
                                    <option></option>
                                    @foreach($status as $data)
                                        <option value="{{$data["id"]}}"
                                        >{{$data["name"]}}</option>
                                    @endforeach
                                </select>
                                <span
                                    class="select-loading-spinner spinner-border spinner-border-sm align-middle ms-2"></span>
                                <span class="select-down-arrow"><i class="fas fa-chevron-down fs-6"></i></span>
                                @if ($errors->has('selected_status'))
                                    <span class="invalid-feedback d-block" role="alert">
                                     <strong>{{ $errors->first('selected_status') }}</strong>
                                </span>
                                @endif
                            </div>
                        @endif

                    </div>

                    <!--end::Users-->
                    <!--begin::Notice-->
                    <div class="d-flex justify-content-between align-items-center">
                        <!--begin::Label-->
                        <div class="fw-bold">
                            <div class="fs-7 text-gray-400">
                                بستن تیکت منوط به اطمینان از خاتمه یافتن موضوع مورد بحث باشد.
                            </div>
                        </div>
                        <button class="btn btn-danger btn-sm"
                                wire:click="$set('selected_status',{{$status_closed_id}})"> بستن تیکت
                        </button>
                        <!--end::Label-->

                    </div>
                    <!--end::Notice-->
                </div>
                <!--end::Modal body-->
            </div>
            <!--end::Modal content-->
        </div>
        <!--end::Modal dialog-->
    </div>
    <!--end::Modal - View Users-->

    <!--begin::Modal - View Users-->
    <div class="modal fade" id="kt_modal_change_team" tabindex="-1" aria-hidden="true" wire:ignore>
        <!--begin::Modal dialog-->
        <div class="modal-dialog mw-650px">
            <!--begin::Modal content-->
            <div class="modal-content">
                <!--begin::Modal header-->
                <div class="modal-header pb-0 border-0 justify-content-end">
                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-icon-primary" data-bs-dismiss="modal">
                        <!--begin::Svg Icon | path: icons/duotone/Interface/Close-Square.svg-->
                        <span class="svg-icon svg-icon-2x">
														<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                             viewBox="0 0 24 24" fill="none">
															<path opacity="0.25" fill-rule="evenodd" clip-rule="evenodd"
                                                                  d="M2.36899 6.54184C2.65912 4.34504 4.34504 2.65912 6.54184 2.36899C8.05208 2.16953 9.94127 2 12 2C14.0587 2 15.9479 2.16953 17.4582 2.36899C19.655 2.65912 21.3409 4.34504 21.631 6.54184C21.8305 8.05208 22 9.94127 22 12C22 14.0587 21.8305 15.9479 21.631 17.4582C21.3409 19.655 19.655 21.3409 17.4582 21.631C15.9479 21.8305 14.0587 22 12 22C9.94127 22 8.05208 21.8305 6.54184 21.631C4.34504 21.3409 2.65912 19.655 2.36899 17.4582C2.16953 15.9479 2 14.0587 2 12C2 9.94127 2.16953 8.05208 2.36899 6.54184Z"
                                                                  fill="#12131A"/>
															<path fill-rule="evenodd" clip-rule="evenodd"
                                                                  d="M8.29289 8.29289C8.68342 7.90237 9.31658 7.90237 9.70711 8.29289L12 10.5858L14.2929 8.29289C14.6834 7.90237 15.3166 7.90237 15.7071 8.29289C16.0976 8.68342 16.0976 9.31658 15.7071 9.70711L13.4142 12L15.7071 14.2929C16.0976 14.6834 16.0976 15.3166 15.7071 15.7071C15.3166 16.0976 14.6834 16.0976 14.2929 15.7071L12 13.4142L9.70711 15.7071C9.31658 16.0976 8.68342 16.0976 8.29289 15.7071C7.90237 15.3166 7.90237 14.6834 8.29289 14.2929L10.5858 12L8.29289 9.70711C7.90237 9.31658 7.90237 8.68342 8.29289 8.29289Z"
                                                                  fill="#12131A"/>
														</svg>
													</span>
                        <!--end::Svg Icon-->
                    </div>
                    <!--end::Close-->
                </div>
                <!--begin::Modal header-->
                <!--begin::Modal body-->
                <div class="modal-body scroll-y mx-5 mx-xl-18 pt-0 pb-15">
                    <!--begin::Heading-->
                    <div class="text-center mb-12">
                        <!--begin::Title-->
                        <div class="fs-2 fw-bolder mb-1">تغییر تیم پشتیبانی</div>
                        <!--end::Title-->
                        <!--begin::Description-->
                        <div class="text-gray-400 fw-bold fs-3">با تغییر تیم، این تیکت از دسترس تیم فعلی شما خارج خواهد
                            شد.
                        </div>
                        <!--end::Description-->
                    </div>
                    <!--end::Heading-->
                    <!--begin::Users-->
                    <div class="mb-15">
                    @if($support_team)
                        <!--begin::Form Group-->
                            <div class="mb-10 position-relative">
                                <select class="select-arrow form-select form-select-lg form-select-solid"
                                        name="selected_status" id="modal_selected_team"
                                        wire:ignore
                                        data-control="select2"
                                        wire:model.live="selected_team"
                                        onchange="@this.selected_team = $(this).val(); $(this).attr('disabled','true').addClass('disabled'); $('.select-loading-spinner').removeClass('d-none'); $('.select-down-arrow').addClass('d-none');"
                                        data-placeholder="انتخاب کنید"
                                        dir="rtl">
                                    <option></option>
                                    @foreach($support_team as $data)
                                        <option value="{{$data["id"]}}"
                                        >{{$data["name"]}}</option>
                                    @endforeach
                                </select>
                                <span
                                    class="select-loading-spinner spinner-border spinner-border-sm align-middle ms-2"></span>
                                <span class="select-down-arrow"><i class="fas fa-chevron-down fs-6"></i></span>
                                @if ($errors->has('selected_team'))
                                    <span class="invalid-feedback d-block" role="alert">
                                     <strong>{{ $errors->first('selected_team') }}</strong>
                                </span>
                                @endif
                            </div>
                        @endif

                    </div>

                    <!--end::Users-->

                </div>
                <!--end::Modal body-->
            </div>
            <!--end::Modal content-->
        </div>
        <!--end::Modal dialog-->
    </div>
    <!--end::Modal - View Users-->
    <!--begin::Messenger-->
    <div id="no-open-ticket" style="transform: translate(50%,-50%); font-weight: 800; color: #d1d2d4;"
         class="d-flex justify-content-center align-items-center flex-column-reverse position-absolute top-50 end-50 fs-2x @if(!empty($ticket)) d-none @endif">
        تیکت مورد نظر را انتخاب کنید
        <i style="font-size: 100px; color: #d1d2d4;" class="fas fa-comment-alt mb-10"></i>
    </div>

    <div class="d-none" id="skeleton-loading">
        <div class="flex-lg-row-fluid">
            <!--begin::Messenger-->
            <div class="card">
                <!--begin::Card header-->
                <div class="card-header">
                    <!--begin::Title-->
                    <div class="card-title">
                        <!--begin::User-->
                        <div class="d-flex justify-content-center flex-column me-3">
                            <span href="#"
                                  class="skeleton skeleton-title fs-4 fw-bolder text-gray-900 text-hover-primary me-1 mb-2 lh-1"></span>
                            <!--begin::Info-->
                            <span class="mb-0 lh-1 skeleton skeleton-status"></span>
                            <!--end::Info-->
                        </div>
                        <!--end::User-->
                    </div>
                    <!--end::Title-->
                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body skeleton-line position-relative overflow-hidden">
                    <!--begin::Messages-->
                    <div class="me-n5 pe-5 h-300px h-lg-auto">
                        <!--begin::Message(out)-->
                        <div class="d-flex justify-content-end mb-10">
                            <!--begin::Wrapper-->
                            <div class="d-flex flex-column align-items-end">
                                <!--begin::User-->
                                <div class="d-flex align-items-center mb-2">
                                    <!--begin::Details-->
                                    <div class="me-3 skeleton skeleton-title">
                                        <span class="text-muted fs-7 mb-1"></span>
                                    </div>
                                    <!--end::Details-->
                                    <!--begin::Avatar-->
                                    <div class="symbol symbol-35px symbol-circle">
                                        <img alt="Pic" src="{{ asset('assets/media/avatars/image.gif') }}">
                                    </div>
                                    <!--end::Avatar-->
                                </div>
                                <!--end::User-->
                                <!--begin::Text-->
                                <div
                                    class="skeleton skeleton-content-lg p-5 rounded text-dark fw-bold mw-lg-400px text-end"
                                    data-kt-element="message-text"></div>
                                <!--end::Text-->
                            </div>
                            <!--end::Wrapper-->
                        </div>
                        <!--end::Message(out)-->
                        <!--begin::Message(in)-->
                        <div class="d-flex justify-content-start mb-10">
                            <!--begin::Wrapper-->
                            <div class="d-flex flex-column align-items-start">
                                <!--begin::User-->
                                <div class="d-flex align-items-center mb-2">
                                    <!--begin::Avatar-->
                                    <div class="symbol symbol-35px symbol-circle">
                                        <img alt="Pic" src="{{ asset('assets/media/avatars/image.gif') }}">
                                    </div>
                                    <!--end::Avatar-->
                                    <!--begin::Details-->
                                    <div class="ms-3 skeleton skeleton-title">
                                        <span class="text-muted fs-7 mb-1"></span>
                                    </div>
                                    <!--end::Details-->
                                </div>
                                <!--end::User-->
                                <!--begin::Text-->
                                <div
                                    class="skeleton skeleton-content-lg p-5 rounded text-dark fw-bold mw-lg-400px text-start"
                                    data-kt-element="message-text"></div>
                                <!--end::Text-->
                            </div>
                            <!--end::Wrapper-->
                        </div>
                        <!--end::Message(in)-->
                    </div>
                    <!--end::Messages-->
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Messenger-->
        </div>
    </div>
    @push('scripts')
        <script>
            Livewire.on('show_ticket', () => {
                $('#skeleton-loading').removeClass('d-none');
                $('#no-open-ticket, #kt_chat_messenger').addClass('d-none');
            });
        </script>
    @endpush
    <div class="card  @if(empty($ticket)) d-none @endif" id="kt_chat_messenger">
        <!--begin::Card header-->
        <div class="card-header" id="kt_chat_messenger_header">
        @if(!empty($ticket))
            <!--begin::Title-->
                <div class="card-title">
                    <!--begin::User-->
                    <div class="d-flex justify-content-center flex-column me-3">
                        <a href="#"
                           class="fs-4 fw-bolder text-gray-900 text-hover-primary me-1 mb-2 lh-1">
                            {{$ticket->subject}}

                            <span class="badge badge-light-{{$ticket->priorityColor()}} me-3">{{Str::limit($ticket->priorityName(),10)}}</span>
                            <span class="badge badge-light-dark me-3">{{$ticket->support_teams->name}}</span>


                        </a>
                        <!--begin::Info-->
                        <div class="mb-0 lh-1">
                            <span class="badge badge-success badge-circle w-10px h-10px me-1"
                                  style="background-color: {{$ticket->statusColor()}}"></span>
                            <span class="fs-7 fw-bold text-gray-400">{{$ticket->statusName()}}</span>


                        </div>
                        <!--end::Info-->
                    </div>
                    <!--end::User-->
                </div>
                <!--end::Title-->
        @endif
        <!--begin::Card toolbar-->
            <div class="card-toolbar">
                <div class="d-flex align-items-center me-1 my-2">
                    <a wire:click="open_tag()"
                       class="btn btn-icon btn-active-light-primary btn-sm me-2" data-bs-toggle="tooltip" title=""
                       data-bs-original-title="اضافه کردن تگ">
                        <i class="fas fa-tags fs-6"></i>
                    </a>
                    @push('scripts')
                        <script>
                            //set tagify from livewire
                             Livewire.on('render_tagify', () => {
                                $('.tooltip').hide();
                                $('[data-bs-toggle="tooltip"]').tooltip();
                                if (typeof window.tagify !== "undefined") {
                                    window.tagify.destroy();
                                }
                                let input = document.querySelector('#kt_tagify__hamed');
                                // The DOM elements you wish to replace with Tagify
                                $('#kt_tagify__hamed').val(@this.tag_string);
                                console.log(@this.tag_string);
                                // Initialize Tagify script on the above inputs
                                window.tagify = new Tagify(input, {
                                    whitelist: {!! $all_tags !!},
                                    enforceWhitelist: true,
                                    placeholder: "لطفا تگ را انتخاب نمایید",
                                    maxTags: 10,
                                    dropdown: {
                                        maxItems: 20,           // <- mixumum allowed rendered suggestions
                                        classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
                                        enabled: 0,             // <- show suggestions on focus
                                        closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected
                                    }
                                });
                                input.addEventListener('change', onChange)

                                function onChange(e) {
                                    // outputs a String
                                    console.log(e.target.value)
                                @this.set('ticket_tags', e.target.value);
                                }

                                $('#kt_modal_add_new_tag').modal('show');
                            });
                        </script>
                    @endpush

                    <a wire:click="change_team()" class="btn btn-icon btn-active-light-primary btn-sm me-2"
                       data-bs-toggle="tooltip"
                       title="" data-bs-original-title="ارجا به تبم دیگر">
                        <i class="fas fa-reply fs-6"></i>
                    </a>

                    @push('scripts')
                        <script>
                             Livewire.on('change_team', param => {
                                $('.tooltip').hide();
                                $('#kt_modal_change_team').modal('show');
                                $('#modal_selected_team').removeAttr('disabled').removeClass('disabled');
                                $('.select-down-arrow').removeClass('d-none');
                                $('.select-loading-spinner').addClass('d-none');
                                $('[data-bs-toggle="tooltip"]').tooltip();
                            });
                        </script>
                    @endpush

                    <a href="#" class="btn btn-icon btn-active-light-primary btn-sm me-2" data-bs-toggle="tooltip"
                       title="" data-bs-original-title="ساخت ایده جدید">
                        <i class="fas fa-lightbulb fs-6"></i>
                    </a>

                    <a wire:click="change_status()" class="btn btn-icon btn-active-light-primary btn-sm me-2"
                       data-bs-toggle="tooltip"
                       title="" data-bs-original-title="تغییر وضعیت">
                        <i class="fas fa-times-circle fs-6"></i>
                    </a>

                    @push('scripts')
                        <script>
                             Livewire.on('change_status', param => {
                                $('.tooltip').hide();
                                $('#kt_modal_change_status').modal('show');
                                $('#modal_selected_status').removeAttr('disabled').removeClass('disabled');
                                $('.select-down-arrow').removeClass('d-none');
                                $('.select-loading-spinner').addClass('d-none');
                                $('[data-bs-toggle="tooltip"]').tooltip();
                            });
                        </script>
                    @endpush

                    <a wire:click="change_priority()" class="btn btn-icon btn-active-light-primary btn-sm me-2"
                       data-bs-toggle="tooltip"
                       title="" data-bs-original-title="تغییر اولویت">
                        <i class="fas fa-procedures fs-6"></i>
                    </a>

                    @push('scripts')
                        <script>
                             Livewire.on('change_priority', param => {

                                $('.tooltip').hide();
                                $('#kt_modal_change_priority').modal('show');
                                $('#modal_support_team').removeAttr('disabled').removeClass('disabled');
                                $('.select-down-arrow').removeClass('d-none');
                                $('.select-loading-spinner').addClass('d-none');
                                $('[data-bs-toggle="tooltip"]').tooltip();
                            });
                        </script>
                    @endpush
                </div>
                <!--begin::Menu-->
                <div class="me-n3">
                    <button class="btn btn-sm btn-icon btn-active-light-primary" data-kt-menu-trigger="click"
                            data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                        <i class="bi bi-three-dots fs-2"></i>
                    </button>
                    <!--begin::Menu 3-->
                    <div
                        class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-bold w-200px py-3 me-4"
                        data-kt-menu="true">
                        <!--begin::Heading-->
                        <div class="menu-item px-3">
                            <div class="menu-content text-muted pb-2 px-3 fs-7 text-uppercase">تنظیمات</div>
                        </div>
                        <!--end::Heading-->
                        <!--begin::Menu item-->
                        <div class="menu-item px-3">
                            <a href="#" class="menu-link px-3" data-bs-toggle="modal"
                               data-bs-target="#kt_modal_users_search">Add Contact</a>
                        </div>
                        <!--end::Menu item-->
                        <!--begin::Menu item-->
                        <div class="menu-item px-3">
                            <a href="#" class="menu-link flex-stack px-3" data-bs-toggle="modal"
                               data-bs-target="#kt_modal_invite_friends">Invite Contacts
                                <i class="fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="tooltip" title=""
                                   data-bs-original-title="Specify a contact email to send an invitation"
                                   aria-label="Specify a contact email to send an invitation"></i></a>
                        </div>
                        <!--end::Menu item-->
                        <!--begin::Menu item-->
                        <div class="menu-item px-3" data-kt-menu-trigger="hover"
                             data-kt-menu-placement="right-start" data-kt-menu-flip="left, center, top">
                            <a href="#" class="menu-link px-3">
                                <span class="menu-title">Groups</span>
                                <span class="menu-arrow"></span>
                            </a>
                            <!--begin::Menu sub-->
                            <div class="menu-sub menu-sub-dropdown w-175px py-4">
                                <!--begin::Menu item-->
                                <div class="menu-item px-3">
                                    <a href="#" class="menu-link px-3" data-bs-toggle="tooltip" title=""
                                       data-bs-original-title="Coming soon">Create Group</a>
                                </div>
                                <!--end::Menu item-->
                                <!--begin::Menu item-->
                                <div class="menu-item px-3">
                                    <a href="#" class="menu-link px-3" data-bs-toggle="tooltip" title=""
                                       data-bs-original-title="Coming soon">Invite Members</a>
                                </div>
                                <!--end::Menu item-->
                                <!--begin::Menu item-->
                                <div class="menu-item px-3">
                                    <a href="#" class="menu-link px-3" data-bs-toggle="tooltip" title=""
                                       data-bs-original-title="Coming soon">Settings</a>
                                </div>
                                <!--end::Menu item-->
                            </div>
                            <!--end::Menu sub-->
                        </div>
                        <!--end::Menu item-->
                        <!--begin::Menu item-->
                        <div class="menu-item px-3 my-1">
                            <a href="#" class="menu-link px-3" data-bs-toggle="tooltip" title=""
                               data-bs-original-title="Coming soon">Settings</a>
                        </div>
                        <!--end::Menu item-->
                    </div>
                    <!--end::Menu 3-->
                </div>
                <!--end::Menu-->
            </div>
            <!--end::Card toolbar-->
        </div>
        <!--end::Card header-->


    @if(!empty($ticket))
        <!--begin::Card body-->
            <div class="card-body" id="kt_chat_messenger_body" wire:loading.class="opacity-05">
                <!--begin::Messages-->
                <div class="scroll-offset-bottom scroll-y me-n5 pe-5 h-300px h-lg-auto" data-kt-element="messages" data-kt-scroll="true"
                     data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto"
                     data-kt-scroll-dependencies="#kt_header, #kt_toolbar, #kt_footer, #kt_chat_messenger_header, #kt_chat_messenger_footer"
                     data-kt-scroll-wrappers="#kt_content, #kt_chat_messenger_body" data-kt-scroll-offset="-2px"
                     style="max-height: 50vh"
                >

                    <!-- starting summary -->
                    <div class="d-flex justify-content-start mb-10">
                        <!--begin::Wrapper-->
                        <div class="d-flex flex-column align-items-start">
                            <!--begin::User-->
                            <div class="d-flex align-items-center mb-2">
                                <!--begin::Avatar-->
                                <div class="symbol symbol-35px symbol-circle">
                                    <img alt="Pic" src="{{ $ticket->user->profile_photo_url() }}">
                                </div>
                                <!--end::Avatar-->
                                <!--begin::Details-->
                                <div class="ms-3">
                                    <a href="#"
                                       class="fs-5 fw-bolder text-gray-900 text-hover-primary me-1">{{$ticket->user->first_name}}</a>
                                    <span
                                        class="text-muted fs-7 mb-1">{{jdate($ticket->created_at)->format("Y/m/d  H:i:s")}}</span>
                                </div>
                                <!--end::Details-->
                            </div>
                            <!--end::User-->
                            <!--begin::Text-->
                            <div class="p-5 rounded bg-light-info text-dark fw-bold mw-lg-400px text-start"
                                 data-kt-element="message-text">      {{$ticket->summary}}</div>
                            <!--end::Text-->
                        </div>
                        <!--end::Wrapper-->
                    </div>
                    <!-- end summary -->

                    @forelse($ticket->commentsAndNotesAndEventsAndAttachments()->sortBy('created_at') as $comment)

                        @if($comment instanceof App\Models\Ticket\TicketEvent)
                            <span class="badge badge-light-dark fs-9 d-flex flex-center mb-3">
                                {{__('event.'.$comment->event_name())}} در
                                {{$comment->ago_time()}}

                            </span>
                        @elseif($comment instanceof App\Models\Ticket\TicketAttachment)



                            @if(($comment->user_id == $user->id))
                                <div class="d-flex justify-content-end mb-10 flex-wrap flex-column align-content-end">
                            @else
                              <div>
                            @endif
                                      <span class="file_holder fs-9 d-flex mw-lg-150px mb-3 align-items-center ">
                                <i class="fas fa-file"></i>
                              <a href="{{$comment->path}}" target="_blank">
                                 <b>{{ substr($comment->path,strlen($comment->path) - 10 ,strlen($comment->path))}}</b>
                              <br/>
                                   <span class="file_holer_size">{{$comment->size}} KB - دانلود</span>

                              </a>

                            </span>
                                <span class="file_holder_time">
                                <i class="far fa-clock"></i>
                                 {{jdate($comment->created_at)->format("Y/m/d  H:i:s")}}
                            </span>
                             </div>
                            @else
                                @if(($comment->user->id == $user->id))
                                    <div class="d-flex justify-content-end mb-10">
                                        <!--begin::Wrapper-->
                                        <div class="d-flex flex-column align-items-end">
                                            <!--begin::User-->
                                            <div class="d-flex align-items-center mb-2">
                                                <!--begin::Details-->
                                                <div class="me-3">
                                            <span
                                                class="text-muted fs-7 mb-1 direction-rtl"> {{jdate($comment->created_at)->format("Y/m/d  H:i:s")}}</span>
                                                    <a href="#"
                                                       class="fs-5 fw-bolder text-gray-900 text-hover-primary ms-1">{{$comment->user->first_name}}</a>
                                                </div>
                                                <!--end::Details-->
                                                <!--begin::Avatar-->
                                                <div class="symbol symbol-35px symbol-circle">
                                                    <img alt="Pic" src="{{ $comment->user->profile_photo_url() }}">
                                                </div>
                                                <!--end::Avatar-->
                                            </div>
                                            <!--end::User-->
                                            <!--begin::Text-->
                                            <div
                                                class="p-5 rounded bg-light-primary text-dark fw-bold mw-lg-400px text-end"
                                                data-kt-element="message-text">
                                                {{$comment->body}}
                                            </div>
                                            <!--end::Text-->
                                        </div>
                                        <!--end::Wrapper-->
                                    </div>
                                @else
                                <!-- starting summary -->
                                    <div class="d-flex justify-content-start mb-10">
                                        <!--begin::Wrapper-->
                                        <div
                                            class="d-flex flex-column align-items-{{($comment->user->id == $user->id) ? "start" : "end"}}">
                                            <!--begin::User-->
                                            <div class="d-flex align-items-center mb-2">
                                                <!--begin::Avatar-->
                                                <div class="symbol symbol-35px symbol-circle">
                                                    <img alt="Pic" src="{{ $comment->user->profile_photo_url() }}">
                                                </div>
                                                <!--end::Avatar-->
                                                <!--begin::Details-->
                                                <div class="ms-3">
                                                    <a href="#"
                                                       class="fs-5 fw-bolder text-gray-900 text-hover-primary me-1">{{$comment->user->first_name}}</a>
                                                    <span
                                                        class="text-muted fs-7 mb-1">{{jdate($comment->created_at)->format("Y/m/d  H:i:s")}}</span>
                                                </div>
                                                <!--end::Details-->
                                            </div>
                                            <!--end::User-->
                                            <!--begin::Text-->
                                            <div
                                                class="p-5 rounded bg-light-info text-dark fw-bold mw-lg-400px text-start"
                                                data-kt-element="message-text">      {{$comment->body}}</div>
                                            <!--end::Text-->
                                        </div>
                                        <!--end::Wrapper-->
                                    </div>
                                    <!-- end summary -->
                                @endif
                            @endif
                            @empty
                                پاسخی ثبت نشده است
                            @endforelse

                </div>

            </div>
            <!--end::Card body-->
        @endif
    <!--begin::Card footer-->
        <div class="card-footer pt-4" id="kt_chat_messenger_footer">
            <div class="w-100" id="uploading_message" style="display: none">
                <div class="progress">
                    <div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0"
                         aria-valuemax="100"></div>
                </div>
                <div class="progress-msg"></div>
            </div>
            <!--begin::Input-->
            <textarea class="form-control form-control-flush mb-3" rows="1" data-kt-element="input"
                      placeholder="متن خود را وارد نمایید" wire:model="comment"></textarea>
            <!--end::Input-->
            <!--begin:Toolbar-->
            <div class="d-flex flex-stack">
                @if(!empty($ticket))
                    <livewire:ticket.file-upload :ticket="$ticket->id"/>
                @endif
                <!--begin::Send-->
                <button class="btn btn-primary" wire:click="submit_new_comment()" type="button" data-kt-element="send">
                    ارسال
                </button>
                <!--end::Send-->
            </div>
            <!--end::Toolbar-->
        </div>
        <!--end::Card footer-->
    </div>
    <!--end::Messenger-->

    {{--    @endif--}}
</div>
