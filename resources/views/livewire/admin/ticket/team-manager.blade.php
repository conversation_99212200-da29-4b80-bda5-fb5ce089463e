<div class="d-flex flex-wrap">
    {{-- The best athlete wants his opponent at his best. --}}
    <div wire:ignore.self class="modal fade modal-full-height modal-center" tab-index="-1" id="add_user_to_team"
         aria-hidden="true">
        <div class="card card-body modal-dialog">
            <div class="modal-content">
                <div class="modal-header direction-rtl">
                    <h5 class="modal-title">اضافه کردن افراد</h5>
                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                         aria-label="Close">
                            <span class="svg-icon svg-icon-2x">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                     width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g transform="translate(12.000000, 12.000000) rotate(-45.000000) translate(-12.000000, -12.000000) translate(4.000000, 4.000000)"
                                       fill="#000000">
                                        <rect fill="#000000" x="0" y="7" width="16" height="2" rx="1"/>
                                        <rect fill="#000000" opacity="0.5"
                                              transform="translate(8.000000, 8.000000) rotate(-270.000000) translate(-8.000000, -8.000000)"
                                              x="0" y="7" width="16" height="2" rx="1"/>
                                    </g>
                                </svg>
                            </span>
                    </div>
                    <!--end::Close-->
                </div>
                <!--begin::Modal body-->
                <div class="modal-body scroll-y mx-5 mx-xl-18 pt-0 pb-15 mt-6">
                    <!--begin::Content-->
                    <div class="text-center mb-12">
                        <div class="fs-2 fw-bolder mb-1">جستجوی افراد</div>
                        <div class="text-gray-400 fw-bold fs-3">اضافه کردن افراد به تیم پشتیبانی</div>
                    </div>
                    <!--end::Content-->
                    <!--begin::Search-->
                    <div id="kt_modal_users_search_handler" data-kt-search-keypress="true" data-kt-search-min-length="2"
                         data-kt-search-enter="enter" data-kt-search-layout="inline">
                        <!--begin::Form-->
                        <form data-kt-search-element="form" class="w-100 position-relative mb-5" autocomplete="off">
                            <!--begin::Hidden input(Added to disable form autocomplete)-->
                            <input type="hidden"/>
                            <!--end::Hidden input-->
                            <!--begin::Icon-->
                            <!--begin::Svg Icon | path: icons/duotone/General/Search.svg-->
                            <span
                                class="svg-icon svg-icon-2 svg-icon-lg-1 svg-icon-gray-500 position-absolute top-50 ms-5 translate-middle-y">
															<svg xmlns="http://www.w3.org/2000/svg"
                                                                 xmlns:xlink="http://www.w3.org/1999/xlink" width="24px"
                                                                 height="24px" viewBox="0 0 24 24" version="1.1">
																<g stroke="none" stroke-width="1" fill="none"
                                                                   fill-rule="evenodd">
																	<rect x="0" y="0" width="24" height="24"/>
																	<path
                                                                        d="M14.2928932,16.7071068 C13.9023689,16.3165825 13.9023689,15.6834175 14.2928932,15.2928932 C14.6834175,14.9023689 15.3165825,14.9023689 15.7071068,15.2928932 L19.7071068,19.2928932 C20.0976311,19.6834175 20.0976311,20.3165825 19.7071068,20.7071068 C19.3165825,21.0976311 18.6834175,21.0976311 18.2928932,20.7071068 L14.2928932,16.7071068 Z"
                                                                        fill="#000000" fill-rule="nonzero"
                                                                        opacity="0.3"/>
																	<path
                                                                        d="M11,16 C13.7614237,16 16,13.7614237 16,11 C16,8.23857625 13.7614237,6 11,6 C8.23857625,6 6,8.23857625 6,11 C6,13.7614237 8.23857625,16 11,16 Z M11,18 C7.13400675,18 4,14.8659932 4,11 C4,7.13400675 7.13400675,4 11,4 C14.8659932,4 18,7.13400675 18,11 C18,14.8659932 14.8659932,18 11,18 Z"
                                                                        fill="#000000" fill-rule="nonzero"/>
																</g>
															</svg>
														</span>
                            <!--end::Svg Icon-->
                            <!--end::Icon-->
                            <!--begin::Input-->
                            <input type="text" wire:model.live="search_term"
                                   class="form-control form-control-lg form-control-solid px-15" name="search" value=""
                                   placeholder="نام و یا نام خانوادگی  را وارد نمایید" data-kt-search-element="input"/>
                            <!--end::Input-->
                            <!--begin::Spinner-->
                            <span class="position-absolute top-50 end-0 translate-middle-y lh-0 d-none me-5"
                                  data-kt-search-element="spinner">
															<span
                                                                class="spinner-border h-15px w-15px align-middle text-gray-400"></span>
														</span>
                            <!--end::Spinner-->
                            <!--begin::Reset-->
                            <span
                                class="btn btn-flush btn-active-color-primary position-absolute top-50 end-0 translate-middle-y lh-0 me-5 d-none"
                                data-kt-search-element="clear">
															<!--begin::Svg Icon | path: icons/duotone/Navigation/Close.svg-->
															<span class="svg-icon svg-icon-2 svg-icon-lg-1 me-0">
																<svg xmlns="http://www.w3.org/2000/svg"
                                                                     xmlns:xlink="http://www.w3.org/1999/xlink"
                                                                     width="24px" height="24px" viewBox="0 0 24 24"
                                                                     version="1.1">
																	<g transform="translate(12.000000, 12.000000) rotate(-45.000000) translate(-12.000000, -12.000000) translate(4.000000, 4.000000)"
                                                                       fill="#000000">
																		<rect fill="#000000" x="0" y="7" width="16"
                                                                              height="2" rx="1"/>
																		<rect fill="#000000" opacity="0.5"
                                                                              transform="translate(8.000000, 8.000000) rotate(-270.000000) translate(-8.000000, -8.000000)"
                                                                              x="0" y="7" width="16" height="2" rx="1"/>
																	</g>
																</svg>
															</span>
                                <!--end::Svg Icon-->
														</span>
                            <!--end::Reset-->
                        </form>
                        <!--end::Form-->
                        <!--begin::Wrapper-->
                        <div class="py-5">
                            <!--begin::Suggestions-->
                            <div class="@if(empty(!$search_term)) d-none  @endif" data-kt-search-element="suggestions">
                                <!--begin::Heading-->
                                <h3 class="fw-bold mb-5">کاربران این تیم:</h3>
                                <!--end::Heading-->
                                <!--begin::Users-->
                                <div class="mh-375px scroll-y me-n7 pe-7">
                                @if($members_from_team)
                                    @forelse($members_from_team as $user)
                                        <!--begin::User-->
                                            <div class="rounded d-flex flex-stack bg-active-lighten p-4  bg-state-light bg-state-opacity-50 mb-1"
                                                 data-user-id="0">
                                                <div class="d-flex align-items-center">

                                                        <!--begin::Avatar-->
                                                        <div class="symbol symbol-35px symbol-circle me-5">
                                                            <img alt="Pic" src="{{$user->profile_photo}}"/>
                                                        </div>
                                                        <!--end::Avatar-->
                                                        <!--begin::Info-->
                                                        <div class="fw-bold">
                                                            <span
                                                                class="fs-6 text-gray-800 me-2">{{$user->full_name}}</span>
                                                            <span class="badge badge-light">اپراتور تیم</span>
                                                        </div>
                                                        <!--end::Info-->


                                                </div>
                                                <!--begin::Access menu-->
                                                <div class="ms-4 w-100px">
                                                    <button wire:click="remove_user_to_team({{$user->id}})"
                                                            class="btn btn-warning">
                                                        حذف
                                                    </button>
                                                </div>
                                                <!--end::Access menu-->
                                            </div>
                                            <!--end::User-->
                                        @empty
                                            لطفا به این تیم افرادی را اضافه نمایید.
                                        @endforelse
                                    @endif


                                </div>
                                <!--end::Users-->
                            </div>
                            <!--end::Suggestions-->
                        @if(!empty($searched_users) && !empty($search_term) )
                            <!--begin::Results(add d-none to below element to hide the users list by default)-->
                                <div data-kt-search-element="results" class="">
                                    <!--begin::Users-->
                                    <div class="mh-375px scroll-y me-n7 pe-7">

                                    @foreach($searched_users as $user)
                                        <!--begin::User-->
                                            <div class="rounded d-flex flex-stack bg-active-lighten p-4"
                                                 data-user-id="0">
                                                <!--begin::Details-->
                                                <div class="d-flex align-items-center">
                                                    <!--begin::Checkbox-->

                                                    <!--end::Checkbox-->
                                                    <!--begin::Avatar-->
                                                    <div class="symbol symbol-35px symbol-circle">
                                                        <img alt="Pic" src="{{$user['profile_photo']}}"/>
                                                    </div>
                                                    <!--end::Avatar-->
                                                    <!--begin::Details-->
                                                    <div class="ms-5">
                                                        <a href="#"
                                                           class="fs-5 fw-boldest text-gray-900 text-hover-primary mb-2"
                                                        >
                                                            {{$user['full_name']}}
                                                        </a>
                                                        <div class="fw-bold text-gray-400">
                                                            {{$user['email']}}
                                                        </div>
                                                    </div>
                                                    <!--end::Details-->
                                                </div>
                                                <!--end::Details-->
                                                <!--begin::Access menu-->
                                                <div class="ms-2 w-100px">
                                                    <button wire:click="add_user_to_team({{$user['id']}})"
                                                            class="btn btn-primary">
                                                        انتخاب
                                                    </button>
                                                </div>
                                                <!--end::Access menu-->
                                            </div>
                                            <!--end::User-->
                                            <!--begin::Separator-->
                                            <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                                            <!--end::Separator-->
                                        @endforeach

                                    </div>
                                    <!--end::Users-->
                                    <!--begin::Actions-->

                                    <!--end::Actions-->
                                </div>
                        @endif
                        <!--end::Results-->
                        @if(empty($searched_users) && !empty($search_term) )
                            <!--begin::Empty-->
                                <div data-kt-search-element="empty"
                                     class="text-center ">
                                    <!--begin::Message-->
                                    <div class="fw-bold py-10">
                                        <div class="text-gray-600 fs-3 mb-2">کاربری با این مشخصات یافت نشد</div>
                                        <div class="text-gray-400 fs-6">میتوانید از ادرس ایمیل و یا کد ملی و یا اسم
                                            انگلیسی نیز جستجو کنید
                                        </div>
                                    </div>
                                    <!--end::Message-->
                                    <!--begin::Illustration-->
                                    <div class="text-center px-4">
                                        <img src="/start/assets/media/illustrations/alert-2.png" alt=""
                                             class="mw-100 mh-200px"/>
                                    </div>
                                    <!--end::Illustration-->
                                </div>
                                <!--end::Empty-->
                            @endif
                        </div>
                        <!--end::Wrapper-->
                    </div>
                    <!--end::Search-->
                </div>
                <!--end::Modal body-->
                <div class="modal-footer">
                    <div class="fv-row direction-rtl">
                        <button type="button" class="btn btn-light me-3"
                                data-bs-toggle="modal" wire:click="cancel()" role="button" aria-expanded="false"
                                aria-controls="add_roles">
                            انصراف
                        </button>

                    </div>
                </div>
            </div>
        </div>
    </div>

    @foreach($teams as $team)
        <h1 class="anchor fw-bolder mb-5 justify-content-between p-2 flex-nowrap text-truncate border rounded-2 mx-2 fs-4" id="content-blocking" style="width: calc(20% - 1rem);">
            {{-- <a href="#c{{$team->id}}"></a> --}}
            <span class="text-truncate pe-6" data-bs-toggle="tooltip" data-bs-placement="top" title="{{$team->name}}"> {{$team->name}} </span>

            <button class="btn btn-light-primary py-2" type="button" data-bs-toggle="modal" data-bs-target="#add_user_to_team"
                    aria-controls="add_user_to_team" wire:click="set_team_id({{$team->id}})">
                افراد
            </button>
        </h1>
    @endforeach

</div>
