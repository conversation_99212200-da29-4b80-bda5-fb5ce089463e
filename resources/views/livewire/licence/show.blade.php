<div>
    <div class="card">
        <!--begin::Card header-->
        <div class="card-header">
            <!--begin::Card header-->
            <div class="card-title fs-3 fw-bold">لایسنس شما (شناسه: {{($licence->hashId)}})</div>
            <!--end::Card header-->
        </div>
        <!--end::Card header-->

        <!--begin::Card body-->
        <div class="card-body">
            <!--begin::Row-->
            <div class="row mb-8">
                <!--begin::Col-->
                <div class="col-xl-3">
                    <div class="fs-6 fw-semibold mt-2 mb-3">میزان استفاده</div>
                </div>
                <!--end::Col-->

                <!--begin::Col-->
                <div class="col-xl-9">
                    <!--begin::Progress-->
                    <div class="d-flex flex-column">
                        <div class="d-flex justify-content-between w-100 fs-4 fw-bold mb-3">
                            <span> میزان مصرف</span>
                            <span>  {{$licence->totalDays - $licence->remindDays}} از  {{$licence->totalDays}} </span>
                        </div>

                        <div class="h-8px bg-light rounded mb-3">
                            <div class="bg-success rounded h-8px" role="progressbar"
                                 style="width: {{$licence->usedDaysPercentage}}%;"
                                 aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>

                        <div class=" fw-semibold text-gray-600">
                            روز های باقی مانده
                            {{$licence->remindDays}}
                        </div>
                    </div>
                    <!--end::Progress-->
                </div>
                <!--end::Col-->
            </div>
            <!--end::Row-->

            <!--begin::Row-->
            <div class="row mb-8">
                <!--begin::Col-->
                <div class="col-xl-3">
                    <div class="fs-6 fw-semibold mt-2 mb-3">ارتقا لایسنس</div>
                </div>
                <!--end::Col-->

                <!--begin::Col-->
                <div class="col-xl-9">
                    <!--begin::Row-->
                    <div class="row g-9" data-kt-buttons="true" data-kt-buttons-target="[data-kt-button]"
                         data-kt-initialized="1">
                        <!--begin::Col-->
                        <div class="col-md-4 col-lg-12 col-xxl-4" wire:click="upgradeLicence(false)">
                            <label
                                class="btn btn-outline btn-outline-dashed btn-active-light-primary active d-flex text-start p-6"
                                data-kt-button="true">
                                <!--begin::Radio button-->
                                <span
                                    class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                    <input class="form-check-input" type="radio" name="usage" value="1"
                                           checked="checked">
                                </span>
                                <!--end::Radio button-->

                                <span class="ms-5">
                                    <span
                                        class="fs-4 fw-bold mb-1 d-block">{{$licence->detail->title}}  {{$licence->detail->persianPeriod}}</span>
                                    <span class="fw-semibold fs-7 text-gray-600">
                                        لایسنس فعلی شما
                                    </span>
                                </span>
                            </label>
                        </div>
                        <!--end::Col-->
                        @foreach($licence->detail->upgrades as $upgradeLicence)
                            <!--begin::Col-->
                            <div class="col-md-4 col-lg-12 col-xxl-4"
                                 wire:click="upgradeLicence('{{$upgradeLicence->slug}}')">
                                <label
                                    class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6"
                                    data-kt-button="true">
                                    <!--begin::Radio button-->
                                    <span
                                        class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                    <input class="form-check-input" type="radio" name="usage" value="2">
                                </span>
                                    <!--end::Radio button-->
                                    <span class="ms-5">
                                    <span
                                        class="fs-4 fw-bold mb-1 d-block">{{$upgradeLicence->title ." ". $upgradeLicence->persianPeriod}}</span>
                                    <span class="fw-semibold fs-7 text-gray-600">
                                          {{number_format($upgradeLicence->price,0)}} تومان برای {{$upgradeLicence->periodInMinutes / (24*60)}}  روز
                                    </span>
                                </span>
                                </label>
                            </div>
                            <!--end::Col-->
                        @endforeach



                        <!--begin::Col-->

                        <!--end::Col-->
                    </div>
                    <!--end::Row-->
                </div>
                <!--end::Col-->
            </div>
            <!--end::Row-->


            <!--begin::Row-->
            <div class="row mb-8">
                <!--begin::Col-->
                <div class="col-xl-3">
                    <div class="fs-6 fw-semibold mt-2 mb-3">هزینه دوره ای لایسنس شما</div>
                </div>
                <!--end::Col-->

                <!--begin::Col-->
                <div class="col-xl-9">
                    <!--begin::Dialer-->
                    {{number_format($licence->detail->price,0)}} تومان برای یک
                    دوره {{$licence->detail->periodInMinutes / (24*60)}} روزه
                    <!--end::Dialer-->
                </div>
                <!--end::Col-->
            </div>
            <!--end::Row-->

            <!--begin::Row-->
            <div class="row mb-8">
                <!--begin::Col-->
                <div class="col-xl-3">
                    <div class="fs-6 fw-semibold mt-2 mb-3">تاریخ شروع لایسنس</div>
                </div>
                <!--end::Col-->

                <!--begin::Col-->
                <div class="col-xl-9">
                    <!--begin::Dialer-->
                    {{\Morilog\Jalali\Jalalian::fromDateTime($licence->createdAt)->format('d F Y')}}
                    <!--end::Dialer-->
                </div>
                <!--end::Col-->
            </div>
            <!--end::Row-->


            <!--begin::Row-->
            <div class="row mb-8">
                <!--begin::Col-->
                <div class="col-xl-3">
                    <div class="fs-6 fw-semibold mt-2 mb-3">تمدید خودکار</div>
                </div>
                <!--end::Col-->

                <!--begin::Col-->
                <div class="col-xl-9">
                    <!--begin::Switch-->
                    <div class="form-check form-switch form-check-custom form-check-solid disabled">
                        <input class="form-check-input" type="checkbox" value="" id="allowchanges">
                        <label class="form-check-label  fw-semibold text-gray-400 ms-3" for="allowchanges">
                            غیرفعال
                        </label>
                    </div>
                    <!--end::Switch-->
                </div>
                <!--end::Col-->
            </div>

            <!--begin::Row-->
            <div class="row mb-8">
                <!--begin::Col-->
                <div class="col-xl-3">
                    <div class="fs-6 fw-semibold mt-2 mb-3">افزودنی ها</div>
                </div>
                <!--end::Col-->

                <!--begin::Col-->
                <div class="col-xl-9">
                    <!--begin::Switch-->

                    <div class="modal fade" tabindex="-1" id="kt_modal_PAG">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3 class="modal-title">نگه داری فایل ها</h3>

                                    <!--begin::Close-->
                                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2"
                                         data-bs-dismiss="modal" aria-label="Close">
                                        <i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span
                                                class="path2"></span></i>
                                    </div>
                                    <!--end::Close-->
                                </div>

                                <div class="modal-body">
                                    <img src="{{asset('assets/media/svg/landing_hero-pag.png')}}"
                                         class="theme-light-show d-block w-200px m-auto" alt=""/>
                                    <p> بر اساس لایسنس شما ما به مدت {{$licence->max_keep_file}} روز بصورت رایگان فایل
                                        های شما
                                        را ذخیره و نگه داری خواهیم کرد که این زمان تا ۹۰ درصد نیاز کاربران را پوشش خواهد
                                        داد.
                                        <br/>
                                        اما در صورتیکه بخواهید بیش از این زمان فایل های خود را نگه داری کنید میتوانید
                                        قابلیت نگه داری طولانی مدت
                                        (PAY AS YOU GO)
                                        را فعال کنید.
                                        در این قابلیت بصورت روزانه هزینه فایل های شما محاسبه و از اعتبار حساب کاربری شما
                                        کاسته خواهد شد.
                                        برای جلوگیری از بروز مشکل پیشنهاد میکنیم حداقل ۱۰۰ هزار تومان اعتبار در کیف پول
                                        ذخیره داشته باشید تا اعتبار شما منفی نشود.
                                        <br/>
                                        در صورت منفی شدن حساب کاربری شما، بعد از ۳ روز مجبور به حذف فایل های شما خواهیم
                                        شد و امکان بازیابی آن ها وجود نخواهد داشت.
                                    </p>

                                    <div class="table-responsive">
                                        <table class="table align-middle table-borderless">
                                            <thead>
                                            <tr class="border-bottom-1 border-bottom-gray-100 fw-bold text-muted fs-6 text-uppercase">
                                                <th class="pt-5 pb-1 ps-0 "></th>
                                                <th class="pt-5 pb-1 text-end">واحد</th>
                                                <th class="pt-5 pb-1 text-end">قیمت</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <td class="fw-bold ps-0 pt-10">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fa fa-genderless text-danger fs-1 me-2"></i>
                                                        فضای ذخیره‌سازی
                                                    </div>
                                                </td>
                                                <td class="text-end pt-10">هرمگابایت</td>
                                                <td class="text-end pt-10">۴۰۰ ریال (۴۰ تومان) ماهیانه</td>
                                            </tr>
                                            <tr>
                                                <td class="fw-bold ps-0">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fa fa-genderless text-success fs-1 me-2"></i>
                                                        ترافیک
                                                    </div>
                                                </td>
                                                <td class="text-end">هرمگابایت</td>
                                                <td class="text-end">رایگان!</td>
                                            </tr>
                                            <tr>
                                                <td class="fw-bold ps-0">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fa fa-genderless text-primary fs-1 me-2"></i>
                                                        درخواست
                                                    </div>
                                                </td>
                                                <td class="text-end">نامحدود</td>
                                                <td class="text-end">با رعایت مصرف منصفانه</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="modal-footer">
                                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">بستن</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="rounded border p-5 mb-5">
                        <div class="d-flex flex-stack">
                            <!--begin::Label-->
                            <div class="me-5 text-align-right direction-rtl">
                                <label class="fs-6 fw-bold form-label">
                                    نگه داری فایل ها
                                </label>
                                <div class="fs-7 fw-bold text-muted">
                                    به میزان دلخواه فایل های شما در سیستم ما خواهد ماند و هزینه آن به ازای مصرف روزانه
                                    محاسبه خواهد شد.

                                    <a data-bs-toggle="modal" data-bs-target="#kt_modal_PAG"
                                       class="fw-semibold cursor-pointer"> جزئیات بیشتر</a>
                                </div>
                            </div>
                            <!--end::Label-->

                            <!--begin::Switch-->
                            <label class="form-check form-switch form-check-custom form-check-solid">
                                @if($licence->has_auto_storage === 0)
                                    <a wire:click="activeStoragePAG(true)" class="btn btn-primary"><i
                                            class="fas fa-plus fs-4 me-2"></i> فعال سازی
                                        <span class="spinner-border spinner-border-sm align-middle ms-2 me-2"
                                              wire:loading wire:target="activeStoragePAG"></span>
                                    </a>
                                @else
                                    <a wire:click="activeStoragePAG(false)" class="btn btn-danger">
                                        <i class="fas fa-info fs-4 me-2"></i> غیرفعال سازی
                                        <span class="spinner-border spinner-border-sm align-middle ms-2 me-2"
                                              wire:loading wire:target="activeStoragePAG"></span>
                                    </a>
                                @endif
                            </label>
                            <!--end::Switch-->
                        </div>
                    </div>

                    <!--end::Switch-->
                </div>
                <!--end::Col-->
            </div>

            <div wire:loading wire:target="upgradeLicence" class="d-flex justify-content-center text-center">
                <div class="dark-box" wire:loading wire:target="upgradeLicence">
                    در حال دریافت اطلاعات ...
                    <span class="spinner-border spinner-border-sm align-middle ms-2 me-2"></span>
                </div>
            </div>

            <div wire:loading.remove class="d-block">
                @if($upgradeMode != false)
                    <livewire:licence.upgrade-licence :licenceUser="$licence->hashId" :from="$licence->detail->slug"
                                                      :to="$upgradeMode" :key="'sr_'.$upgradeMode"/>
                @endif
                <!--end::Row-->
                @if($upgradeMode===false && !$isHideRenew && $licence->detail->slug !== 'free')
                    <!--begin::Row-->
                    <div class="row">
                        <!--begin::Col-->
                        <div class="col-xl-3">
                            <div class="fs-6 fw-semibold mt-2 mb-3">تمدید لایسنس</div>
                        </div>
                        <!--end::Col-->

                        <!--begin::Col-->
                        <div class="col-xl-9">
                            <!--begin::Switch-->

                            @if($userBalance < $licence->detail->price)
                                <div class="text-align-center">
            <span class="svg-icon svg-icon-5tx svg-icon-danger mb-5">
            <svg xmlns="http://www.w3.org/2000/svg" width="24"
                 height="24" viewBox="0 0 24 24" fill="none">
                <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="black"></rect>
                <rect x="11" y="14" width="7" height="2" rx="1" transform="rotate(-90 11 14)" fill="black"></rect>
                <rect x="11" y="17" width="2" height="2" rx="1" transform="rotate(-90 11 17)" fill="black"></rect>
            </svg>
        </span>
                                    <div class="text-center text-dark">
                                        <h1 class="fw-bolder mb-5">اعتبار حساب کاربری کافی نیست</h1>
                                        <div class="separator separator-dashed border-danger opacity-25 mb-5"></div>
                                        <div class="mb-5">
                                            برای تمدید این لایسنس نیاز به
                                            <strong>
                                                {{number_format($licence->detail->price - $userBalance,0)}} تومان
                                            </strong>
                                            اعتبار دیگر دارید.
                                        </div>

                                        <button type="button" class="btn btn-primary"
                                                wire:click="increase({{$licence->detail->price - $userBalance}})">
                                            <span wire:loading.remove wire:target="increase">افزایش اعتبار</span>
                                            <span wire:loading wire:target="increase">
                                        <span class="spinner-border spinner-border-sm align-middle"></span>
                                    </span>
                                        </button>


                                    </div>
                                </div>

                            @else
                                <div class="text-align-center">
            <span class="svg-icon svg-icon-5tx svg-icon-success mb-5">
           <!--begin::Svg Icon | path: assets/media/icons/duotune/general/gen048.svg-->
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
<path opacity="0.3"
      d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z"
      fill="black"/>
<path
    d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z"
    fill="black"/>
</svg>
                <!--end::Svg Icon-->
        </span>
                                    <div class="text-center text-dark">
                                        <h1 class="fw-bolder mb-5"></h1>
                                        <div class="separator separator-dashed border-danger opacity-25 mb-5"></div>
                                        <div class="mb-5">
                                            تمدید لایسنس
                                            <strong>
                                                {{$licence->detail->title}}
                                            </strong>
                                            و کسر مبلغ
                                            <strong>
                                                {{number_format($licence->detail->price,0)}} تومان
                                            </strong>
                                            برای یک دوره
                                            <strong>
                                                {{$licence->detail->periodInMinutes / (60*24)}}
                                            </strong>
                                            روزه
                                        </div>

                                        <button type="button" class="btn btn-primary"
                                                wire:click="renew()">
                                            <span wire:loading.remove wire:target="renew">تمدید</span>
                                            <span wire:loading wire:target="renew">
                                        <span class="spinner-border spinner-border-sm align-middle"></span>
                                    </span>
                                        </button>

                                    </div>
                                </div>

                            @endif

                            <!--end::Switch-->
                        </div>
                        <!--end::Col-->
                    </div>
                    <!--end::Row-->
                @endif
            </div>

        </div>
        <!--end::Card body-->

    </div>
</div>
