<div>
    <div class="row">
        <style wire:ignore>
            .calculation-container {
                max-width: 600px;
                margin: 0 auto;
            }

            .calculation-container h2 {
                margin-bottom: 10px;
                font-size: 20px;
                color: #333;
            }

            .calculation-step {
                margin-bottom: 10px;
            }

            .highlight {
                font-weight: bold;
                color: #27ae60;
            }

            .final-result {
                font-size: 20px;
                font-weight: bold;
                color: #e74c3c;
                text-align: center;
                padding: 10px;
                background-color: #fce4e4;
                border-radius: 5px;
            }

            .equation {
                flex-direction: row-reverse;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }

            .equation .operator {
                font-size: 18px;
                margin: 0 10px;
            }

            .highlight-price {
                display: flex;
                flex-direction: column;
                color: #939393;
            }

            .highlight-price > strong {
                color: #2b2b2b;
                margin-bottom: 1px;
            } </style>
        <div class="col-xl-3">
            <div class="fs-6 fw-semibold mt-2 mb-3">ارتقا لایسنس</div>
        </div>
        <!--end::Col--> <!--begin::Col-->
        <div class="col-xl-9">
            <!--begin::Switch-->
            @if($userBalance < $payableAmount)
                <div class="text-align-center"><span class="svg-icon svg-icon-5tx svg-icon-danger mb-5"> <svg
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"> <rect
                                opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="black"></rect> <rect
                                x="11"
                                y="14"
                                width="7"
                                height="2"
                                rx="1"
                                transform="rotate(-90 11 14)"
                                fill="black"></rect> <rect
                                x="11" y="17" width="2" height="2" rx="1" transform="rotate(-90 11 17)"
                                fill="black"></rect> </svg> </span>
                    <div class="text-center text-dark">
                        <h1 class="fw-bolder mb-5">اعتبار حساب کاربری کافی نیست</h1>
                        <div class="separator separator-dashed border-danger opacity-25 mb-5"></div>
                        <div class="mb-5">
                            برای تمدید این لایسنس نیاز به
                            <strong> {{number_format($payableAmount - $userBalance,0)}} تومان </strong>
                            اعتبار دیگر دارید.
                        </div>
                        <div class="calculation-container mb-5"><br/>
                            <div class="equation"><span class="highlight-price"> <strong> {{number_format($newLicencePrice)}} تومان </strong> لایسنس جدید </span>
                                <span class="operator">-</span> <span class="highlight-price"> <strong> {{number_format($remainCurrentLicenceBalance)}} تومان </strong> اعتبار باقی مانده لایسنس فعلی </span>
                                <span class="operator">-</span> <span class="highlight-price"> <strong> {{number_format($userBalance)}} تومان </strong> اعتبار شما </span>
                                <span class="operator">=</span> <span class="highlight-price"> <strong> {{number_format($payableAmount - $userBalance,0)}} تومان </strong> پرداختی شما </span>
                            </div>
                        </div>
                        <button type="button" class="btn btn-primary"
                                wire:click="increase({{$payableAmount - $userBalance}})">
                            <span wire:loading.remove wire:target="increase">افزایش اعتبار</span>
                            <span wire:loading wire:target="increase">
                            <span class="spinner-border spinner-border-sm align-middle"></span>
                        </span>
                        </button>
                    </div>
                </div>
            @else
                <div class="text-align-center">
            <span class="svg-icon svg-icon-5tx svg-icon-success mb-5">
           <!--begin::Svg Icon | path: assets/media/icons/duotune/general/gen048.svg-->
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
<path opacity="0.3"
      d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z"
      fill="black"/>
<path
    d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z"
    fill="black"/>
</svg>
                <!--end::Svg Icon-->
        </span>
                    <div class="text-center text-dark">
                        <h1 class="fw-bolder mb-5"></h1>
                        <div class="separator separator-dashed border-danger opacity-25 mb-5"></div>
                        <div class="mb-5">
                            شما میتوانید با اعتبار کیف پول خود لایسنس را ارتقا دهید
                            <div class="calculation-container mb-5"><br/>
                                <div class="equation"><span class="highlight-price"> <strong> {{number_format($newLicencePrice)}} تومان </strong> لایسنس جدید </span>
                                    <span class="operator">-</span> <span class="highlight-price"> <strong> {{number_format($remainCurrentLicenceBalance)}} تومان </strong> اعتبار باقی مانده لایسنس فعلی </span>
                                    <span class="operator">=</span> <span class="highlight-price"> <strong> {{number_format($payableAmount,0)}} تومان </strong> پرداختی شما </span>
                                </div>
                            </div>

                        </div>

                        <button type="button" class="btn btn-primary"
                                wire:click="upgradeLicence()">
                            <span wire:loading.remove wire:target="upgradeLicence">کسر و ارتقا لایسنس</span>
                            <span wire:loading wire:target="upgradeLicence">
                                        <span class="spinner-border spinner-border-sm align-middle"></span>
                                    </span>
                        </button>

                    </div>
                </div>

            @endif
            <!--end::Switch-->
        </div>
        <!--end::Col-->
    </div>
</div>
