<div class="position-relative pb-8">

    @if(!$verify_mode)
        {{-- If your happiness depends on money, you will never be happy with yourself. --}}
        <!--begin::Form group-->

        <div class="form w-100" novalidate="novalidate" id="kt_login_signin_form" wire:ignore>
            <div class="fv-row">
                <label class="form-label fs-6 fw-bolder text-dark">شماره تماس / ایمیل</label>
                <input id="kt_login_signin_input" wire:target="login_with_phone_or_email" wire:loading.attr="disabled"
                       class="danger-border form-control form-control-lg form-control-solid direction-ltr" type="text"
                       name="username" tabindex="1" wire:model.blur="user_entry" wire:ignore data-livewire="@this"/>
            </div>
        </div>
        @if ($errors->has('user_entry'))
            <div class="fv-plugins-message-container">
                <div data-field="user_entry" data-validator="user_entry" class="fv-help-block">
                    {{ $errors->first('user_entry') }}
                </div>
            </div>
        @endif
        @if ($errors->has('alert'))
            <div class="fv-plugins-message-container">
                <div data-field="alert" data-validator="alert" class="fv-help-block">
                    {{ $errors->first('alert') }}
                </div>
            </div>
        @endif
        <!--end::Form group-->


        <!--begin::Action-->
        <div class="text-align-left pb-lg-0 pb-5 mt-10">
            <button id="kt_login_signin_form_submit_button" tabindex="4"
                    class="btn btn-primary fw-bolder fs-6 px-8 py-4 my-3 me-3  "
                    wire:click="login_with_phone_or_email()" wire:loading.class="disabled">
                <span wire:loading.remove wire:target="login_with_phone_or_email">ورود</span>
                <span wire:loading wire:target="login_with_phone_or_email">منتظر بمانید
                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </span>
            </button>
            <a href="{{ url('auth/google') }}" type="button"
               class="btn btn-light-primary fw-bolder px-8 py-4 my-3 fs-6">
                <img src="{{ asset('assets/media/svg/social-icons/google.svg') }}"
                     class="w-20px h-20px me-3" alt=""/>ورود / ثبت نام با گوگل
            </a>
        </div>
        <!--end::Action-->

    @else
        <div>
            <!--begin::Form group-->
            <div class="fv-row">
                <div class="mb-3">
                    <label class="label-required form-label fs-6 fw-bolder text-dark">کد ارسالی</label>
                    <label2 style="display: flex; justify-content: space-between;">
                        کد به {{ $user_entry }} ارسال شد. <br/>
                        <a href="#" wire:click="cancel()" wire:loading.class="disabled" wire:key="cancel_verify">
                            <span wire:loading.remove wire:target="cancel">
                                <!--begin::Svg Icon | path: /var/www/preview.keenthemes.com/keenthemes/start/docs/core/html/src/media/icons/duotune/general/gen044.svg-->
<span class="svg-icon svg-icon-muted svg-icon-hx"><svg width="10" height="10" viewBox="0 0 24 24" fill="none"
                                                       xmlns="http://www.w3.org/2000/svg">
<rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
<rect x="11" y="14" width="7" height="2" rx="1" transform="rotate(-90 11 14)" fill="currentColor"/>
<rect x="11" y="17" width="2" height="2" rx="1" transform="rotate(-90 11 17)" fill="currentColor"/>
</svg>
</span>
                                <!--end::Svg Icon-->
                                ویرایش
                            </span>
                            <span wire:loading wire:target="cancel">
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                            </span>
                        </a>
                    </label2>
                </div>

                <!--end::Action-->
                <div class="fv-row mb-1">
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12">
                            <div class="otp-password flex-stack flex-column" style="display: flex;">
                                <div id="inputOtp" class="mt-5" name="otp" input-key="otp" data-vv-as="کد تایید"
                                     is-input-num="true" value="" style="display: flex; direction: ltr;"
                                     wire:ignore>

                                    <input min="0" max="9" pattern="[0-9]" type="tel"
                                           wire:model.lazy="code"
                                           class="form-control   w-full form-control-solid otp-input text-align-center btn ms-2 ms-lg-3"
                                           maxlength="{{$token_size}}" wire:loading.class="disabled">

                                </div>

                                <button tabindex="5"
                                        class="timer-btn btn btn-color-gray-600 btn-active-light-primary  fw-bolder fs-6 my-3 px-3 w-100"
                                        wire:click="refresh_token()" wire:ignore.self disabled>
                                    <span class="timer-btn-text hidden" wire:ignore id="retry-btn">ارسال مجدد</span>
                                    <span class="js-timeout fs-1 fw-bolder" wire:ignore>{{$count_down}}</span>
                                </button>

                                <button tabindex="5"
                                        class="login-btn btn btn-primary w-full fw-bolder fs-6 my-3 px-3 w-100 mt-5"
                                        wire:click="check_token()" wire:ignore.self>
                                    <span class="timer-btn-text" wire:ignore id="retry-btn">ورود</span>
                                </button>

                            </div>
                        </div>
                    </div>
                </div>
                <!--end::Signin Form-->
            </div>
            <!--end::Form group-->
        </div>

        <!--begin::Action-->
        <div
            class="text-align-left pb-lg-0 pb-5 d-flex align-items-center flex-row-reverse position-absolute bottom-0 user-select-none">
            <button tabindex="6" class="d-none btn btn-light-primary fw-bolder px-8 py-4 my-3 fs-6"
                    wire:click="cancel()"
                    wire:key="cancel_verify">
                انصراف
            </button>
            <div tabindex="4" class="fw-bolder fs-6 me-3">
                <span wire:loading>منتظر بمانید...
                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </span>
            </div>

        </div>
        <div class="mt-5">
            @if ($errors->has('code'))
                <div wire:loading.remove class="fv-plugins-message-container">
                    <div data-field="code" data-validator="code" class="fv-help-block">
                        {{ $errors->first('code') }}
                    </div>
                </div>
            @endif
            @if ($errors->has('alert'))
                <div wire:loading.remove class="fv-plugins-message-container">
                    <div data-field="alert" data-validator="alert" class="fv-help-block">
                        {{ $errors->first('alert') }}
                    </div>
                </div>
            @endif
        </div>

    @endif
</div>
@assets
<style>
    .hidden {
        display: none;
    }

    .timer-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: auto !important;
    }
</style>
@endassets
@script
<script>
    $(document).ready(function () {
        // CONFIRMATION CODE FIELD
        // define ctlr key
        var ctrlDown = false,
            ctrlKey = 17,
            cmdKey = 91,
            tokens = [];
        window.inputFlag = true;
        $('#inputOtp .form-control:first-child').focus();

        function checkToken(tokens) {
            if ($('#inputOtp').attr('value').length === $('.otp-input').length) {
                $wire.set('code_array', $('#inputOtp').attr('value'));
            }
        }

        $(document).keydown(function (e) {
            if (e.keyCode == ctrlKey || e.keyCode == cmdKey) ctrlDown = true;
        }).keyup(function (e) {
            if (e.keyCode == ctrlKey || e.keyCode == cmdKey) ctrlDown = false;
        });


        $('.otp-input').on('keydown', function (e) {
            // MOVE FORWARD
            if ((e.keyCode >= 48 && e.keyCode <= 57 || (e.keyCode >= 96 && e.keyCode <= 105))) {
                if ($(this).val() != "" && window.inputFlag) {
                    $('.otp-input').eq($(this).index() + 1).select();
                } else if ($(this).val() != "" && !window.inputFlag) {
                    $(this).val('')
                }
                window.inputFlag = true;
            } else if (e.keyCode == 39) {
                if ($(this).index() < $('.otp-input').length - 1) {
                    $('.otp-input').eq($(this).index() + 1).select();
                    window.inputFlag = false;
                }
            }

            // MOVE BACKWARD
            else if (e.keyCode == 8) {
                if ($(this).val() == "" && $(this).index() > 0) {
                    $('.otp-input').eq($(this).index() - 1).select();
                }
                $(this).val('');
            } else if (e.keyCode == 37) {
                if ($(this).index() > 0) {
                    $('.otp-input').eq($(this).index() - 1).select();
                    window.inputFlag = false;
                }
            }

            // tab
            else if (e.keyCode == 9) {
                window.inputFlag = false;
            }

            // ctrl + v
            else if (ctrlDown && e.keyCode != 86) {
                e.preventDefault();
            }
        });

        $('.otp-input').on('keydown keyup keypress', function (e) {
            tokens[$(this).index()] = $(this).val();
            $('#inputOtp').attr('value', `${tokens.join("")}`);
            !ctrlDown && checkToken(tokens);
        });


        // paste event
        document.querySelectorAll('.otp-input').forEach(function (element) {
            var currentIndex
            var currentEl
            element.addEventListener('click', function (e) {
                currentEl = $(element);
                currentEl.select();
                window.inputFlag = false;
            });

            element.addEventListener('paste', function (e) {
                var clipboardData, pastedData;
                currentEl = $(element);
                currentIndex = currentEl.index();

                // Stop data actually being pasted into div
                e.stopPropagation();
                e.preventDefault();

                // Get pasted data via clipboard API
                clipboardData = e.clipboardData || window.clipboardData;
                pastedData = clipboardData.getData('Text');

                // Do whatever with pasted data
                var splitData = pastedData.split('');
                for (var i of splitData) {

                    currentEl.val(i)
                    if (currentIndex > -1 && currentIndex < $('.otp-input').length) {
                        if (currentIndex <= $('.otp-input').length - 1) {
                            currentIndex += 1;
                        } else {
                            break
                        }
                        currentEl = $('.otp-input').eq(currentIndex);
                        currentEl.select();
                    }
                }


                $('.otp-input').each(function () {
                    tokens[$(this).index()] = $(this).val();
                });
                $('#inputOtp').attr('value', `${tokens.join("")}`);
                checkToken(tokens)
            })
        });
        // END OF CONFIRMATION CODE FIELD


        // TIMER
        String.prototype.toPersianDigit = function () {
            var id = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
            return this.replace(/[0-9]/g, function (w) {
                return id[+w]
            });
        }
        String.prototype.toEnglishDigits = function () {
            var id = {
                '۰': '0',
                '۱': '1',
                '۲': '2',
                '۳': '3',
                '۴': '4',
                '۵': '5',
                '۶': '6',
                '۷': '7',
                '۸': '8',
                '۹': '9'
            };
            return this.replace(/[^0-9.]/g, function (w) {
                return id[w] || w;
            });
        };
        window.interval;
        clearInterval(window.interval);

        function countdown() {
            clearInterval(window.interval);
            window.interval = setInterval(function () {
                var timer = $('.js-timeout').html();
                if (timer !== undefined) {
                    timer = timer.split(':');
                    if (timer[1] === undefined) {
                        var minutes = Math.round(timer / 60);
                        var seconds = timer % 60;
                    } else {
                        var minutes = parseInt(timer[0].toString().toEnglishDigits(), 10);
                        var seconds = parseInt(timer[1].toString().toEnglishDigits(), 10);
                    }

                    seconds -= 1;
                    if (minutes < 0) return;
                    else if (seconds < 0 && minutes != 0) {
                        minutes -= 1;
                        seconds = 59;
                    } else if (seconds < 10 && length.seconds != 2) seconds = '0' + seconds;

                    if (minutes == 0 && seconds == 0) {
                        $('.timer-btn').attr('disabled', false);
                        $('.js-timeout').css('display', 'none');
                        $('.timer-btn-text').css('margin-right', '0');
                        $('.timer-btn').removeClass('w-100');
                    }
                    ;

                    $('.js-timeout').html(minutes.toString().toPersianDigit() + ':' + seconds.toString().toPersianDigit());
                }


                if (minutes == 0 && seconds == 0) {
                    $('#retry-btn').removeClass('hidden');
                    clearInterval(window.interval);
                }
            }, 1000);
        }

        function run_count_down(time) {
            var sec = time;
            var minnum = Math.floor(sec / 60);
            var secnum = sec - (minnum * 60)
            if (secnum >= 0 && secnum < 10) {
                secnum = "0" + `${secnum}`
            }
            $('.js-timeout').text(`${minnum}:${secnum}`.toPersianDigit());
            countdown();
        }

        run_count_down({{$count_down}})
        // END OF TIMER

        Livewire.on('refresh_timer', () => {
            $('.timer-btn').attr('disabled', true);
            $('.js-timeout, .timer-btn-text').removeAttr('style');
            $('.timer-btn').addClass('w-100');
            run_count_down({{$static_count_down}})
        });

    });


    Livewire.on('run_validation', () => {
        KTLogin.init();
    });

    Livewire.on('reload_component', () => {
        KTLogin.init().validate();
    });

    Livewire.on('run_validation', () => {
        $('#kt_body #kt_login_signin_input').keydown(function (e) {
            if (e.keyCode === 13) {
                $('#kt_body #kt_login_signin_form_submit_button').click();
                $wire.call('login_with_phone_or_email')
            }
        });
    });
    $(document).ready(function () {
        $('#kt_body #kt_login_signin_input').keydown(function (e) {
            if (e.keyCode === 13) {
                $('#kt_body #kt_login_signin_form_submit_button').click();
                $wire.call('login_with_phone_or_email')
            }
        });
    })
</script>
@endscript
