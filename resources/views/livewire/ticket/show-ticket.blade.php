<div>
{{-- Care about people's approval and you will be their prisoner. --}}
{{--@if(!empty($ticket))--}}
<!--begin::Messenger-->
    <div style="transform: translate(50%,-50%); font-weight: 800; color: #d1d2d4;"
         class="d-flex justify-content-center align-items-center flex-column-reverse position-absolute top-50 end-50 fs-2x @if(!empty($ticket)) d-none @endif">
        تیکت مورد نظر را انتخاب کنید
        <i style="font-size: 100px; color: #d1d2d4;" class="fas fa-comment-alt mb-10"></i>
    </div>

    <div class="d-none" id="skeleton-loading">
        <div class="flex-lg-row-fluid">
            <!--begin::Messenger-->
            <div class="card">
                <!--begin::Card header-->
                <div class="card-header">
                    <!--begin::Title-->
                    <div class="card-title">
                        <!--begin::User-->
                        <div class="d-flex justify-content-center flex-column me-3">
                            <span href="#"
                                  class="skeleton skeleton-title fs-4 fw-bolder text-gray-900 text-hover-primary me-1 mb-2 lh-1"></span>
                            <!--begin::Info-->
                            <span class="mb-0 lh-1 skeleton skeleton-status"></span>
                            <!--end::Info-->
                        </div>
                        <!--end::User-->
                    </div>
                    <!--end::Title-->
                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body skeleton-line position-relative overflow-hidden">
                    <!--begin::Messages-->
                    <div class="me-n5 pe-5 h-300px h-lg-auto">
                        <!--begin::Message(out)-->
                        <div class="d-flex justify-content-end mb-10">
                            <!--begin::Wrapper-->
                            <div class="d-flex flex-column align-items-end">
                                <!--begin::User-->
                                <div class="d-flex align-items-center mb-2">
                                    <!--begin::Details-->
                                    <div class="me-3 skeleton skeleton-title">
                                        <span class="text-muted fs-7 mb-1"></span>
                                    </div>
                                    <!--end::Details-->
                                    <!--begin::Avatar-->
                                    <div class="symbol symbol-35px symbol-circle">
                                        <img alt="Pic" src="{{ asset('assets/media/avatars/image.gif') }}">
                                    </div>
                                    <!--end::Avatar-->
                                </div>
                                <!--end::User-->
                                <!--begin::Text-->
                                <div
                                    class="skeleton skeleton-content-lg p-5 rounded text-dark fw-bold mw-lg-400px text-end"
                                    data-kt-element="message-text"></div>
                                <!--end::Text-->
                            </div>
                            <!--end::Wrapper-->
                        </div>
                        <!--end::Message(out)-->
                        <!--begin::Message(in)-->
                        <div class="d-flex justify-content-start mb-10">
                            <!--begin::Wrapper-->
                            <div class="d-flex flex-column align-items-start">
                                <!--begin::User-->
                                <div class="d-flex align-items-center mb-2">
                                    <!--begin::Avatar-->
                                    <div class="symbol symbol-35px symbol-circle">
                                        <img alt="Pic" src="{{ asset('assets/media/avatars/image.gif') }}">
                                    </div>
                                    <!--end::Avatar-->
                                    <!--begin::Details-->
                                    <div class="ms-3 skeleton skeleton-title">
                                        <span class="text-muted fs-7 mb-1"></span>
                                    </div>
                                    <!--end::Details-->
                                </div>
                                <!--end::User-->
                                <!--begin::Text-->
                                <div
                                    class="skeleton skeleton-content-lg p-5 rounded text-dark fw-bold mw-lg-400px text-start"
                                    data-kt-element="message-text"></div>
                                <!--end::Text-->
                            </div>
                            <!--end::Wrapper-->
                        </div>
                        <!--end::Message(in)-->
                    </div>
                    <!--end::Messages-->
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Messenger-->
        </div>
    </div>
    @push('scripts')
        <script>
            Livewire.on('show_ticket', () => {
                $('#skeleton-loading').removeClass('d-none');
                $('#no-open-ticket, #kt_chat_messenger').addClass('d-none');
            });
        </script>
    @endpush


    <div class="card  @if(empty($ticket)) d-none @endif" id="kt_chat_messenger">
        <!--begin::Card header-->
        <div class="card-header" id="kt_chat_messenger_header">
        @if(!empty($ticket))
            <!--begin::Title-->
                <div class="card-title">
                    <!--begin::User-->
                    <div class="d-flex justify-content-center flex-column me-3">
                        <a href="#"
                           class="fs-4 fw-bolder text-gray-900 text-hover-primary me-1 mb-2 lh-1">{{$ticket->subject}}</a>
                        <!--begin::Info-->
                        <div class="mb-0 lh-1">
                            <span class="badge badge-success badge-circle w-10px h-10px me-1"
                                  style="background-color: {{$ticket->statusColor()}}"></span>
                            <span class="fs-7 fw-bold text-gray-400">{{$ticket->statusName()}}</span>
                        </div>
                        <!--end::Info-->
                    </div>
                    <!--end::User-->
                </div>
                <!--end::Title-->
        @endif
        <!--begin::Card toolbar-->
            <div class="card-toolbar">
                <!--begin::Menu-->
                <div class="me-n3">
                    <span class="btn btn-sm btn-icon btn-active-light-primary" data-kt-menu-trigger="click"
                          data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                        <i class="bi bi-three-dots fs-2"></i>
                    </span>
                    <!--begin::Menu 3-->
                    <div
                        class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-bold w-200px py-3 me-4"
                        data-kt-menu="true">
                        <!--begin::Heading-->
                        <div class="menu-item px-3">
                            <div class="menu-content text-muted pb-2 px-3 fs-7 text-uppercase">تنظیمات</div>
                        </div>
                        <!--end::Heading-->
                        <!--begin::Menu item-->
                        <div class="menu-item px-3">
                            <a class="menu-link px-3" wire:click="close_my_ticket()">بستن تیکت</a>
                        </div>
                        <!--end::Menu item-->
                        <!--begin::Menu item-->
                        <div class="menu-item px-3">
                            <a href="#" class="menu-link flex-stack px-3" data-bs-toggle="modal"
                               data-bs-target="#kt_modal_invite_friends">تغییر اولویت
                                <i class="fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="tooltip" title=""
                                   data-bs-original-title="بزودی"
                                   aria-label="Specify a contact email to send an invitation"></i></a>
                        </div>
                        <!--end::Menu item-->

                    </div>
                    <!--end::Menu 3-->

                </div>
                <!--end::Menu-->
            </div>
            <!--end::Card toolbar-->
        </div>
        <!--end::Card header-->
    @if(!empty($ticket))
        <!--begin::Card body-->
            <div class="card-body" id="kt_chat_messenger_body" wire:loading.class="opacity-05">
                <!--begin::Messages-->
                <div class="scroll-offset-bottom scroll-y me-n5 pe-5 h-300px h-lg-auto" data-kt-element="messages"
                     data-kt-scroll="true"
                     data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto"
                     data-kt-scroll-dependencies="#kt_header, #kt_toolbar, #kt_footer, #kt_chat_messenger_header, #kt_chat_messenger_footer"
                     data-kt-scroll-wrappers="#kt_content, #kt_chat_messenger_body" data-kt-scroll-offset="-2px"
                     style="max-height: 50vh">

                    <!-- starting summary -->
                    <div class="d-flex justify-content-start mb-10">
                        <!--begin::Wrapper-->
                        <div class="d-flex flex-column align-items-start">
                            <!--begin::User-->
                            <div class="d-flex align-items-center mb-2">
                                <!--begin::Avatar-->
                                <div class="symbol symbol-35px symbol-circle">
                                    <img alt="Pic" src="{{ $user->profile_photo_url() }}">
                                </div>
                                <!--end::Avatar-->
                                <!--begin::Details-->
                                <div class="ms-3">
                                    <a href="#"
                                       class="fs-5 fw-bolder text-gray-900 text-hover-primary me-1">{{$user->first_name}}</a>
                                    <span
                                        class="text-muted fs-7 mb-1">{{jdate($ticket->created_at)->format("Y/m/d  H:i:s")}}</span>
                                </div>
                                <!--end::Details-->
                            </div>
                            <!--end::User-->
                            <!--begin::Text-->
                            <div class="p-5 rounded bg-light-info text-dark fw-bold mw-lg-400px text-start"
                                 data-kt-element="message-text">      {{$ticket->summary}}</div>
                            <!--end::Text-->
                        </div>
                        <!--end::Wrapper-->
                    </div>
                    <!-- end summary -->

                    @forelse($ticket->commentsAndNotesAndEventsAndAttachments()->sortBy('created_at') as $comment)

                        @if($comment instanceof App\Models\Ticket\TicketEvent)
                            <span class="badge badge-light-dark fs-9 d-flex flex-center mb-3">
                                {{__('event.'.$comment->event_name())}} در
                                {{$comment->ago_time()}}
                            </span>
                        @elseif($comment instanceof App\Models\Ticket\TicketAttachment)
                            @if(($comment->user_id == $user->id))
                                <div class="d-flex justify-content-end mb-10 flex-wrap flex-column align-content-end">
                                    @else
                                        <div>
                                            @endif
                                            <span class="file_holder fs-9 d-flex mw-lg-150px mb-3 align-items-center ">
                                <i class="fas fa-file"></i>
                              <a href="{{$comment->path}}" target="_blank">
                                 <b>{{ substr($comment->path,strlen($comment->path) - 10 ,strlen($comment->path))}}</b>
                              <br/>
                                   <span class="file_holer_size">{{$comment->size}} KB - دانلود</span>

                              </a>

                            </span>
                                            <span class="file_holder_time">
                                <i class="far fa-clock"></i>
                                 {{jdate($comment->created_at)->format("Y/m/d  H:i:s")}}
                            </span>
                                            <button
                                                wire:click="send_report_component({{$comment->id}},'Ticket\\TicketAttachment')">
                                                گزارش
                                            </button>

                                        </div>

                                        @else
                                            @if(($comment->user->id != $user->id))
                                                <div class="d-flex justify-content-end mb-10">
                                                    <!--begin::Wrapper-->
                                                    <div class="d-flex flex-column align-items-end">
                                                        <!--begin::User-->
                                                        <div class="d-flex align-items-center mb-2">
                                                            <!--begin::Details-->
                                                            <div class="me-3">
                                                <span
                                                    class="text-muted fs-7 mb-1 direction-rtl"> {{jdate($comment->created_at)->format("Y/m/d  H:i:s")}}</span>
                                                                <a href="#"
                                                                   class="fs-5 fw-bolder text-gray-900 text-hover-primary ms-1">{{$comment->user->first_name}}</a>
                                                            </div>
                                                            <!--end::Details-->
                                                            <!--begin::Avatar-->
                                                            <div class="symbol symbol-35px symbol-circle">
                                                                <img alt="Pic"
                                                                     src="{{ $comment->user->profile_photo_url() }}">
                                                            </div>
                                                            <!--end::Avatar-->
                                                        </div>
                                                        <!--end::User-->
                                                        <!--begin::Text-->
                                                        <div
                                                            class="p-5 rounded bg-light-primary text-dark fw-bold mw-lg-400px text-end"
                                                            data-kt-element="message-text">
                                                            {{$comment->body}}
                                                        </div>
                                                        <!--end::Text-->
                                                    </div>
                                                    <!--end::Wrapper-->
                                                    <button
                                                        wire:click="send_report_component({{$comment->id}},'Ticket\\TicketComment')">
                                                        گزارش
                                                    </button>
                                                </div>

                                            @else
                                            <!-- starting summary -->
                                                <div class="d-flex justify-content-start mb-10">
                                                    <!--begin::Wrapper-->
                                                    <div
                                                        class="d-flex flex-column align-items-{{($comment->user->id == $user->id) ? "start" : "end"}}">
                                                        <!--begin::User-->
                                                        <div class="d-flex align-items-center mb-2">
                                                            <!--begin::Avatar-->
                                                            <div class="symbol symbol-35px symbol-circle">
                                                                <img alt="Pic"
                                                                     src="{{ $comment->user->profile_photo_url() }}">
                                                            </div>
                                                            <!--end::Avatar-->
                                                            <!--begin::Details-->
                                                            <div class="ms-3">
                                                                <a href="#"
                                                                   class="fs-5 fw-bolder text-gray-900 text-hover-primary me-1">{{$comment->user->first_name}}</a>
                                                                <span
                                                                    class="text-muted fs-7 mb-1">{{jdate($comment->created_at)->format("Y/m/d  H:i:s")}}</span>
                                                            </div>
                                                            <!--end::Details-->
                                                        </div>
                                                        <!--end::User-->
                                                        <!--begin::Text-->
                                                        <div
                                                            class="p-5 rounded bg-light-info text-dark fw-bold mw-lg-400px text-start"
                                                            data-kt-element="message-text">      {{$comment->body}}</div>
                                                        <!--end::Text-->
                                                    </div>
                                                    <!--end::Wrapper-->
                                                </div>
                                                <!-- end summary -->
                                            @endif
                                        @endif
                                        @empty
                                            پاسخی ثبت نشده است
                                        @endforelse


                                </div>
                </div>
            </div>
            <!--end::Card body-->
    @endif
    <!--begin::Card footer-->
        <div class="card-footer pt-4" id="kt_chat_messenger_footer">
            <div class="w-100" id="uploading_message" style="display: none">
                <div class="progress">
                    <div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0"
                         aria-valuemax="100"></div>
                </div>
                <div class="progress-msg"></div>
            </div>
            <!--begin::Input-->
            <textarea class="form-control form-control-flush mb-3" rows="1" data-kt-element="input"
                      placeholder="متن خود را وارد نمایید" wire:model="comment"></textarea>
            <!--end::Input-->
            <!--begin:Toolbar-->
            <div class="d-flex flex-stack">
                @if(!empty($ticket))
                    <livewire:ticket.file-upload :ticket="$ticket->id"/>
            @endif
            <!--begin::Send-->
                <button class="btn btn-primary" wire:click="submit_new_comment()" type="button"
                        data-kt-element="send">
                    ارسال
                </button>
                <!--end::Send-->
            </div>
            <!--end::Toolbar-->
        </div>
        <!--end::Card footer-->
    </div>
    <!--end::Messenger-->

    {{--    @endif--}}
</div>
