<div>
    {{-- If you look to others for fulfillment, you will never truly be fulfilled. --}}

    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#kt_modal_1">
        ارسال تیکت جدید
    </button>

    <div class="modal fade" tabindex="-1" id="kt_modal_1" wire:ignore.self>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">بازکردن تیکت جدید</h5>

                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                         aria-label="Close">
                        <span class="svg-icon svg-icon-2x"></span>
                    </div>
                    <!--end::Close-->
                </div>

                <div class="modal-body">

                    <form wire:submit>

                        <!--begin::Form Group-->
                        <div class="mb-10">
                            <label class="fs-6 form-label fw-bolder text-dark">موضوع
                            </label>
                            <input type="text" class="form-control form-control-lg form-control-solid"
                                   name="subject"
                                   wire:model.blur="subject"
                                   value="{{$subject}}"/>

                            @if ($errors->has('subject'))
                                <span class="invalid-feedback d-block" role="alert">
                                     <strong>{{ $errors->first('subject') }}</strong>
                                </span>
                            @endif

                        </div>


                        <!--end::Form Group-->

                        <!--begin::Form Group-->
                        <div class="mb-10" >
                            <label class="fs-6 form-label fw-bolder text-dark">واحد
                            </label>
                            <div  wire:ignore>
                            <select class="form-select form-select-lg form-select-solid" name="support_team"
                                    data-control="select2"
{{--                                    wire:model.live="selected_team"--}}
                                    onchange="@this.selected_team = $(this).val();$('#support_team').css('display','inline-block')"
                                    data-placeholder="واحد پشتیبانی"
                            >
                                <option>انتخاب نمایید</option>
                                @foreach($support_team as $data)
                                    <option value="{{$data->id}}"
                                    >{{$data->name}}</option>
                                @endforeach
                            </select>
                            </div>
                            @if ($errors->has('selected_team'))
                                <span class="invalid-feedback d-block" role="alert">
                                     <strong>{{ $errors->first('selected_team') }}</strong>
                                </span>
                            @endif
                        </div>
                        <!--end::Form Group-->

                        <!--begin::Form Group-->
                        <div class="mb-10">
                            <label class="fs-6 form-label fw-bolder text-dark">
                                متن تیکت
                            </label>
                            <!--begin::Input-->
                            <textarea wire:model.blur="summary" class="form-control  form-control-solid mb-3" rows="1"
                                      data-kt-element="input" placeholder="متن خود را وارد نمایید"></textarea>
                            <!--end::Input-->

                            @if ($errors->has('summary'))
                                <span class="invalid-feedback d-block" role="alert">
                                     <strong>{{ $errors->first('summary') }}</strong>
                                </span>
                            @endif
                        </div>
                        <!--end::Form Group-->

                        <!--begin::Form Group-->
                        <div class="mb-10">
                            <label class="fs-6 form-label fw-bolder text-dark">
                                اولویت
                            </label>
                            <div wire:ignore>
                                <select class="form-select form-select-lg form-select-solid" name="support_team"
                                        data-control="select2"
                                        wire:model.live="selected_priority"
                                        onchange="@this.selected_priority = $(this).val();$('#selected_priority').css('display','inline-block')"
                                        data-placeholder="اولویت"
                                >

                                    @foreach($priority as $data)
                                        <option value="{{$data["id"]}}"
                                                @if($data["id"] == 1)
                                                    selected
                                                @endif
                                        >{{$data["name"]}}</option>
                                    @endforeach
                                </select>
                            </div>
                            @if ($errors->has('selected_priority'))
                                <span class="invalid-feedback d-block" role="alert">
                                     <strong>{{ $errors->first('selected_priority') }}</strong>
                                </span>
                            @endif
                        </div>
                        <!--end::Form Group-->


                    </form>

                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">انصراف</button>
                    <button type="button" class="btn btn-primary" wire:click="create_new_ticket()">ذخیره</button>
                </div>
            </div>
        </div>
    </div>
</div>
