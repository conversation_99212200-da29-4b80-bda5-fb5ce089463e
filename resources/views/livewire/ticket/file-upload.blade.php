<div>
{{-- Nothing in the world is as soft and yielding as water. --}}
<!--begin::Actions-->
    <div class="d-flex align-items-center me-2">
        <button id="main-update" class="btn btn-sm btn-icon btn-active-light-primary me-1" type="button"
                data-bs-toggle="tooltip" title="" data-bs-original-title="ارسال فایل">
            <i class="bi bi-paperclip fs-3"></i>
        </button>


        <form wire:submit="save">

            <div>
                <label class="d-none custom-file-upload d-block" for="ticket_file">
                    <!--begin::Input group-->
                    <!--end::Input group-->
                    <input id="ticket_file" type="file" wire:model.live="ticket_file">
                </label>
                @error('ticket_file') <span class="error">{{ $message }}</span> @enderror

            </div>

        </form>



        <button class="btn btn-sm btn-icon btn-active-light-primary me-1" type="button"
                data-bs-toggle="tooltip" title="" data-bs-original-title="Coming soon">
            <i class="bi bi-upload fs-3"></i>
        </button>
    </div>
    <!--end::Actions-->

    <script>
        $('#main-update').on('click', function(){
            $('#ticket_file').trigger('click');
        })
    </script>
</div>
