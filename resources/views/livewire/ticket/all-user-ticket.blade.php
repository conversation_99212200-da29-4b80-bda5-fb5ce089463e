<div>
    <!--begin::Card header-->

    <!--end::Card header-->
    <!--begin::Card body-->
    <div class="card-body pt-5" id="kt_chat_contacts_body" wire:loading.class="opacity-05" wire:target="search_term">

        <!--begin::Form-->
        <form class="w-100 position-relative" autocomplete="off">
            <!--begin::Icon-->
            <!--begin::Svg Icon | path: icons/duotone/General/Search.svg-->
            <span
                class="svg-icon svg-icon-2 svg-icon-lg-1 svg-icon-gray-500 position-absolute top-50 ms-5 translate-middle-y">
														<svg xmlns="http://www.w3.org/2000/svg"
                                                             xmlns:xlink="http://www.w3.org/1999/xlink" width="24px"
                                                             height="24px" viewBox="0 0 24 24" version="1.1">
															<g stroke="none" stroke-width="1" fill="none"
                                                               fill-rule="evenodd">
																<rect x="0" y="0" width="24" height="24"></rect>
																<path
                                                                    d="M14.2928932,16.7071068 C13.9023689,16.3165825 13.9023689,15.6834175 14.2928932,15.2928932 C14.6834175,14.9023689 15.3165825,14.9023689 15.7071068,15.2928932 L19.7071068,19.2928932 C20.0976311,19.6834175 20.0976311,20.3165825 19.7071068,20.7071068 C19.3165825,21.0976311 18.6834175,21.0976311 18.2928932,20.7071068 L14.2928932,16.7071068 Z"
                                                                    fill="#000000" fill-rule="nonzero"
                                                                    opacity="0.3"></path>
																<path
                                                                    d="M11,16 C13.7614237,16 16,13.7614237 16,11 C16,8.23857625 13.7614237,6 11,6 C8.23857625,6 6,8.23857625 6,11 C6,13.7614237 8.23857625,16 11,16 Z M11,18 C7.13400675,18 4,14.8659932 4,11 C4,7.13400675 7.13400675,4 11,4 C14.8659932,4 18,7.13400675 18,11 C18,14.8659932 14.8659932,18 11,18 Z"
                                                                    fill="#000000" fill-rule="nonzero"></path>
															</g>
														</svg>
													</span>
            <!--end::Svg Icon-->
            <!--end::Icon-->
            <!--begin::Input-->
            <input type="text" class="form-control form-control-solid px-15" name="search" value=""
                   wire:model.live="search_term"
                   placeholder="جستجو در تیکت ها...">
            <span wire:loading wire:target="search_term" style=" position: absolute; top: 24%; left: 10px; ">
                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
            </span>

            <!--end::Input-->
        </form>
        <!--end::Form-->

        <div class="separator separator-dashed my-3"></div>
        <!--begin::List-->
        <div class="scroll-y pe-5 h-200px h-lg-auto" data-kt-scroll="true"
             data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto"
             data-kt-scroll-dependencies="#kt_header, #kt_toolbar, #kt_footer, #kt_chat_contacts_header"
             data-kt-scroll-wrappers="#kt_content, #kt_chat_contacts_body" data-kt-scroll-offset="0px"
             style="max-height: 554px;">


            {{-- Do your work, then step back. --}}


            @forelse($tickets as $ticket)
            <!--begin::User-->

                <div class="d-flex flex-stack py-4 cursor-pointer" wire:click="show_ticket({{$ticket->id}})">

                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                        <!--begin::Avatar-->
                        <div class="symbol symbol-45px symbol-circle">
                            <span class="symbol-label bg-light-danger text-danger fs-6 fw-bolder"  style="color: {{$ticket->statusColor()}} !important; background-color:#f2f2f2!important; font-size: 11px !important;">{{$ticket->statusName()}}</span>
                        </div>
                        <!--end::Avatar-->
                        <!--begin::Details-->
                        <div class="ms-5">
                            <a class="fs-5 fw-bolder text-gray-900 text-hover-primary mb-2">{{$ticket->subject}}</a>
                            <div class="fw-bold text-gray-400">{{ Str::limit($ticket->summary, 20) }}</div>
                        </div>
                        <!--end::Details-->
                    </div>
                    <!--end::Details-->
                    <!--begin::Lat seen-->
                    <div class="d-flex flex-column align-items-end ms-2">
                        <span class="text-muted fs-7 mb-1">{{$ticket->ago_time()}}</span>
                    </div>
                    <!--end::Lat seen-->
                </div>
                <!--end::User-->
                <!--begin::Separator-->
                <div class="separator separator-dashed d-none"></div>
                <!--end::Separator-->
            @empty

                        تیکتی موجود نیست

            @endforelse
        </div>

    </div>
    <!--end::List-->
</div>
<!--end::Card body-->


<div>
</div>
