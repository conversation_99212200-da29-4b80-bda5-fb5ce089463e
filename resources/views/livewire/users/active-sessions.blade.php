<div class="card-body px-0 overflow-auto direction-rtl">
    @if(!(\App\Models\Session::is_valid_device_number()))
        <div class="p-5 direction-rtl">
            <!--begin::Information-->
            <div class="d-flex align-items-center rounded py-3 px-3 bg-light-warning">
                <!--begin::Icon-->
                <!--begin::Svg Icon | path: icons/duotone/Code/Info-circle.svg-->
                <span class="svg-icon svg-icon-3x svg-icon-warning me-5">
														<svg xmlns="http://www.w3.org/2000/svg" width="24px"
                                                             height="24px" viewBox="0 0 24 24" version="1.1">
															<circle fill="#000000" opacity="0.3" cx="12" cy="12"
                                                                    r="10"></circle>
															<rect fill="#000000" x="11" y="10" width="2" height="7"
                                                                  rx="1"></rect>
															<rect fill="#000000" x="11" y="7" width="2" height="2"
                                                                  rx="1"></rect>
														</svg>
													</span>
                <!--end::Svg Icon-->
                <!--end::Icon-->
                <!--begin::Description-->
                <div class="text-gray-600 fw-bold fs-6">
                    تعداد دستگاه های مجاز شما از حد مجاز عبور کرده است. لطفا یک دستگاه را از زیر انتخاب کرده و آن را
                    خارج نمایید.
                </div>
                <!--end::Description-->
            </div>
            <!--end::Information-->
        </div>

    @endif

    <!--start::Title-->
    <div class="fw-boldest fs-6 text-gray-800 min-w-700px" data-inbox="list">
        <!--begin::Item-->
        <div class="d-flex align-items-center card-px py-3">
            <!--begin::Toolbar-->
            <div class="d-flex align-items-center">
                <!--begin::Author-->
                <div class="d-flex align-items-center flex-wrap w-xxl-200px me-3">
                    دستگاه
                </div>
                <!--end::Author-->
                <span class="text-muted me-5" style="visibility:hidden">
                    <!--begin::Svg Icon | path: assets/media/icons/duotone/Devices/Tablet.svg-->
                    <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <path d="M6.5,4 L6.5,20 L17.5,20 L17.5,4 L6.5,4 Z M7,2 L17,2 C18.1045695,2 19,2.8954305 19,4 L19,20 C19,21.1045695 18.1045695,22 17,22 L7,22 C5.8954305,22 5,21.1045695 5,20 L5,4 C5,2.8954305 5.8954305,2 7,2 Z" fill="#000000" fill-rule="nonzero"/>
                        <polygon fill="#000000" opacity="0.3" points="6.5 4 6.5 20 17.5 20 17.5 4"/>
                    </svg></span>
                    <!--end::Svg Icon-->
                </span>
            </div>
            <!--end::Toolbar-->
            <!--begin::Info-->
            <div class="flex-grow-1 me-2">
                توضیحات
            </div>
            <!--end::Info-->
            <!--begin::Datetime-->
            <div class="w-300px text-start">
                آخرین فعالیت
            </div>
            <!--end::Datetime-->
            <!--begin::Actions-->
            <div class="w-55px d-flex align-items-center me-3 justify-content-center">
                اقدامات
            </div>
            <!--end::Actions-->
        </div>
        <!--end::Item-->
    </div>
    <!--end::Title-->
    <!--begin::Items-->
    @foreach($sessions as $session)
        <div class="min-w-700px" data-inbox="list">
            <!--begin::Item-->
            <div class="d-flex align-items-center bg-hover-light card-px py-3">
                <!--begin::Toolbar-->
                <div class="d-flex align-items-center">
                    @if($session->device_is_phone())
                        <span class="text-muted me-5">
                            <!--begin::Svg Icon | path: assets/media/icons/duotone/Devices/Tablet.svg-->
                            <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                <path d="M6.5,4 L6.5,20 L17.5,20 L17.5,4 L6.5,4 Z M7,2 L17,2 C18.1045695,2 19,2.8954305 19,4 L19,20 C19,21.1045695 18.1045695,22 17,22 L7,22 C5.8954305,22 5,21.1045695 5,20 L5,4 C5,2.8954305 5.8954305,2 7,2 Z" fill="#000000" fill-rule="nonzero"/>
                                <polygon fill="#000000" opacity="0.3" points="6.5 4 6.5 20 17.5 20 17.5 4"/>
                            </svg></span>
                            <!--end::Svg Icon-->
                        </span>
                    @else
                        <span class="text-muted me-5">
                            <!--begin::Svg Icon | path: assets/media/icons/duotone/Devices/Laptop-macbook.svg-->
                            <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                <path d="M5,6 L19,6 C19.5522847,6 20,6.44771525 20,7 L20,17 L4,17 L4,7 C4,6.44771525 4.44771525,6 5,6 Z" fill="#000000"/>
                                <rect fill="#000000" opacity="0.3" x="1" y="18" width="22" height="1" rx="0.5"/>
                            </svg></span>
                            <!--end::Svg Icon-->
                        </span>
                    @endif
                    <!--begin::Author-->
                    <div class="d-flex align-items-center flex-wrap w-xxl-200px me-3" data-bs-toggle="view">
                        <a href="#" class="fw-bold text-gray-800 text-hover-primary">{{$session->device_name()}}</a>
                    </div>
                    <!--end::Author-->
                </div>
                <!--end::Toolbar-->
                <!--begin::Info-->
                <div class="flex-grow-1 me-2" data-bs-toggle="view">
                    <div>
                        <span class="fw-bolder fs-6 me-5">آی پی : {{$session->ip_address}}</span>
                        <span class="fw-bolder fs-6 me-5">پلتفرم : {{$session->device_platform()}}</span>
                        <span class="fw-bolder fs-6 me-5">مرورگر : {{$session->device_browser()}}</span>
                    </div>
                </div>
                <!--end::Info-->
                <!--begin::Datetime-->
                <div class="fw-normal w-300px text-start text-muted"
                     data-bs-toggle="view">{{$session->last_activity_time}}</div>
                <!--end::Datetime-->
                <!--begin::Actions-->
                <div class="w-55px d-flex align-items-center me-3" data-inbox="actions">
                    <a wire:click="destroy_session('{{$session->ip_address}}')" class="btn btn-sm btn-danger">خروج</a>
                </div>
                <!--end::Actions-->
            </div>
            <!--end::Item-->
        </div>
@endforeach
<!--end::Items-->
</div>
