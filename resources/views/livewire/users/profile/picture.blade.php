<div>
{{-- Knowing others is intelligence; knowing yourself is true wisdom. --}}

<!--begin::Form row-->
    <div class="row mb-8 direction-rtl">
        <label class="col-lg-12 col-form-label text-center fw-boldest mb-5">ویرایش تصویر حساب کاربری</label>

        <div class="col-lg-12 text-center">

                @if ($profile_photo)
                    <script>
                        livewire.on('avatar_preview_updated', image => {
                            var vEl = document.getElementById('upload-demo'),
                                vanilla = new Croppie(vEl, {
                                    url: image,
                                    enableOrientation: true,
                                    viewport: {width: 400, height: 400, type: 'circle'},
                                    boundary: {width: 400, height: 400,},
                                    enforceBoundary: true,
                                });
                            vEl.addEventListener('update', function (ev) {
                                // console.log('vanilla update', ev);
                            });
                            document.querySelector('.vanilla-result').addEventListener('click', function (ev) {
                                ev.preventDefault();
                                let image_crop_data = vanilla.get();
                                let rotate = 0;
                                // console.log(image_crop_data);
                                if (image_crop_data.orientation == 1) {
                                    rotate = 0;
                                } else if (image_crop_data.orientation == 3) {
                                    rotate = 180;
                                } else if (image_crop_data.orientation == 6) {
                                    rotate = -90;
                                } else if (image_crop_data.orientation == 8) {
                                    rotate = 90;
                                }

                            @this.save(image_crop_data.points, rotate, image_crop_data.zoom);

                            });

                            $('.vanilla-rotate').on('click', function (ev) {
                                ev.preventDefault();
                                vanilla.rotate(parseInt($(this).data('deg')));
                            });


                        });
                    </script>
                    <div id="upload-demo">
                    </div>
                    <div class="actions text-center d-flex justify-content-center">
                        <button class="btn btn-light-primary fw-bold btn-sm vanilla-rotate me-3" data-deg="-90">چرخش به
                            راست
                            <!--begin::Svg Icon | path: assets/media/icons/duotone/Navigation/Left-3.svg-->
                            <span class="svg-icon svg-icon-muted svg-icon-2 ms-3"><svg class="flip-image"
                                                                                       xmlns="http://www.w3.org/2000/svg"
                                                                                       xmlns:xlink="http://www.w3.org/1999/xlink"
                                                                                       width="24px" height="24px"
                                                                                       viewBox="0 0 24 24"
                                                                                       version="1.1">
                            <path
                                d="M4.7071045,12.7071045 C4.3165802,13.0976288 3.68341522,13.0976288 3.29289093,12.7071045 C2.90236664,12.3165802 2.90236664,11.6834152 3.29289093,11.2928909 L9.29289093,5.29289093 C9.67146987,4.914312 10.2810563,4.90106637 10.6757223,5.26284357 L16.6757223,10.7628436 C17.0828413,11.136036 17.1103443,11.7686034 16.7371519,12.1757223 C16.3639594,12.5828413 15.7313921,12.6103443 15.3242731,12.2371519 L10.0300735,7.38413553 L4.7071045,12.7071045 Z"
                                fill="#000000" fill-rule="nonzero"
                                transform="translate(10.000001, 8.999997) scale(-1, -1) rotate(90.000000) translate(-10.000001, -8.999997) "/>
                            <path
                                d="M20,8 C20.5522847,8 21,8.44771525 21,9 C21,9.55228475 20.5522847,10 20,10 L13.5,10 C12.9477153,10 12.5,10.4477153 12.5,11 L12.5,21.0415946 C12.5,21.5938793 12.0522847,22.0415946 11.5,22.0415946 C10.9477153,22.0415946 10.5,21.5938793 10.5,21.0415946 L10.5,11 C10.5,9.34314575 11.8431458,8 13.5,8 L20,8 Z"
                                fill="#000000" fill-rule="nonzero" opacity="0.5"
                                transform="translate(15.750000, 15.020797) scale(-1, 1) translate(-15.750000, -15.020797) "/>
                        </svg></span>
                            <!--end::Svg Icon-->
                        </button>
                        <button class="btn btn-light-primary fw-bold btn-sm vanilla-rotate me-10" data-deg="90">چرخش به
                            چپ
                            <!--begin::Svg Icon | path: assets/media/icons/duotone/Navigation/Left-3.svg-->
                            <span class="svg-icon svg-icon-muted svg-icon-2 ms-3"><svg
                                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                    width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <path
                                d="M4.7071045,12.7071045 C4.3165802,13.0976288 3.68341522,13.0976288 3.29289093,12.7071045 C2.90236664,12.3165802 2.90236664,11.6834152 3.29289093,11.2928909 L9.29289093,5.29289093 C9.67146987,4.914312 10.2810563,4.90106637 10.6757223,5.26284357 L16.6757223,10.7628436 C17.0828413,11.136036 17.1103443,11.7686034 16.7371519,12.1757223 C16.3639594,12.5828413 15.7313921,12.6103443 15.3242731,12.2371519 L10.0300735,7.38413553 L4.7071045,12.7071045 Z"
                                fill="#000000" fill-rule="nonzero"
                                transform="translate(10.000001, 8.999997) scale(-1, -1) rotate(90.000000) translate(-10.000001, -8.999997) "/>
                            <path
                                d="M20,8 C20.5522847,8 21,8.44771525 21,9 C21,9.55228475 20.5522847,10 20,10 L13.5,10 C12.9477153,10 12.5,10.4477153 12.5,11 L12.5,21.0415946 C12.5,21.5938793 12.0522847,22.0415946 11.5,22.0415946 C10.9477153,22.0415946 10.5,21.5938793 10.5,21.0415946 L10.5,11 C10.5,9.34314575 11.8431458,8 13.5,8 L20,8 Z"
                                fill="#000000" fill-rule="nonzero" opacity="0.5"
                                transform="translate(15.750000, 15.020797) scale(-1, 1) translate(-15.750000, -15.020797) "/>
                        </svg></span>
                            <!--end::Svg Icon-->
                        </button>

                        <button class="btn btn-light-primary fw-bold btn-sm vanilla-result me-3"
                                wire:loading.class="disabled" wire:target="save">
                            <div wire:loading.remove wire:target="save">ذخیره نهایی</div>
                            <div wire:loading wire:target="save">
                                در حال ذخیره سازی...
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                            </div>
                        </button>
                        <div class="btn btn-light-danger fw-bold btn-sm" data-deg="90" wire:click="cancel()"
                             wire:loading.class="disabled">
                            <div wire:loading.remove wire:target="cancel">
                                انصراف
                            </div>
                            <div wire:loading wire:target="cancel">
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                            </div>
                        </div>
                    </div>
                @else
                    <form wire:submit="save">

                        <div wire:loading.remove>

                            <label class="custom-file-upload d-block" for="fileInput">
                                <!--begin::Input group-->
                                <div class="fv-row">
                                    <!--begin::Dropzone-->
                                    <div class="dropzone" id="dropContainer">
                                        <!--begin::Message-->
                                        <div class="dz-message needsclick align-items-center">
                                            <!--begin::Icon-->
                                            <i class="bi bi-file-earmark-arrow-up text-primary fs-3x"></i>
                                            <!--end::Icon-->
                                            <!--begin::Info-->
                                            <div class="ms-4 direction-rtl text-justify">
                                                <h3 class="fs-5 fw-bolder text-gray-900 mb-1">عکس خود را درگ کنید تا
                                                    آپلود شود</h3>
                                                <span class="fs-7 fw-bold text-gray-400">حداکثر حجم 1 مگابایت</span>
                                            </div>
                                            <!--end::Info-->
                                        </div>
                                    </div>
                                    <!--end::Dropzone-->
                                </div>
                                <!--end::Input group-->
                                <input id="fileInput" type="file" wire:model.live="profile_photo">
                            </label>
                            @error('profile_photo') <span class="error">{{ $message }}</span> @enderror
                            <div class="btn-container d-flex align-items-center justify-content-end mt-5">

                                <button type="button" class="d-none btn btn-light-primary fw-bold btn-sm"
                                        wire:click="save()">بارگزاری
                                    عکس
                                </button>

                                @if((Auth::user()->profile))

                                    <div class="btn btn-light-danger fw-bold btn-sm ms-5" data-deg="90"
                                         wire:click="remove_old_image()"
                                         wire:loading.class="disabled">
                                        <div wire:loading.remove>
                                            حذف عکس فعلی
                                        </div>
                                        <div wire:loading wire:target="remove_old_image">
                                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                        </div>
                                    </div>

                                @endif
                            </div>
                        </div>

                    </form>
                    <div wire:loading wire:target="profile_photo" class=" w-100" id="uploading_message">

                        <div class="progress">

                            <div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0"
                                 aria-valuemax="100"></div>
                        </div>
                    </div>
                    <div class="form-text mt-10">لطفا از تصاویر مربع شکل و با حداقل اندازه 400 در 400 پیکسل آپلود
                        نمایید.
                        <a href="#" class="fw-bold">راهنمایی بیشتر</a>.
                    </div>
                @endif
                {{-- <div wire:loading wire:target="save">در حال ذخیره سازی...</div> --}}

        </div>
    </div>

    <!--end::Form row-->

</div>
@push('scripts')
    <script>
        (window.dragAndDrop = function () {
            $('body').find('#dropContainer').on('dragenter dragover', function (e) {
                e.preventDefault();
                $('body').addClass('dragOperation');
            });
            $('body').find('#dropContainer').on('drop', function (e) {
                e.preventDefault();
                $('body').removeClass('dragOperation');
            @this.upload('profile_photo', e.originalEvent.dataTransfer.files[0], (upladedFilename) => {
            }, () => {
            }, (event) => {
                let valeur = event.detail.progress;
                $('.progress-bar').css('width', valeur + '%').attr('aria-valuenow', valeur);

                if (valeur == 100) {
                    $('#uploading_message').html('در حال پردازش');
                }
            });
            });
        })();
         Livewire.on('reload_drag_and_drop', () => {
            dragAndDrop();
        });
         Livewire.on('refresh_profile_photo', () => {
            dragAndDrop();
        });
    </script>
@endpush

