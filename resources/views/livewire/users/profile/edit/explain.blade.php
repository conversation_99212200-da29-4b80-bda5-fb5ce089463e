<div>
{{-- Success is as dangerous as failure. --}}

<!--begin::Heading-->
    <div class="pb-10 pb-lg-15">
        <h3 class="fw-bolder text-dark display-6">توضیحات</h3>
        <div class="text-muted fw-bold fs-3">مشکلی پیش اومده؟
            <a href="#" class="text-primary fw-bolder">پشتیبان</a></div>
    </div>
    <!--begin::Heading-->
    <div class="row">
        <div class="col-xl-6">
            <!--begin::Input-->
            <div class="mb-10">
                <label class="fs-6 form-label fw-bolder text-dark form-label">ایسنتاگرام</label>
                <input type="text" class="form-control form-control-lg form-control-solid" name="instagram"
                       wire:model.blur="instagram" wire:loading.class="disabled" placeholder="" value=""/>

                @if ($errors->has('instagram'))
                    <span class="invalid-feedback d-block" role="alert">
                     <strong>{{ $errors->first('instagram') }}</strong>
                </span>
                @endif
            </div>
            <!--end::Input-->
        </div>
        <div class="col-xl-6">
            <!--begin::Input-->
            <div class="mb-10">
                <label class="fs-6 form-label fw-bolder text-dark form-label">توییتر</label>
                <input type="text" class="form-control form-control-lg form-control-solid" name="twitter"
                       wire:model.blur="twitter" wire:loading.class="disabled" placeholder="" value=""/>

                @if ($errors->has('twitter'))
                    <span class="invalid-feedback d-block" role="alert">
                     <strong>{{ $errors->first('twitter') }}</strong>
                </span>
                @endif
            </div>
            <!--end::Input-->
        </div>
    </div>
    <div class="row">
        <div class="col-xl-6">
            <!--begin::Input-->
            <div class="mb-10">
                <label class="fs-6 form-label fw-bolder text-dark form-label">لینکداین</label>
                <input type="text" class="form-control form-control-lg form-control-solid" name="linkedin"
                       wire:model.blur="linkedin" wire:loading.class="disabled" placeholder="" value=""/>

                @if ($errors->has('linkedin'))
                    <span class="invalid-feedback d-block" role="alert">
                     <strong>{{ $errors->first('linkedin') }}</strong>
                </span>
                @endif
            </div>
            <!--end::Input-->
        </div>
        <div class="col-xl-6">
            <!--begin::Input-->
            <div class="mb-10">
                <label class="fs-6 form-label fw-bolder text-dark form-label">پورتفولیو</label>
                <input type="text" class="form-control form-control-lg form-control-solid" name="portfolio"
                       wire:model.blur="portfolio" wire:loading.class="disabled" placeholder="" value=""/>

                @if ($errors->has('portfolio'))
                    <span class="invalid-feedback d-block" role="alert">
                     <strong>{{ $errors->first('portfolio') }}</strong>
                </span>
                @endif
            </div>
            <!--end::Input-->
        </div>
    </div>
    <div class="row">
        <!--begin::Input-->
        <div class="mb-10">
            <label class="fs-6 form-label fw-bolder text-dark form-label">درباره من
                <span wire:loading wire:target="about">
                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                    </span>
            </label>
            <textarea class="form-control form-control-lg form-control-solid" name="about" placeholder="" value=""
                      wire:model.blur="about" wire:loading.class="disabled"></textarea>
            <span class="form-text text-muted">درباره خودتان بیشتر توضیح بدید</span>
            @if ($errors->has('about'))
                <span class="invalid-feedback d-block" role="alert">
                     <strong>{{ $errors->first('about') }}</strong>
                </span>
            @endif
        </div>
        <!--end::Input-->
    </div>

</div>
