<div>
    {{-- In work, do what you enjoy. --}}
    <!--begin::Heading-->
    <div class="pb-10 pb-lg-15">
        <h3 class="fw-bolder text-dark display-6">مشخصات کاربری</h3>
        <div class="text-muted fw-bold fs-3">مشکلی پیش اومده؟
            <a href="#" class="text-primary fw-bolder">پشتیبان</a></div>
    </div>
    <!--begin::Heading-->
    <!--begin::Form Group-->
    <div class="fv-row row mb-10">

        <div class="col-lg-6 col-md-6  mt-10">
            <label class="fs-6 form-label fw-bolder text-dark">نام
                <span wire:loading wire:target="first_name">
                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </span>
            </label>
            <input type="text" class="form-control form-control-lg form-control-solid" name="first_name" placeholder=""
                   wire:model.blur="first_name" wire:loading.class="disabled"/>
            @if ($errors->has('first_name'))
                <span class="invalid-feedback d-block" role="alert">
                     <strong>{{ $errors->first('first_name') }}</strong>
                </span>
            @endif
        </div>
        <div class="col-lg-6 col-md-6  mt-10">
            <label class="fs-6 form-label fw-bolder text-dark">نام خانوادگی
                <span wire:loading wire:target="last_name">
                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </span>
            </label>
            <input type="text" class="form-control form-control-lg form-control-solid" name="last_name" placeholder=""
                   wire:model.blur="last_name" wire:loading.class="disabled"/>
            @if ($errors->has('last_name'))
                <span class="invalid-feedback d-block" role="alert">
                     <strong>{{ $errors->first('last_name') }}</strong>
                </span>
            @endif
        </div>
        @if(!empty($phone_number))
            <div class="col-lg-12 col-md-12 mt-10">
                <label class="fs-6 form-label fw-bolder text-dark">
                    شماره موبایل
                </label>
                <input type="text" class="form-control form-control-lg form-control-solid direction-ltr"
                       name="last_name"
                       disabled value="{{$phone_number}}"
                />
            </div>
        @else
            <div class="col-lg-12 col-md-12 mt-10">
                <div class="alert alert-danger d-flex align-items-center p-5 mb-10">
                    <i class="ki-duotone ki-shield-tick fs-2hx text-danger me-4"><span class="path1"></span><span
                            class="path2"></span></i>
                    <div class="d-flex flex-column">
                        <h4 class="mb-1 text-danger">شما هنوز موبایل خود را وارد نکرده اید.</h4>
                        <span>
                            ما اطلاع رسانی های مهم را از طریق پیامک به شما ارسال میکنیم. پس پیشنهاد میکنیم حتما شماره موبایل خود را ثبت نمایید.
                        </span>
                    </div>
                </div>
                <a class="btn btn-dark me-2 mb-2" onclick="$('#newMobileHolder').slideToggle(300)">
                    <i class="las la-plus fs-2 me-2"></i> ثبت شماره موبایل
                </a>
            </div>

            <div class="col-lg-12 col-md-12 mt-10" id="newMobileHolder" style="display: none" wire:ignore.self>
                <div class="rounded border p-10">
                    <!--begin::Title-->
                    <h4 class="fs-5 fw-semibold text-gray-800">
                        شماره موبایل خود را وارد کنید
                    </h4>
                    <!--end::Title-->

                    @if(!$verify_mode)
                        <!--begin::Title-->
                        <div class="d-flex">
                            <input id="" type="text" class="form-control form-control-solid me-3 flex-grow-1"
                                   pattern="[0-9.]+"
                                   wire:model="newPhoneNumber"
                            >
                            <button
                                wire:click="submitToken()" class="btn btn-light-primary fw-bold flex-shrink-0"
                                wire:loading.class="disabled" wire:target="submitToken">
                                <div wire:loading.remove wire:target="submitToken"> ارسال کد
                                    فعال سازی
                                </div>
                                <div wire:loading wire:target="submitToken">
                                    در حال ارسال...
                                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                </div>
                            </button>

                        </div>
                        @if ($errors->has('newPhoneNumber'))
                            <span class="invalid-feedback d-block" role="alert">
                                            <strong>{{ $errors->first('newPhoneNumber') }}</strong>
                                </span>
                        @endif
                        <!--end::Title-->
                    @else
                        <div>
                            <!--begin::Form group-->
                            <div class="fv-row mb-10">
                                <label class="label-required form-label fs-6 fw-bolder text-dark">کد ارسالی</label>
                                <label2>
                                    <br/>
                                    کد تایید به {{ $newPhoneNumber }} ارسال شده است. <br/>
                                </label2>

                                <!--end::Action-->
                                <div class="fv-row mb-10">
                                    <form onsubmit="event.preventDefault();" onload="FocusOnInput">
                                        <div class="row">
                                            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12">
                                                <div class="otp-password flex-stack flex-row-reverse"
                                                     style="display: flex;">
                                                    <div id="inputOtp" name="otp" input-key="otp" data-vv-as="کد تایید"
                                                         is-input-num="true"
                                                         value="" style="display: flex; direction: ltr;" wire:ignore>
                                                        <input pattern="[0-9]" type="number"
                                                               class="form-control form-control-solid otp-input text-align-center btn ms-3 ms-lg-6"
                                                               wire:model.live="submittedToken"
                                                               maxlength="{{$token_size}}">
                                                    </div>
                                                    <button tabindex="5"
                                                            class="btn btn-secondary fw-bolder fs-6 px-8 py-2 my-3 me-3"
                                                            style="flex-grow: 1"
                                                            wire:click="refresh_token()">

                                                        <span class="js-timeout fs-1 fw-bolder text-dark" wire:ignore>2:00</span>
                                                        <span id="send-again" style="display: none;margin-right: 10px; position: relative; top: -1px; "> ارسال مجدد </span>
                                                    </button>
                                                </div>
                                                <button  class="btn btn-primary fw-bolder fs-6  my-3 me-3 w-100"
                                                        wire:loading.class="disabled" wire:target="submit_token" wire:click="submit_token">
                                                    <div wire:loading.remove wire:target="submit_token">ذخیره نهایی</div>
                                                    <div wire:loading wire:target="submit_token">
                                                        در حال ذخیره سازی...
                                                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                                    </div>
                                                </button>

                                            </div>
                                        </div>
                                    </form>
                                </div>

                                <!--end::Signin Form-->

                                @if ($errors->has('token_from_user'))
                                    <div class="fv-plugins-message-container">
                                        <div data-field="token_from_user" data-validator="token_from_user"
                                             class="fv-help-block">
                                            {{ $errors->first('token_from_user') }}
                                        </div>
                                    </div>
                                @endif
                                @if ($errors->has('alert'))
                                    <div class="fv-plugins-message-container">
                                        <div data-field="alert" data-validator="alert" class="fv-help-block">
                                            {{ $errors->first('alert') }}
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <!--end::Form group-->
                        </div>

                    @endif
                </div>
            </div>
        @endif

    </div>
    @script
    <script>
        // Convert English digits to Persian
        String.prototype.toPersianDigit = function () {
            const id = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
            return this.replace(/[0-9]/g, w => id[+w]);
        };

        // Convert Persian digits to English
        String.prototype.toEnglishDigits = function () {
            const id = { '۰': '0', '۱': '1', '۲': '2', '۳': '3', '۴': '4', '۵': '5', '۶': '6', '۷': '7', '۸': '8', '۹': '9' };
            return this.replace(/[^0-9.]/g, w => id[w] || w);
        };

        $(document).ready(function () {
            let ctrlDown = false;
            const ctrlKey = 17, cmdKey = 91;
            let tokens = [];
            window.inputFlag = true;

            function checkToken() {
                if ($('#inputOtp').val().length === $('.otp-input').length) {
                    @this.set('code_array', $('#inputOtp').val());
                }
            }

            $(document).on('keydown keyup', function (e) {
                if (e.keyCode === ctrlKey || e.keyCode === cmdKey) ctrlDown = e.type === 'keydown';
            });

            $('.otp-input').on('keydown', function (e) {
                const index = $(this).index();
                const isNumber = (e.keyCode >= 48 && e.keyCode <= 57) || (e.keyCode >= 96 && e.keyCode <= 105);
                const isNavigation = [37, 39, 9, 8].includes(e.keyCode);

                if (isNumber) {
                    if ($(this).val() && window.inputFlag) $('.otp-input').eq(index + 1).select();
                    else if (!window.inputFlag) $(this).val('');
                    window.inputFlag = true;
                } else if (e.keyCode === 39 && index < $('.otp-input').length - 1) {
                    $('.otp-input').eq(index + 1).select();
                    window.inputFlag = false;
                } else if (e.keyCode === 8) {
                    if (!$(this).val() && index > 0) $('.otp-input').eq(index - 1).select();
                    $(this).val('');
                } else if (e.keyCode === 37 && index > 0) {
                    $('.otp-input').eq(index - 1).select();
                    window.inputFlag = false;
                } else if (!isNavigation && ctrlDown && e.keyCode !== 86) {
                    e.preventDefault();
                }
            }).on('input', function () {
                tokens[$(this).index()] = $(this).val();
                $('#inputOtp').val(tokens.join(""));
                checkToken();
            });

            $('.otp-input').on('click', function () {
                $(this).select();
                window.inputFlag = false;
            }).on('paste', function (e) {
                e.preventDefault();
                const pastedData = (e.clipboardData || window.clipboardData).getData('Text').split('');
                let currentIndex = $(this).index();

                pastedData.forEach(char => {
                    if (currentIndex < $('.otp-input').length) {
                        $('.otp-input').eq(currentIndex).val(char).select();
                        currentIndex++;
                    }
                });

                $('.otp-input').each(function () {
                    tokens[$(this).index()] = $(this).val();
                });
                $('#inputOtp').val(tokens.join(""));
                checkToken();
            });

            function countdown() {
                clearInterval(window.interval);
                window.interval = setInterval(() => {
                    let timer = $('.js-timeout').text();
                    if (!timer) return;

                    let [minutes, seconds] = timer.split(':').map(num => parseInt(num, 10));
                    seconds -= 1;

                    if (minutes < 0) return;
                    if (seconds < 0 && minutes > 0) {
                        minutes -= 1;
                        seconds = 59;
                    }
                    $('.js-timeout').text(`${minutes}:${seconds < 10 ? '0' : ''}${seconds}`);

                    if (minutes === 0 && seconds === 0) {
                        $('#send-again').show(100);
                        clearInterval(window.interval);
                    }
                }, 1000);
            }

            function runCountDown(time) {
                const min = Math.floor(time / 60);
                const sec = time % 60;
                $('.js-timeout').text(`${min}:${sec < 10 ? '0' : ''}${sec}`);
                countdown();
            }

            runCountDown({{$static_count_down}});

            Livewire.on('refresh_timer', data => runCountDown(data.time));
            Livewire.on('run_validation', () => KTSignup.init().validate());
        });
    </script>

    @endscript
</div>
