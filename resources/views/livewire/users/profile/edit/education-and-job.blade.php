<div>
{{-- Because she competes with no one, no one can compete with her. --}}


<!--begin::Heading-->
    <div class="pb-10 pb-lg-15">
        <h3 class="fw-bolder text-dark display-6">اطلاعات تحصیلی و شغلی</h3>
        <div class="text-muted fw-bold fs-3">مشکلی پیش اومده؟
            <a href="#" class="text-primary fw-bolder">پشتیبان</a></div>
    </div>
    <!--begin::Heading-->
    <div class="row">
        <div class="col-xl-6">
            <!--begin::Form Group-->
            <div class="mb-10">
                <label class="fs-6 form-label fw-bolder text-dark">مقطع تحصیلی
                    <span wire:loading wire:target="updatedUserEducation" id="show_ed_loading">
                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                    </span>
                </label>
                <select type="text" class="form-select form-select-lg form-select-solid js-select"
                        data-control="select2"

                        name="university" wire:loading.class="disabled" wire:model.live="user_education"
                        onchange="@this.user_education = $(this).val();$('#show_ed_loading').css('display','inline-block')"
                        data-placeholder="مقطع تحصیلی را انتخاب نمایید"
                >
                    <option>مقطع تحصیلی را انتخاب نمایید</option>
                    @foreach($education as $data)
                        <option value="{{$data->id}}"
                                @if($user_education == $data->id)
                                selected
                            @endif
                        >{{$data->title}}</option>
                    @endforeach
                </select>
                @if ($errors->has('user_education'))
                    <span class="invalid-feedback d-block" role="alert">
                     <strong>{{ $errors->first('user_education') }}</strong>
                </span>
                @endif

            </div>
            <!--end::Form Group-->
        </div>
        <div class="col-xl-6">
            <!--begin::Form Group-->
            <div class="mb-10">
                <label class="fs-6 form-label fw-bolder text-dark">سمت شغلی

                    <span wire:loading wire:target="user_job_position" id="show_jp_loading">
                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                    </span>
                </label>
                <select class="form-select form-select-lg form-select-solid" name="user_job_position"
                        data-control="select2"
                        wire:loading.class="disabled" wire:model.live="user_job_position"
                        onchange="@this.user_job_position = $(this).val();$('#show_jp_loading').css('display','inline-block')"
                        data-placeholder="مقطع تحصیلی را انتخاب نمایید"
                >
                    <option>انتخاب نمایید</option>
                    @foreach($job_position as $data)
                        <option value="{{$data->id}}"
                                @if($user_job_position == $data->id)
                                selected
                            @endif
                        >{{$data->title}}</option>
                    @endforeach
                </select>
                @if ($errors->has('user_job_position'))
                    <span class="invalid-feedback d-block" role="alert">
                     <strong>{{ $errors->first('user_job_position') }}</strong>
                </span>
                @endif
            </div>
            <!--end::Form Group-->
        </div>
    </div>
    <div class="fv-row">
        <!--begin::Form Group-->
        <div class="mb-10">
            <label class="fs-6 form-label fw-bolder text-dark">رشته تحصیلی
                <span wire:loading wire:target="field_of_study">
                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </span>
            </label>
            <input type="text" class="form-control form-control-lg form-control-solid" name="field_of_study"
                   wire:model.blur="field_of_study"
                   value="{{$field_of_study}}" wire:loading.class="disabled" />
        </div>

        @if ($errors->has('filed_of_study'))
            <span class="invalid-feedback d-block" role="alert">
                     <strong>{{ $errors->first('filed_of_study') }}</strong>
            </span>
        @endif

    <!--end::Form Group-->
    </div>

</div>
