<!--begin::Scrolltop-->
@include("layouts.components.scrolltop")
<!--end::Scrolltop-->
<!--end::Main-->
<!--begin::Javascript-->
<!--begin::Global Javascript Bundle(used by all pages)-->
<script src="{{ asset('assets/plugins/global/plugins.bundle.js') }}"></script>
<script src="{{ asset('assets/js/scripts.bundle.js') }}"></script>
<!--end::Global Javascript Bundle-->
<!--begin::Page Custom Javascript(used by this page)-->
<script src="{{ asset('assets/js/custom/widgets.js') }}"></script>
{{--<script src="{{ asset('assets/js/custom/modals/select-location.js') }}"></script>--}}
<!--end::Page Custom Javascript-->



<script src="{{ asset('assets/plugins/global/persian-date.min.js') }}"></script>
<script src="{{ asset('assets/plugins/global/persian-datepicker.min.js') }}"></script>

@livewireScripts

<livewire:components.toast/>


@yield('footer_script')
@stack('scripts')

<!--end::Javascript-->
<script type="text/javascript">

    $(document).ready(function () {
        $('[data-kt-password-meter-control="visibility"]').on('click', function () {
            let input = $(this).siblings('input[data-type="password"]');
            let eyeIcon = $(this).find('.ki-eye');
            let eyeSlashIcon = $(this).find('.ki-eye-slash');

            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
                eyeIcon.removeClass('d-none');
                eyeSlashIcon.addClass('d-none');
            } else {
                input.attr('type', 'password');
                eyeIcon.addClass('d-none');
                eyeSlashIcon.removeClass('d-none');
            }
        });
    });


    /**
     * Create HTML elements
     *
     * @param {String} tagname
     * @param {Object} attribute
     * @param {String | Array | Undefined} content
     * @return {HTMLElement} el
     */
    function createElement(tagname, attribute, content) {
        var el = document.createElement(tagname);
        if (Object.keys(attribute).length !== 0) {
            for (var key in attribute) {
                el.setAttribute(key, attribute[key]);
            }
        }
        if (typeof content !== "undefined") {
            if (Array.isArray(content)) {
                for (var item of content) {
                    if (item instanceof HTMLElement) {
                        el.appendChild(item);
                    } else {
                        el.innerHTML += item;
                    }
                }
            } else {
                if (content instanceof HTMLElement) {
                    el.appendChild(content);
                } else {
                    el.innerText = content;
                }
            }
        }
        return el;
    }

    // end::createElement


    /**
     * Add loading element
     */
    function loading(indicator) {
        if (indicator) {
            $('body').append(
                createElement(
                    'div',
                    {class: 'loading-container'},
                    [
                        createElement(
                            'div',
                            {class: 'spinner-border mb-5', role: 'status'},
                            createElement(
                                'span',
                                {class: 'visually-hidden'}
                            )
                        ),
                        createElement(
                            'span',
                            {},
                            'لطفا منتظر بمانید'
                        )
                    ]
                )
            )
        } else {
            $('.loading-container').remove();
        }
    }

    // end::loading

    Livewire.on('alert', (event) => {
        param = event[0];
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": true,
            "progressBar": true,
            "positionClass": "toast-top-center",
            "preventDuplicates": true,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "10000",
            "timeOut": "10000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        }
        toastr[param['type']](param['message']);
    });

    Livewire.on('sweet', (event) => {
        param = event[0];
        Swal.fire({
            text: param['message'],
            icon: param['type'],
            buttonsStyling: false,
            confirmButtonText: param['btn'],
            customClass: {
                confirmButton: "btn btn-primary",
            }
        });
    });

    Livewire.on('user_updated', () => {
        $('#edit_users').modal('hide');
    });

    Livewire.on('close_modal', () => {
        $('.modal').modal('hide');
    });

    Livewire.on('refresh_page_from_back', () => {
        window.location.reload();
    });


    Livewire.on('hide_element', (elementId) => {
       let el = document.getElementById(elementId[0].elementId)
        el.classList.add('hide')
    });
    Livewire.on('close_drawer', (id) => {
        $('#' + id).trigger('click');
    });
    Livewire.on('close_tooltip', () => {
        $(".tooltip").css('display', 'none');
    });


    Livewire.on('render_select2', () => {
        $('.form-select').select2();
    })
    Livewire.on('hide_kt_menu', () => {
        $('[data-kt-menu="true"]').hide();
    })

    /**
     * change url from livewire
     */
    document.addEventListener('DOMContentLoaded', function () {
        Livewire.on('url_change', (url) => {
            history.pushState(null, null, url);
        });
    });


    //only for logging livewire in console
    let logComponentsData = function () {
        window.livewire.components.components().forEach(component => {
            console.log(component.name);
            console.log(component.data);
        });
    };


    Livewire.on('show_console', () => {
        logComponentsData();
    });

    //run to to top animation when scroll_top event fire from livewire
    Livewire.on('scroll_top', () => {
        $("html, body").animate({scrollTop: 0}, "slow");
        return false;
    })

    //

    function changeLivewireModel(id, modelValue) {
        console.log("add: " + id + "|" + modelValue)

        $('#' + id).val(modelValue)
        let element = document.getElementById(id);
        element.dispatchEvent(new Event('input'));
    }


    function copyToClipboard(elementId) {
        document.getElementById(elementId).addEventListener('click', function () {
            const input = document.getElementById(elementId + 'Target');
            const icon = this.querySelector('i');
            // Copy the input value to clipboard
            navigator.clipboard.writeText(input.value).then(function () {
                // Change the icon class
                icon.classList.remove('la-copy');
                icon.classList.add('la-check');

                // Optionally change it back after a delay
                setTimeout(() => {
                    icon.classList.remove('la-check');
                    icon.classList.add('la-copy');
                }, 2000);
            });
        });
    }

    Livewire.on('swal:confirm', (params) => {
        Swal.fire({
            text: params[0].title,
            icon: params[0].icon,
            buttonsStyling: false,
            showCancelButton: true,
            confirmButtonText: params[0].confirmButtonText,
            cancelButtonText: params[0].cancelButtonText,
            customClass: {
                confirmButton: "btn fw-bold btn-danger",
                cancelButton: "btn fw-bold btn-light"
            }
        }).then((result) => {
            if (result.isConfirmed) {
                Livewire.dispatch(params[0].onConfirmed.dispatch, params[0].onConfirmed.params);
            }
        });
    });

</script>


<script type="text/javascript">
    (function (c, l, a, r, i, t, y) {
        c[a] = c[a] || function () {
            (c[a].q = c[a].q || []).push(arguments)
        };
        t = l.createElement(r);
        t.async = 1;
        t.src = "https://www.clarity.ms/tag/" + i;
        y = l.getElementsByTagName(r)[0];
        y.parentNode.insertBefore(t, y);
    })(window, document, "clarity", "script", "okfe6f79ry");
</script>

</body>
<!--end::Body-->
</html>
