<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<!--begin::Head-->

<head>
    @include("layouts.components.meta")
    <title>@if(View::hasSection('title')) @yield('title') | آی روم @else آی روم @endif </title>
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @stack('meta_tags')


    <!--begin::Global Stylesheets Bundle(used by all pages)-->
    @livewireStyles
    @yield('header_style')
    <link href="{{ asset('assets/plugins/global/plugins.bundle.css?ver=1.0.0.2') }}" rel="stylesheet" type="text/css"/>
    <link href="{{ asset('assets/css/style.bundle.css') }}" rel="stylesheet" type="text/css"/>
    <link href="{{ asset('assets/css/changed_style.css') }}" rel="stylesheet" type="text/css"/>

    <link href="{{ asset('assets/plugins/global/persian-datepicker.min.css') }}" rel="stylesheet" type="text/css"/>
    @stack('header_style_after')

    <style>
        ._hj-YR-2H__Feedback__container {
            z-index: 1000 !important;
        }

        #crisp-chatbox {
            z-index: 1000 !important;
        }
    </style>

    <!--end::Global Stylesheets Bundle-->

</head>
