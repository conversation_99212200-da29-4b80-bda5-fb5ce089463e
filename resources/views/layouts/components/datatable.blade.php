<script>
        $(document).ready(function (){

            window.table = $('.amanj-datatable').DataTable({

                processing: true,
                serverSide: true,
                ajax: {
                    url:'{!! route('datatables.data') !!}',
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [
                    { data: 'id', name: 'id' },
                    { data: 'user_name', name: 'user_name' },
                    { data: 'first_name', name: 'user_name' },
                    { data: 'last_name', name: 'user_name' },
                    { data: 'email', name: 'email' },
                    { data: null, name: null },

                ],
                retrieve: true,
                "deferRender": true,
                fixedHeader: {
                    header: true,
                    // footer: false,
                    headerOffset: 65
                },
                "order": [[0, "desc"]],
                columnDefs: [
                    // { "targets": [0], "searchable": false, "orderable": false, "visible": true}
                ],
                "stateSave": true,
                "stateSaveParams": function (settings, data) {
                    data.search.search = "";
                },
                "oLanguage": {
                    "sSearch": "جستجو: ",
                    "sShow": "تعداد نمایش در صفحه: ",
                    "next": "بعدی",
                    "sInfo": "نمایش _START_ تا _END_ از _TOTAL_ ورودی",
                    "sInfoEmpty": "بدون داده",
                    "sInfoFiltered": "( _MAX_ داده فیلتر شده است)",
                    "sLengthMenu": "تعداد نمایش در صفحه:  _MENU_ ",
                    "sZeroRecords": "بدون اطلاعات",
                    "Next": "بعدی",
                    "sPrevious": "قبلی",
                },
                "oPaginate": {
                    "Next": "بعدی",
                    "sPrevious": "قبلی",
                },
                dom: 'fBlrtip',
                buttons: [
                    // 'colvis',
                    // 'csv',
                    // 'excel',
                    //  'print',
                    // 'pageLength',
                    // 'pdf',
                    {
                        extend: 'excel',
                        text: 'دریافت Excel',
                        exportOptions: {
                            columns: ':visible'
                        },
                    },
                    // header: true,
                    {
                        extend: 'colvis',
                        text: 'فیلتر ستون ها ',
                    },
                    {
                        extend: 'print',
                        footer: true,
                        exportOptions: {
                            columns: ':visible',
                            filter: 'applied', order: 'current'
                        },

                        text: 'چاپ / PDF',
                        autoPrint: true,
                        customize: function (win) {
                            $(win.document.body).find('table').after('');
                            $(win.document.body).find('table').before('<div class="row"><div class="col-md-1"></div><div class="col-md-10">\n' +
                                '               <br /><br /> <div class="project-det2ail project-report" style="    direction: rtl;    text-align: right; border:0px">\n' +

                                '                    <span class="project-title">\n' + window.document.title +
                                '                    </span>\n' +
                                '                <br />    <br />   تاریخ چاپ گزارش: {{jdate('today')->format('%A, %d %B %y')}} <img src="{{asset('assets/media/logos/logo-compact.svg')}}" style="\n' +
                                '    float: left;\n' +
                                '    height: 70px;\n' +
                                '">\n' +
                                '\n' +
                                '                </div>\n' +
                                '            </div></div>');



                        }
                    },

                ],
                "footerCallback": function (row, data, start, end, display) {

                }

            });

        });


</script>
