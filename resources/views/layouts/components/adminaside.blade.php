<div id="kt_aside" class="aside bg-info" data-kt-drawer="true" data-kt-drawer-name="aside" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="{default:'200px', '300px': '250px'}" data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_aside_toggler">
    <!--begin::Primary-->
    <div class="aside-primary d-flex flex-column align-items-center flex-row-auto">
        <!--begin::Logo-->
        <div class="aside-logo d-flex flex-column align-items-center flex-column-auto py-4 pt-lg-10 pb-lg-7" id="kt_aside_logo">
            <a href="{{route('dashboard')}}">
                <img alt="Logo" src="https://lms.amanjacademy.com/wp-content/themes/wplms/logo.png" class="mh-50px" />
            </a>
        </div>
        <!--end::Logo-->
        <!--begin::Nav Wrapper-->
        <div class="aside-nav d-flex flex-column align-items-center flex-column-fluid pt-0 pb-5" id="kt_aside_nav">
            <!--begin::Nav scroll-->
            <div class="hover-scroll-y" data-kt-scroll="true" data-kt-scroll-height="auto" data-kt-scroll-dependencies="#kt_aside_logo, #kt_aside_footer" data-kt-scroll-wrappers="#kt_aside_nav" data-kt-scroll-offset="10px">
                <!--begin::Nav-->
                <ul class="nav flex-column">
                    <!--begin::Item-->
                    <li class="nav-item mb-1" title="Features">
                        <a href="#" class="nav-link h-40px w-40px h-lg-50px w-lg-50px btn btn-custom btn-icon btn-color-white active" data-bs-toggle="tab" data-bs-target="#kt_aside_tab_1" role="tab">
                            <!--begin::Svg Icon | path: icons/stockholm/Layout/Layout-4-blocks.svg-->
                            <span class="svg-icon svg-icon-2 svg-icon-lg-1">
												<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
													<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
														<rect x="0" y="0" width="24" height="24" />
														<rect fill="#000000" x="4" y="4" width="7" height="7" rx="1.5" />
														<path d="M5.5,13 L9.5,13 C10.3284271,13 11,13.6715729 11,14.5 L11,18.5 C11,19.3284271 10.3284271,20 9.5,20 L5.5,20 C4.67157288,20 4,19.3284271 4,18.5 L4,14.5 C4,13.6715729 4.67157288,13 5.5,13 Z M14.5,4 L18.5,4 C19.3284271,4 20,4.67157288 20,5.5 L20,9.5 C20,10.3284271 19.3284271,11 18.5,11 L14.5,11 C13.6715729,11 13,10.3284271 13,9.5 L13,5.5 C13,4.67157288 13.6715729,4 14.5,4 Z M14.5,13 L18.5,13 C19.3284271,13 20,13.6715729 20,14.5 L20,18.5 C20,19.3284271 19.3284271,20 18.5,20 L14.5,20 C13.6715729,20 13,19.3284271 13,18.5 L13,14.5 C13,13.6715729 13.6715729,13 14.5,13 Z" fill="#000000" opacity="0.3" />
													</g>
												</svg>
											</span>
                            <!--end::Svg Icon-->
                        </a>
                    </li>
                    <!--end::Item-->
                    <!--begin::Item-->
                    <li class="nav-item mb-1" title="Members">
                        <a href="#" class="nav-link h-40px w-40px h-lg-50px w-lg-50px btn btn-custom btn-icon btn-color-white" data-bs-toggle="tab" data-bs-target="#kt_aside_tab_2" role="tab">
                            <!--begin::Svg Icon | path: icons/stockholm/Communication/Group.svg-->
                            <span class="svg-icon svg-icon-2 svg-icon-lg-1">
												<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
													<path d="M18,14 C16.3431458,14 15,12.6568542 15,11 C15,9.34314575 16.3431458,8 18,8 C19.6568542,8 21,9.34314575 21,11 C21,12.6568542 19.6568542,14 18,14 Z M9,11 C6.790861,11 5,9.209139 5,7 C5,4.790861 6.790861,3 9,3 C11.209139,3 13,4.790861 13,7 C13,9.209139 11.209139,11 9,11 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" />
													<path d="M17.6011961,15.0006174 C21.0077043,15.0378534 23.7891749,16.7601418 23.9984937,20.4 C24.0069246,20.5466056 23.9984937,21 23.4559499,21 L19.6,21 C19.6,18.7490654 18.8562935,16.6718327 17.6011961,15.0006174 Z M0.00065168429,20.1992055 C0.388258525,15.4265159 4.26191235,13 8.98334134,13 C13.7712164,13 17.7048837,15.2931929 17.9979143,20.2 C18.0095879,20.3954741 17.9979143,21 17.2466999,21 C13.541124,21 8.03472472,21 0.727502227,21 C0.476712155,21 -0.0204617505,20.45918 0.00065168429,20.1992055 Z" fill="#000000" fill-rule="nonzero" />
												</svg>
											</span>
                            <!--end::Svg Icon-->
                        </a>
                    </li>
                    <!--end::Item-->
                    <!--begin::Item-->
                    <li class="nav-item mb-1" title="Latest Reports">
                        <a href="#" class="nav-link h-40px w-40px h-lg-50px w-lg-50px btn btn-custom btn-icon btn-color-white" data-bs-toggle="tab" data-bs-target="#kt_aside_tab_3" role="tab">
                            <!--begin::Svg Icon | path: icons/stockholm/Media/Equalizer.svg-->
                            <span class="svg-icon svg-icon-2 svg-icon-lg-1">
												<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
													<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
														<rect x="0" y="0" width="24" height="24" />
														<rect fill="#000000" opacity="0.3" x="13" y="4" width="3" height="16" rx="1.5" />
														<rect fill="#000000" x="8" y="9" width="3" height="11" rx="1.5" />
														<rect fill="#000000" x="18" y="11" width="3" height="9" rx="1.5" />
														<rect fill="#000000" x="3" y="13" width="3" height="7" rx="1.5" />
													</g>
												</svg>
											</span>
                            <!--end::Svg Icon-->
                        </a>
                    </li>
                    <!--end::Item-->
                    <!--begin::Item-->
                    <li class="nav-item mb-1" title="Project Management">
                        <a href="#" class="nav-link h-40px w-40px h-lg-50px w-lg-50px btn btn-custom btn-icon btn-color-white" data-bs-toggle="tab" data-bs-target="#kt_aside_tab_2" role="tab">
                            <!--begin::Svg Icon | path: icons/stockholm/General/Shield-check.svg-->
                            <span class="svg-icon svg-icon-2 svg-icon-lg-1">
												<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
													<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
														<rect x="0" y="0" width="24" height="24" />
														<path d="M4,4 L11.6314229,2.5691082 C11.8750185,2.52343403 12.1249815,2.52343403 12.3685771,2.5691082 L20,4 L20,13.2830094 C20,16.2173861 18.4883464,18.9447835 16,20.5 L12.5299989,22.6687507 C12.2057287,22.8714196 11.7942713,22.8714196 11.4700011,22.6687507 L8,20.5 C5.51165358,18.9447835 4,16.2173861 4,13.2830094 L4,4 Z" fill="#000000" opacity="0.3" />
														<path d="M11.1750002,14.75 C10.9354169,14.75 10.6958335,14.6541667 10.5041669,14.4625 L8.58750019,12.5458333 C8.20416686,12.1625 8.20416686,11.5875 8.58750019,11.2041667 C8.97083352,10.8208333 9.59375019,10.8208333 9.92916686,11.2041667 L11.1750002,12.45 L14.3375002,9.2875 C14.7208335,8.90416667 15.2958335,8.90416667 15.6791669,9.2875 C16.0625002,9.67083333 16.0625002,10.2458333 15.6791669,10.6291667 L11.8458335,14.4625 C11.6541669,14.6541667 11.4145835,14.75 11.1750002,14.75 Z" fill="#000000" />
													</g>
												</svg>
											</span>
                            <!--end::Svg Icon-->
                        </a>
                    </li>
                    <!--end::Item-->
                    <!--begin::Item-->
                    <li class="nav-item mb-1" title="User Management">
                        <a href="#" class="nav-link h-40px w-40px h-lg-50px w-lg-50px btn btn-custom btn-icon btn-color-white" data-bs-toggle="tab" data-bs-target="#kt_aside_tab_3" role="tab">
                            <!--begin::Svg Icon | path: icons/stockholm/Home/Library.svg-->
                            <span class="svg-icon svg-icon-2 svg-icon-lg-1">
												<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
													<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
														<rect x="0" y="0" width="24" height="24" />
														<path d="M5,3 L6,3 C6.55228475,3 7,3.44771525 7,4 L7,20 C7,20.5522847 6.55228475,21 6,21 L5,21 C4.44771525,21 4,20.5522847 4,20 L4,4 C4,3.44771525 4.44771525,3 5,3 Z M10,3 L11,3 C11.5522847,3 12,3.44771525 12,4 L12,20 C12,20.5522847 11.5522847,21 11,21 L10,21 C9.44771525,21 9,20.5522847 9,20 L9,4 C9,3.44771525 9.44771525,3 10,3 Z" fill="#000000" />
														<rect fill="#000000" opacity="0.3" transform="translate(17.825568, 11.945519) rotate(-19.000000) translate(-17.825568, -11.945519)" x="16.3255682" y="2.********" width="3" height="18" rx="1" />
													</g>
												</svg>
											</span>
                            <!--end::Svg Icon-->
                        </a>
                    </li>
                    <!--end::Item-->
                    <!--begin::Item-->
                    <li class="nav-item mb-1" title="Finance &amp; Accounting">
                        <a href="#" class="nav-link h-40px w-40px h-lg-50px w-lg-50px btn btn-custom btn-icon btn-color-white" data-bs-toggle="tab" data-bs-target="#kt_aside_tab_6" role="tab">
                            <!--begin::Svg Icon | path: icons/stockholm/Files/File-plus.svg-->
                            <span class="svg-icon svg-icon-2 svg-icon-lg-1">
												<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
													<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
														<polygon points="0 0 24 0 24 24 0 24" />
														<path d="M5.********,2 L13.7364114,2 C14.0910962,2 14.4343066,2.******** 14.7051108,2.******** L19.4686994,6.3839416 C19.8056532,6.******** 20,7.******** 20,7.******** L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.********,22 C4.********,22 4,21.8738751 4,20.0833333 L4,3.******** C4,2.******** 4.********,2 5.********,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" />
														<path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000" />
													</g>
												</svg>
											</span>
                            <!--end::Svg Icon-->
                        </a>
                    </li>
                    <!--end::Item-->
                </ul>
                <!--end::Nav-->
            </div>
            <!--end::Nav scroll-->
        </div>
        <!--end::Nav Wrapper-->
        <!--begin::Footer-->
        <div class="aside-footer d-flex flex-column align-items-center flex-column-auto py-5 py-lg-7" id="kt_aside_footer">
            <!--begin::Aside Toggle-->
            <button class="btn btn-sm btn-icon btn-white btn-active-primary position-absolute translate-middle start-0 end-100 bottom-0 shadow-sm d-none d-lg-flex" id="kt_aside_toggle" data-kt-toggle="true" data-kt-toggle-state="active" data-kt-toggle-target="body" data-kt-toggle-name="aside-minimize" title="Toggle Aside">
                <!--begin::Svg Icon | path: icons/stockholm/Navigation/Left-2.svg-->
                <span class="svg-icon svg-icon-2 icon-flip rotate-360">
									<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
										<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
											<polygon points="0 0 24 0 24 24 0 24" />
											<rect fill="#000000" opacity="0.3" transform="translate(15.000000, 12.000000) scale(-1, 1) rotate(-90.000000) translate(-15.000000, -12.000000)" x="14" y="7" width="2" height="10" rx="1" />
											<path d="M3.7071045,15.7071045 C3.3165802,16.0976288 2.68341522,16.0976288 2.29289093,15.7071045 C1.90236664,15.3165802 1.90236664,14.6834152 2.29289093,14.2928909 L8.29289093,8.29289093 C8.67146987,7.914312 9.28105631,7.90106637 9.67572234,8.26284357 L15.6757223,13.7628436 C16.0828413,14.136036 16.1103443,14.7686034 15.7371519,15.1757223 C15.3639594,15.5828413 14.7313921,15.6103443 14.3242731,15.2371519 L9.03007346,10.3841355 L3.7071045,15.7071045 Z" fill="#000000" fill-rule="nonzero" transform="translate(9.000001, 11.999997) scale(-1, -1) rotate(90.000000) translate(-9.000001, -11.999997)" />
										</g>
									</svg>
								</span>
                <!--end::Svg Icon-->
            </button>
            <!--end::Aside Toggle-->
            <!--begin::Menu-->
            <div class="mb-2" data-bs-toggle="tooltip" data-bs-placement="right" data-bs-trigger="hover" title="Quick settings">
                <button type="button" class="btn btn-custom h-40px w-40px h-lg-50px w-lg-50px btn-icon btn-color-white" data-kt-menu-trigger="click" data-kt-menu-overflow="true" data-kt-menu-placement="top-start" data-kt-menu-flip="top-end">
                    <!--begin::Svg Icon | path: icons/stockholm/Communication/Dial-numbers.svg-->
                    <span class="svg-icon svg-icon-2 svg-icon-lg-1">
										<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
											<rect fill="#000000" opacity="0.3" x="4" y="4" width="4" height="4" rx="2" />
											<rect fill="#000000" x="4" y="10" width="4" height="4" rx="2" />
											<rect fill="#000000" x="10" y="4" width="4" height="4" rx="2" />
											<rect fill="#000000" x="10" y="10" width="4" height="4" rx="2" />
											<rect fill="#000000" x="16" y="4" width="4" height="4" rx="2" />
											<rect fill="#000000" x="16" y="10" width="4" height="4" rx="2" />
											<rect fill="#000000" x="4" y="16" width="4" height="4" rx="2" />
											<rect fill="#000000" x="10" y="16" width="4" height="4" rx="2" />
											<rect fill="#000000" x="16" y="16" width="4" height="4" rx="2" />
										</svg>
									</span>
                    <!--end::Svg Icon-->
                </button>
                <!--begin::Menu-->
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold w-200px" data-kt-menu="true">
                    <div class="menu-item px-3">
                        <div class="menu-content fs-6 text-dark fw-bolder px-3 py-4">Manage</div>
                    </div>
                    <div class="separator mb-3 opacity-75"></div>
                    <div class="menu-item px-3">
                        <a href="#" class="menu-link px-3">Add User</a>
                    </div>
                    <div class="menu-item px-3">
                        <a href="#" class="menu-link px-3">Add Role</a>
                    </div>
                    <div class="menu-item px-3" data-kt-menu-trigger="hover" data-kt-menu-placement="right-start" data-kt-menu-flip="left-start, top">
                        <a href="#" class="menu-link px-3">
                            <span class="menu-title">Add Group</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <div class="menu-sub menu-sub-dropdown w-200px py-4">
                            <div class="menu-item px-3">
                                <a href="#" class="menu-link px-3">Admin Group</a>
                            </div>
                            <div class="menu-item px-3">
                                <a href="#" class="menu-link px-3">Staff Group</a>
                            </div>
                            <div class="menu-item px-3">
                                <a href="#" class="menu-link px-3">Member Group</a>
                            </div>
                        </div>
                    </div>
                    <div class="menu-item px-3">
                        <a href="#" class="menu-link px-3">Reports</a>
                    </div>
                    <div class="separator mt-3 opacity-75"></div>
                    <div class="menu-item px-3">
                        <div class="menu-content px-3 py-3">
                            <a class="btn btn-primary fw-bold btn-sm px-4" href="#">Create New</a>
                        </div>
                    </div>
                </div>
                <!--end::Menu-->
            </div>
            <!--end::Menu-->
        </div>
        <!--end::Footer-->
    </div>
    <!--end::Primary-->
    <!--begin::Secondary-->
    <div class="aside-secondary d-flex flex-row-fluid bg-white">
        <!--begin::Workspace-->
        <div class="aside-workspace my-7 ps-5 pe-4 ps-lg-10 pe-lg-6" id="kt_aside_wordspace">
            <!--begin::Tab Content-->
            <div class="tab-content">
                <!--begin::Main menu-->
                <div class="tab-pane fade show active" id="kt_aside_tab_1">
                    <!--begin::Aside Menu-->
                    <!--begin::Menu-->
                    <div class="menu menu-column menu-rounded menu-title-gray-700 menu-state-title-primary menu-state-icon-primary menu-state-bullet-primary menu-arrow-gray-500 fw-bold fs-6" data-kt-menu="true">
                        <div class="hover-scroll-y pe-4 pe-lg-5" id="kt_aside_menu_scroll" data-kt-scroll="true" data-kt-scroll-height="auto" data-kt-scroll-wrappers="#kt_aside_wordspace" data-kt-scroll-offset="10px">
                            <div class="menu-wrapper menu-column menu-fit">
                                <div class="menu-item here show">
                                    <h4 class="menu-content text-muted mb-0 fs-6 fw-bold text-uppercase">کاربران</h4>
                                    <div class="menu-sub menu-fit menu-sub-accordion show pb-10">
                                        <div class="menu-item">
                                            <a class="menu-link active py-2" href="{{route('users.index')}}">
                                                <span class="menu-title">مدیریت کاربران</span>
                                            </a>
                                        </div>


                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="{{route('ticket.index.admin')}}">
                                                <span class="menu-title">مدیریت تیکت ها</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="{{route('ticket.teams')}}">
                                                <span class="menu-title">مدیریت تیم های پشتیتبانی</span>
                                            </a>
                                        </div>

                                    </div>
                                </div>

                                <div class="menu-item">
                                    <h4 class="menu-content text-muted mb-0 fs-6 fw-bold text-uppercase">Apps</h4>
                                    <div class="menu-sub menu-fit menu-sub-accordion show pb-10">
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../apps/chat.html">
                                                <span class="menu-title">Chat</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../apps/inbox.html">
                                                <span class="menu-title">Inbox</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../apps/shop/shop-1.html">
                                                <span class="menu-title">Shop 1</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../apps/shop/shop-2.html">
                                                <span class="menu-title">Shop 2</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../apps/shop/product.html">
                                                <span class="menu-title">Shop Product</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="menu-item">
                                    <h4 class="menu-content text-muted mb-0 fs-6 fw-bold text-uppercase">General</h4>
                                    <div class="menu-sub menu-fit menu-sub-accordion show pb-10">
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../general/faq.html">
                                                <span class="menu-title">FAQ</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../general/pricing.html">
                                                <span class="menu-title">Pricing</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../general/invoice.html">
                                                <span class="menu-title">Invoice</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../general/login.html">
                                                <span class="menu-title">Login</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../general/wizard.html">
                                                <span class="menu-title">Wizard</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../general/error.html">
                                                <span class="menu-title">Error</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="menu-item">
                                    <h4 class="menu-content text-muted mb-0 fs-6 fw-bold text-uppercase">Profile</h4>
                                    <div class="menu-sub menu-fit menu-sub-accordion show pb-10">
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../profile/overview.html">
                                                <span class="menu-title">Overview</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../profile/account.html">
                                                <span class="menu-title">Account</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../profile/settings.html">
                                                <span class="menu-title">Settings</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="menu-item">
                                    <h4 class="menu-content text-muted mb-0 fs-6 fw-bold text-uppercase">Resources</h4>
                                    <div class="menu-sub menu-fit menu-sub-accordion show">
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../documentation/base/utilities.html">
                                                <span class="menu-title">Components</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../documentation/getting-started.html">
                                                <span class="menu-title">Documentation</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../layout-builder.html">
                                                <span class="menu-title">Layout Builder</span>
                                            </a>
                                        </div>
                                        <div class="menu-item">
                                            <a class="menu-link py-2" href="../documentation/getting-started/changelog.html">
                                                <span class="menu-title">Changelog</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end::Menu-->
                </div>
                <!--end::Main menu-->
                <!--begin::Demo menu-->
                <div class="tab-pane fade" id="kt_aside_tab_2">Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.</div>
                <!--end::Demo menu-->
                <!--begin::Demo menu-->
                <div class="tab-pane fade" id="kt_aside_tab_3">Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.</div>
                <!--end::Demo menu-->
            </div>
            <!--end::Tab Content-->
        </div>
        <!--end::Workspace-->
    </div>
    <!--end::Secondary-->
</div>
