<div class="modal bg-white fade" id="kt_header_search_modal" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content shadow-none">
            <div class="container w-lg-800px">
                <div class="modal-header d-flex justify-content-end border-0">
                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-light-primary ms-2" data-bs-dismiss="modal">
                        <!--begin::Svg Icon | path: icons/stockholm/Navigation/Close.svg-->
                        <span class="svg-icon svg-icon-1">
									<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
										<g transform="translate(12.000000, 12.000000) rotate(-45.000000) translate(-12.000000, -12.000000) translate(4.000000, 4.000000)" fill="#000000">
											<rect fill="#000000" x="0" y="7" width="16" height="2" rx="1" />
											<rect fill="#000000" opacity="0.5" transform="translate(8.000000, 8.000000) rotate(-270.000000) translate(-8.000000, -8.000000)" x="0" y="7" width="16" height="2" rx="1" />
										</g>
									</svg>
								</span>
                        <!--end::Svg Icon-->
                    </div>
                    <!--end::Close-->
                </div>
                <div class="modal-body">
                    <!--begin::Search-->
                    <form class="pb-10">
                        <input autofocus="" type="text" class="form-control bg-transparent border-0 fs-4x text-center fw-normal" name="query" placeholder="Search..." />
                    </form>
                    <!--end::Search-->
                    <!--begin::Shop Goods-->
                    <div class="py-10">
                        <h3 class="fw-bolder mb-8">Shop Goods</h3>
                        <!--begin::Row-->
                        <div class="row g-5">
                            <div class="col-sm-6">
                                <div class="row g-5">
                                    <div class="col-sm-6">
                                        <div class="card overlay min-h-125px mb-5 shadow-none">
                                            <div class="card-body d-flex flex-column p-0">
                                                <div class="overlay-wrapper flex-grow-1 bgi-no-repeat bgi-size-cover bgi-position-center card-rounded" style="background-image:url('{{ asset('assets/media/stock/600x400/img-17.jpg') }}')"></div>
                                                <div class="overlay-layer bg-white bg-opacity-50">
                                                    <a href="#" class="btn btn-sm fw-bold btn-primary">Explore</a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card overlay min-h-125px mb-5 shadow-none">
                                            <div class="card-body d-flex flex-column p-0">
                                                <div class="overlay-wrapper flex-grow-1 bgi-no-repeat bgi-size-cover bgi-position-center card-rounded" style="background-image:url('{{ asset('assets/media/stock/600x400/img-1.jpg') }}')"></div>
                                                <div class="overlay-layer bg-white bg-opacity-50">
                                                    <a href="#" class="btn btn-sm fw-bold btn-primary">Explore</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="card card-stretch card-stretch-rtl overlay mb-5 shadow-none min-h-250px">
                                            <div class="card-body d-flex flex-column p-0">
                                                <div class="overlay-wrapper flex-grow-1 bgi-no-repeat bgi-size-cover bgi-position-center card-rounded" style="background-image:url('{{ asset('assets/media/stock/600x400/img-23.jpg') }}')"></div>
                                                <div class="overlay-layer bg-white bg-opacity-50">
                                                    <a href="#" class="btn btn-sm fw-bold btn-primary">Explore</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="card card-stretch card-stretch-rtl overlay mb-5 shadow-none min-h-250px">
                                    <div class="card-body d-flex flex-column p-0">
                                        <div class="overlay-wrapper flex-grow-1 bgi-no-repeat bgi-size-cover bgi-position-center card-rounded" style="background-image:url('{{ asset('assets/media/stock/600x400/img-11.jpg') }}')"></div>
                                        <div class="overlay-layer bg-white bg-opacity-50">
                                            <a href="#" class="btn btn-sm fw-bold btn-primary">Explore</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--end::Row-->
                    </div>
                    <!--end::Shop Goods-->
                    <!--begin::Framework Users-->
                    <div>
                        <h3 class="text-dark fw-bolder fs-1 mb-6">Framework Users</h3>
                        <!--begin::List Widget 4-->
                        <div class="card bg-transparent mb-5 shadow-none">
                            <!--begin::Body-->
                            <div class="card-body pt-2 px-0">
                                <div class="table-responsive">
                                    <table class="table table-borderless align-middle">
                                        <!--begin::Item-->
                                        <tr>
                                            <th class="ps-0 w-55px">
                                                <!--begin::Symbol-->
                                                <div class="symbol symbol-55px flex-shrink-0 me-4">
															<span class="symbol-label bg-light-primary">
																<img src="{{ asset('assets/media/svg/avatars/009-boy-4.svg') }}" class="h-75 align-self-end" alt="" />
															</span>
                                                </div>
                                                <!--end::Symbol-->
                                            </th>
                                            <td class="ps-0 flex-column min-w-300px">
                                                <!--begin::Title-->
                                                <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6 mb-1">Brad Simmons</a>
                                                <div class="text-muted fw-bold">Uses: HTML/CSS/JS, Python</div>
                                                <!--end::Title-->
                                            </td>
                                            <td>
                                                <!--begin::Label-->
                                                <div class="me-4 me-md-19 text-md-right">
                                                    <div class="text-gray-800 fw-bolder fs-6 mb-1">$2,000,000</div>
                                                    <span class="text-muted fw-bold">Paid</span>
                                                </div>
                                                <!--end::Label-->
                                            </td>
                                            <td class="text-end pe-0">
                                                <!--begin::Btn-->
                                                <a href="#" class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm">
                                                    <!--begin::Svg Icon | path: icons/stockholm/Navigation/Arrow-right.svg-->
                                                    <span class="svg-icon svg-icon-4">
																<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
																	<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
																		<polygon points="0 0 24 0 24 24 0 24" />
																		<rect fill="#000000" opacity="0.5" transform="translate(12.000000, 12.000000) rotate(-90.000000) translate(-12.000000, -12.000000)" x="11" y="5" width="2" height="14" rx="1" />
																		<path d="M9.70710318,15.7071045 C9.31657888,16.0976288 8.68341391,16.0976288 8.29288961,15.7071045 C7.90236532,15.3165802 7.90236532,14.6834152 8.29288961,14.2928909 L14.2928896,8.29289093 C14.6714686,7.914312 15.281055,7.90106637 15.675721,8.26284357 L21.675721,13.7628436 C22.08284,14.136036 22.1103429,14.7686034 21.7371505,15.1757223 C21.3639581,15.5828413 20.7313908,15.6103443 20.3242718,15.2371519 L15.0300721,10.3841355 L9.70710318,15.7071045 Z" fill="#000000" fill-rule="nonzero" transform="translate(14.999999, 11.999997) scale(1, -1) rotate(90.000000) translate(-14.999999, -11.999997)" />
																	</g>
																</svg>
															</span>
                                                    <!--end::Svg Icon-->
                                                </a>
                                                <!--end::Btn-->
                                            </td>
                                        </tr>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <tr>
                                            <th class="ps-0">
                                                <!--begin::Symbol-->
                                                <div class="symbol symbol-55px flex-shrink-0 me-4">
															<span class="symbol-label bg-light-danger">
																<img src="{{ asset('assets/media/svg/avatars/006-girl-3.svg') }}" class="h-75 align-self-end" alt="" />
															</span>
                                                </div>
                                                <!--end::Symbol-->
                                            </th>
                                            <td class="ps-0 flex-column min-w-300px">
                                                <!--begin::Title-->
                                                <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6 mb-1">Jessie Clarcson</a>
                                                <div class="text-muted fw-bold">Uses: HTML, ReactJS, ASP.NET</div>
                                                <!--end::Title-->
                                            </td>
                                            <td>
                                                <!--end::Label-->
                                                <div class="me-4 me-md-19 text-md-right">
                                                    <div class="text-gray-800 fw-bolder fs-6 mb-1">$1,200,000</div>
                                                    <span class="text-muted fw-bold">Paid</span>
                                                </div>
                                                <!--end::Label-->
                                            </td>
                                            <td class="text-end pe-0">
                                                <!--begin::Btn-->
                                                <a href="#" class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm">
                                                    <!--begin::Svg Icon | path: icons/stockholm/Navigation/Arrow-right.svg-->
                                                    <span class="svg-icon svg-icon-4">
																<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
																	<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
																		<polygon points="0 0 24 0 24 24 0 24" />
																		<rect fill="#000000" opacity="0.5" transform="translate(12.000000, 12.000000) rotate(-90.000000) translate(-12.000000, -12.000000)" x="11" y="5" width="2" height="14" rx="1" />
																		<path d="M9.70710318,15.7071045 C9.31657888,16.0976288 8.68341391,16.0976288 8.29288961,15.7071045 C7.90236532,15.3165802 7.90236532,14.6834152 8.29288961,14.2928909 L14.2928896,8.29289093 C14.6714686,7.914312 15.281055,7.90106637 15.675721,8.26284357 L21.675721,13.7628436 C22.08284,14.136036 22.1103429,14.7686034 21.7371505,15.1757223 C21.3639581,15.5828413 20.7313908,15.6103443 20.3242718,15.2371519 L15.0300721,10.3841355 L9.70710318,15.7071045 Z" fill="#000000" fill-rule="nonzero" transform="translate(14.999999, 11.999997) scale(1, -1) rotate(90.000000) translate(-14.999999, -11.999997)" />
																	</g>
																</svg>
															</span>
                                                    <!--end::Svg Icon-->
                                                </a>
                                                <!--end::Btn-->
                                            </td>
                                        </tr>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <tr>
                                            <th class="ps-0">
                                                <!--begin::Symbol-->
                                                <div class="symbol symbol-55px flex-shrink-0 me-4">
															<span class="symbol-label bg-light-success">
																<img src="{{ asset('assets/media/svg/avatars/011-boy-5.svg') }}" class="h-75 align-self-end" alt="" />
															</span>
                                                </div>
                                                <!--end::Symbol-->
                                            </th>
                                            <td class="ps-0 flex-column min-w-300px">
                                                <!--begin::Title-->
                                                <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6 mb-1">Lebron Wayde</a>
                                                <div class="text-muted fw-bold">Uses: HTML. Laravel Framework</div>
                                                <!--end::Title-->
                                            </td>
                                            <td>
                                                <!--end::Label-->
                                                <div class="me-4 me-md-19 text-md-right">
                                                    <div class="text-gray-800 fw-bolder fs-6 mb-1">$3,400,000</div>
                                                    <span class="text-muted fw-bold">Paid</span>
                                                </div>
                                                <!--end::Label-->
                                            </td>
                                            <td class="text-end pe-0">
                                                <!--begin::Btn-->
                                                <a href="#" class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm">
                                                    <!--begin::Svg Icon | path: icons/stockholm/Navigation/Arrow-right.svg-->
                                                    <span class="svg-icon svg-icon-4">
																<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
																	<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
																		<polygon points="0 0 24 0 24 24 0 24" />
																		<rect fill="#000000" opacity="0.5" transform="translate(12.000000, 12.000000) rotate(-90.000000) translate(-12.000000, -12.000000)" x="11" y="5" width="2" height="14" rx="1" />
																		<path d="M9.70710318,15.7071045 C9.31657888,16.0976288 8.68341391,16.0976288 8.29288961,15.7071045 C7.90236532,15.3165802 7.90236532,14.6834152 8.29288961,14.2928909 L14.2928896,8.29289093 C14.6714686,7.914312 15.281055,7.90106637 15.675721,8.26284357 L21.675721,13.7628436 C22.08284,14.136036 22.1103429,14.7686034 21.7371505,15.1757223 C21.3639581,15.5828413 20.7313908,15.6103443 20.3242718,15.2371519 L15.0300721,10.3841355 L9.70710318,15.7071045 Z" fill="#000000" fill-rule="nonzero" transform="translate(14.999999, 11.999997) scale(1, -1) rotate(90.000000) translate(-14.999999, -11.999997)" />
																	</g>
																</svg>
															</span>
                                                    <!--end::Svg Icon-->
                                                </a>
                                                <!--end::Btn-->
                                            </td>
                                        </tr>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <tr>
                                            <th class="ps-0">
                                                <!--begin::Symbol-->
                                                <div class="symbol symbol-55px flex-shrink-0 me-4">
															<span class="symbol-label bg-light-warning">
																<img src="{{ asset('assets/media/svg/avatars/015-boy-6.svg') }}" class="h-75 align-self-end" alt="" />
															</span>
                                                </div>
                                                <!--end::Symbol-->
                                            </th>
                                            <td class="ps-0 flex-column min-w-300px">
                                                <!--begin::Title-->
                                                <a href="#" class="text-gray-800 fw-bolder text-hover-primary fs-6 mb-1">Kevin Leonard</a>
                                                <div class="text-muted fw-bold">Uses: VueJS, Laravel Framework</div>
                                                <!--end::Title-->
                                            </td>
                                            <td>
                                                <!--end::Label-->
                                                <div class="me-4 me-md-19 text-md-right">
                                                    <div class="text-gray-800 fw-bolder fs-6 mb-1">$35,600,000</div>
                                                    <span class="text-muted fw-bold">Paid</span>
                                                </div>
                                                <!--end::Label-->
                                            </td>
                                            <td class="text-end pe-0">
                                                <!--begin::Btn-->
                                                <a href="#" class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm">
                                                    <!--begin::Svg Icon | path: icons/stockholm/Navigation/Arrow-right.svg-->
                                                    <span class="svg-icon svg-icon-4">
																<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
																	<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
																		<polygon points="0 0 24 0 24 24 0 24" />
																		<rect fill="#000000" opacity="0.5" transform="translate(12.000000, 12.000000) rotate(-90.000000) translate(-12.000000, -12.000000)" x="11" y="5" width="2" height="14" rx="1" />
																		<path d="M9.70710318,15.7071045 C9.31657888,16.0976288 8.68341391,16.0976288 8.29288961,15.7071045 C7.90236532,15.3165802 7.90236532,14.6834152 8.29288961,14.2928909 L14.2928896,8.29289093 C14.6714686,7.914312 15.281055,7.90106637 15.675721,8.26284357 L21.675721,13.7628436 C22.08284,14.136036 22.1103429,14.7686034 21.7371505,15.1757223 C21.3639581,15.5828413 20.7313908,15.6103443 20.3242718,15.2371519 L15.0300721,10.3841355 L9.70710318,15.7071045 Z" fill="#000000" fill-rule="nonzero" transform="translate(14.999999, 11.999997) scale(1, -1) rotate(90.000000) translate(-14.999999, -11.999997)" />
																	</g>
																</svg>
															</span>
                                                    <!--end::Svg Icon-->
                                                </a>
                                                <!--end::Btn-->
                                            </td>
                                        </tr>
                                        <!--end::Item-->
                                    </table>
                                </div>
                            </div>
                            <!--end::Body-->
                        </div>
                        <!--end::List Widget 4-->
                    </div>
                    <!--end::Framework Users-->
                    <!--begin::Tutorials-->
                    <div class="pb-10">
                        <h3 class="text-dark fw-bolder fs-1 mb-6">Tutorials</h3>
                        <!--begin::List Widget 5-->
                        <div class="card mb-5 shadow-none">
                            <!--begin::Body-->
                            <div class="card-body pt-2 px-0">
                                <!--begin::Item-->
                                <div class="d-flex mb-6">
                                    <!--begin::Icon-->
                                    <div class="me-1">
                                        <!--begin::Svg Icon | path: icons/stockholm/Navigation/Angle-right.svg-->
                                        <span class="svg-icon svg-icon-sm svg-icon-primary">
													<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
														<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
															<polygon points="0 0 24 0 24 24 0 24" />
															<path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="#000000" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-270.000000) translate(-12.000003, -11.999999)" />
														</g>
													</svg>
												</span>
                                        <!--end::Svg Icon-->
                                    </div>
                                    <!--end::Icon-->
                                    <!--begin::Content-->
                                    <div class="d-flex flex-column">
                                        <a href="#" class="fs-6 fw-bolder text-hover-primary text-gray-800 mb-2">How to Create Your First Project with Start Admin Theme</a>
                                        <div class="fw-bold text-muted">But nothing can prepare you for the real thing</div>
                                    </div>
                                    <!--end::Content-->
                                </div>
                                <!--end::Item-->
                                <!--begin::Item-->
                                <div class="d-flex mb-6">
                                    <!--begin::Icon-->
                                    <div class="me-1">
                                        <!--begin::Svg Icon | path: icons/stockholm/Navigation/Angle-right.svg-->
                                        <span class="svg-icon svg-icon-sm svg-icon-primary">
													<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
														<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
															<polygon points="0 0 24 0 24 24 0 24" />
															<path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="#000000" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-270.000000) translate(-12.000003, -11.999999)" />
														</g>
													</svg>
												</span>
                                        <!--end::Svg Icon-->
                                    </div>
                                    <!--end::Icon-->
                                    <!--begin::Content-->
                                    <div class="d-flex flex-column">
                                        <a href="#" class="fs-6 fw-bolder text-hover-primary text-gray-800 mb-2">Start Admin Theme Getting Started &amp; Installation</a>
                                        <div class="fw-bold text-muted">Long before you sit down to put digital pen</div>
                                    </div>
                                    <!--end::Content-->
                                </div>
                                <!--end::Item-->
                                <!--begin::Item-->
                                <div class="d-flex mb-6">
                                    <!--begin::Icon-->
                                    <div class="me-1">
                                        <!--begin::Svg Icon | path: icons/stockholm/Navigation/Angle-right.svg-->
                                        <span class="svg-icon svg-icon-sm svg-icon-primary">
													<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
														<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
															<polygon points="0 0 24 0 24 24 0 24" />
															<path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="#000000" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-270.000000) translate(-12.000003, -11.999999)" />
														</g>
													</svg>
												</span>
                                        <!--end::Svg Icon-->
                                    </div>
                                    <!--end::Icon-->
                                    <!--begin::Content-->
                                    <div class="d-flex flex-column">
                                        <a href="#" class="fs-6 fw-bolder text-hover-primary text-gray-800 mb-2">Craft a headline that is both informative and will capture</a>
                                        <div class="fw-bold text-muted">But nothing can prepare you for the real thing</div>
                                    </div>
                                    <!--end::Content-->
                                </div>
                                <!--end::Item-->
                                <!--begin::Item-->
                                <div class="d-flex mb-6">
                                    <!--begin::Icon-->
                                    <div class="me-1">
                                        <!--begin::Svg Icon | path: icons/stockholm/Navigation/Angle-right.svg-->
                                        <span class="svg-icon svg-icon-sm svg-icon-primary">
													<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
														<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
															<polygon points="0 0 24 0 24 24 0 24" />
															<path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="#000000" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-270.000000) translate(-12.000003, -11.999999)" />
														</g>
													</svg>
												</span>
                                        <!--end::Svg Icon-->
                                    </div>
                                    <!--end::Icon-->
                                    <!--begin::Content-->
                                    <div class="d-flex flex-column">
                                        <a href="#" class="fs-6 fw-bolder text-hover-primary text-gray-800 mb-2">Write your post, either writing a draft in a single</a>
                                        <div class="fw-bold text-muted">Long before you sit down to put pen</div>
                                    </div>
                                    <!--end::Content-->
                                </div>
                                <!--end::Item-->
                                <!--begin::Item-->
                                <div class="d-flex mb-6">
                                    <!--begin::Icon-->
                                    <div class="me-1">
                                        <!--begin::Svg Icon | path: icons/stockholm/Navigation/Angle-right.svg-->
                                        <span class="svg-icon svg-icon-sm svg-icon-primary">
													<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
														<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
															<polygon points="0 0 24 0 24 24 0 24" />
															<path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="#000000" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-270.000000) translate(-12.000003, -11.999999)" />
														</g>
													</svg>
												</span>
                                        <!--end::Svg Icon-->
                                    </div>
                                    <!--end::Icon-->
                                    <!--begin::Content-->
                                    <div class="d-flex flex-column">
                                        <a href="#" class="fs-6 fw-bolder text-hover-primary text-gray-800 mb-2">Use images to enhance your post, improve its flow</a>
                                        <div class="fw-bold text-muted">Long before you sit down to put digital pen</div>
                                    </div>
                                    <!--end::Content-->
                                </div>
                                <!--end::Item-->
                            </div>
                            <!--end::Body-->
                        </div>
                        <!--end::List Widget 5-->
                    </div>
                    <!--end::Tutorials-->
                </div>
            </div>
        </div>
    </div>
</div>
