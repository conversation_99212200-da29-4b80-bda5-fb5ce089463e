@extends('layouts.base')

@section('title')
    لیست انتظار - {{ $feature }}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row justify-content-center min-vh-100 align-items-center">
            <div class="col-lg-6 col-md-8 direction-rtl">
                <div class="card shadow-lg border-0">
                    <div class="card-body p-8">
                        <!-- Header Section -->
                        <div class="text-center mb-8">
                            <div class="mb-4">
                                <i class="ki-duotone ki-rocket fs-3x text-primary">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                            </div>
                            <h1 class="fw-bolder text-gray-900 mb-3">ویژگی جدید در راه است!</h1>
                            <p class="text-gray-600 fs-5">یادداشت برداری هوشمند با قدرت هوش مصنوعی</p>
                        </div>

                        <!-- Already Registered Alert -->


                        <!-- Feature Description -->
                        <div class="bg-light-primary rounded p-6 mb-8" dir="rtl">
                            <div class="d-flex align-items-center mb-4" dir="rtl">

                            </div>

                            <!--ARCADE EMBED START--><div style="position: relative; padding-bottom: calc(57.666666666666664% + 41px); height: 0; width: 100%;"><iframe src="https://demo.arcade.software/0j6kSezPeH9Bxv9vxEWa?embed&embed_mobile=tab&embed_desktop=inline&show_copy_link=true" title="دریافت یادداشت‌های هوشمند برای جلسات" frameborder="0" loading="lazy" webkitallowfullscreen mozallowfullscreen allowfullscreen allow="clipboard-write" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; color-scheme: light;" ></iframe></div><!--ARCADE EMBED END-->
                            <p class="mt-4 mb-0">
                                در حال حاضر با برخی از کاربران محدود در حال تست و بررسی این ویژگی هستیم. اما شما هم
                                میتوانید درخواست فعال سازی را ثبت کنید
                                تا در صورت امکان این ویژگی زودتر از سایرین برای شما فعال شود
                            </p>
                        </div>

                        <!-- Alert Messages -->
                        @if(session('success'))
                            <div class="alert alert-success d-flex align-items-center mb-6">
                                <i class="ki-duotone ki-check-circle fs-2 text-success me-3">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                <div class="fw-semibold">{{ session('success') }}</div>
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="alert alert-danger d-flex align-items-center mb-6">
                                <i class="ki-duotone ki-cross-circle fs-2 text-danger me-3">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                <div class="fw-semibold">{{ session('error') }}</div>
                            </div>
                        @endif

                        @if(session('info'))
                            <div class="alert alert-info d-flex align-items-center mb-6">
                                <i class="ki-duotone ki-information-5 fs-2 text-info me-3">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                </i>
                                <div class="fw-semibold">{{ session('info') }}</div>
                            </div>
                        @endif

                        <!-- Waitlist Form -->
                        @if(!$existingEntry)
                            <form method="POST" action="{{ route('feature.waitlist.store') }}" class="form">
                                @csrf
                                <input type="hidden" name="feature" value="{{ $feature }}">

                                <div class="mb-6">
                                    <label class="form-label fw-semibold text-gray-900 fs-6 mb-2">شماره تماس</label>
                                    <div class="input-group direction-ltr">
                                    <span class="input-group-text">
                                        <span class="svg-icon svg-icon-muted svg-icon-2hx">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                 xmlns="http://www.w3.org/2000/svg">
                                                <path opacity="0.3"
                                                      d="M12 22C13.6569 22 15 20.6569 15 19C15 17.3431 13.6569 16 12 16C10.3431 16 9 17.3431 9 19C9 20.6569 10.3431 22 12 22Z"
                                                      fill="currentColor"/>
                                                <path
                                                    d="M19 15V18C19 18.6 18.6 19 18 19H6C5.4 19 5 18.6 5 18V15C6.1 15 7 14.1 7 13V10C7 7.6 8.7 5.6 11 5.1V3C11 2.4 11.4 2 12 2C12.6 2 13 2.4 13 3V5.1C15.3 5.6 17 7.6 17 10V13C17 14.1 17.9 15 19 15ZM11 10C11 9.4 11.4 9 12 9C12.6 9 13 8.6 13 8C13 7.4 12.6 7 12 7C10.3 7 9 8.3 9 10C9 10.6 9.4 11 10 11C10.6 11 11 10.6 11 10Z"
                                                    fill="currentColor"/>
                                            </svg>
                                        </span>
                                    </span>
                                        <input type="text" name="phone_number"
                                               class="form-control form-control-lg @error('phone_number') is-invalid @enderror"
                                               value="{{ auth()->user()->phone_number ?? old('phone_number') }}"
                                               placeholder="09xxxxxxxxx" required>
                                    </div>
                                    @error('phone_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-8">
                                    <label class="form-label fw-semibold text-gray-900 fs-6 mb-2">ایمیل
                                        (اختیاری)</label>
                                    <div class="input-group direction-ltr">
                                    <span class="input-group-text">
                                        <span class="svg-icon svg-icon-muted svg-icon-2hx">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                 xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M6 8.725C6 8.125 6.4 7.725 7 7.725H14L18 11.725V12.925L22 9.725L12.6 2.225C12.2 1.925 11.7 1.925 11.4 2.225L2 9.725L6 12.925V8.725Z"
                                                    fill="currentColor"/>
                                                <path opacity="0.3"
                                                      d="M22 9.72498V20.725C22 21.325 21.6 21.725 21 21.725H3C2.4 21.725 2 21.325 2 20.725V9.72498L11.4 17.225C11.8 17.525 12.3 17.525 12.6 17.225L22 9.72498ZM15 11.725H18L14 7.72498V10.725C14 11.325 14.4 11.725 15 11.725Z"
                                                      fill="currentColor"/>
                                            </svg>
                                        </span>
                                    </span>
                                        <input type="email" class="form-control form-control-lg"
                                               value="{{ auth()->user()->email }}" readonly>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary btn-lg w-100 mb-4">
                                    <i class="ki-duotone ki-rocket fs-2 me-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                    عضویت در لیست انتظار
                                </button>
                            </form>
                        @else
                            <!-- Already Registered Status -->
                            <div class="bg-light-success rounded p-6 text-center mb-4">
                                <i class="ki-duotone ki-check-circle fs-3x text-success mb-3">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                <h4 class="fw-bold text-gray-900 mb-2">شما در لیست انتظار هستید</h4>
                                <p class="text-gray-700 mb-3">
                                    شماره تماس: {{ $existingEntry->phone_number }}<br>
                                    تاریخ
                                    ثبت: {{ \Morilog\Jalali\Jalalian::fromDateTime($existingEntry->created_at)->format('Y/m/d H:i') }}
                                </p>
                                <div class="badge badge-light-success fs-7 fw-bold">
                                    در انتظار فعال‌سازی
                                </div>
                            </div>
                        @endif

                        <!-- Info Section -->
                        <div class="bg-light-info rounded p-4 text-center">
                            <i class="ki-duotone ki-information-5 fs-2x text-info mb-2">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                            </i>
                            <p class="text-gray-700 mb-0 fs-6">
                                پس از آماده شدن ویژگی، از طریق پیامک و ایمیل به شما اطلاع خواهیم داد
                            </p>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <style>
        .invalid-feedback {
            display: inline-block !important;
        }
    </style>
@endsection

@push('styles')
    <style>
        .invalid-feedback {
            display: inline-block !important;
        }

        .min-vh-100 {
            min-height: 100vh;
        }

        .invalid-feedback {
            display: inline !important;
        }
    </style>
@endpush
