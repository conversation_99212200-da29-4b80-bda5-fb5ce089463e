@extends('layouts.base')

@section('title')
    نتیجه پرداخت
@endsection

@section('header_style')
    <link href="{{ asset('assets/plugins/global/persian-datepicker.min.css') }}" rel="stylesheet" type="text/css"/>
@endsection

@section('content')

    <div class=" container-xxl ">
        <!--begin::Main-->
        <div class="d-flex flex-column flex-column-fluid">
            <!--begin::toolbar-->
            <div class="toolbar direction-rtl" id="kt_toolbar">
                <div class="d-flex flex-stack flex-wrap flex-sm-nowrap">
                    <!--begin::Info-->
                    <div class="d-flex flex-column align-items-start justify-content-center flex-wrap me-1">
                        <!--begin::Title-->
                        <h3 class="text-dark fw-bolder my-1">نتیجه پرداخت</h3>
                        <!--end::Title-->
                        <!--begin::Breadcrumb-->
                        <ul class="breadcrumb breadcrumb-line bg-transparent text-muted fw-bold p-0 my-1 fs-7">
                            <li class="breadcrumb-item">
                                <a href="{{route('dashboard')}}" class="text-muted text-hover-primary">پنل کاربری</a>
                            </li>
                            <li class="breadcrumb-item">پرداخت</li>
                            <li class="breadcrumb-item text-dark">نتیجه پرداخت</li>
                        </ul>
                        <!--end::Breadcrumb-->
                    </div>
                    <!--end::Info-->
                </div>
            </div>
            <!--end::toolbar-->
            <!--begin::Content-->
            <div class="content fs-6 d-flex flex-column-fluid direction-rtl" id="kt_content">
                <!--begin::Container-->
                <div class="col-md-12">
                    <!--begin::Card-->
                    <div class="card">
                        <!--begin::Card Body-->

                        <div class="card-body d-flex bg-white p-12 flex-column flex-md-row flex-lg-column flex-xxl-row">

                            <!--begin::Card-->
                            <div class="card shadow-none w-auto ml-auto">
                                <!--begin::Card Body-->
                                <div class="card-body bg-light px-12 py-10">
                                    @if($success)

                                        <h3 class="fw-bolder fs-1 mb-1">
                                            پرداخت شما با موفقیت انجام شد
                                        </h3>
                                        <br/>
                                        <div class="fs-7 mb-8">
                                            صورت حساب شما به شرح زیر است:
                                        </div>

                                        <!--begin::Info-->
                                        <table class="table table-borderless align-middle fw-bold">
                                            <tbody>
                                            <tr>
                                                <td class="text-gray-600 ps-0 text-align-right">اعتبار افزوده شده</td>
                                                <td class="text-dark pe-0">{{number_format($amount,0)}}</td>
                                            </tr>
                                            <tr>
                                                <td class="text-gray-600 ps-0 text-align-right">
                                                    جمع پرداختی شما (+ مالیات بر ارزش افزوده)
                                                </td>
                                                <td class="text-dark pe-0">{{number_format($amountWithTxa,0)}}</td>
                                            </tr>
                                            <tr>
                                                <td class="text-gray-600 ps-0 text-align-right">شناسه فاکتور</td>
                                                <td class="text-dark pe-0">{{$invoice_number}}</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                        <!--end::Info-->

                                    @else
                                        <h3 class="fw-bolder fs-1 mb-1">
                                            اشکال در پرداخت
                                        </h3>
                                        <br/>
                                        <div class="fs-7 mb-8">
                                            پیغام درگاه:
                                            <b>
                                                {{base64_decode($message)}}
                                            </b>

                                            <br/>
                                            <hr>
                                            <br/>

                                            در صورتیکه در پرداخت خود دچار مشکل شدید و پول از حساب شما کسر شده است کافیست
                                            با پشتیبانی در ارتباط باشید
                                        </div>
                                    @endif
                                </div>
                                <!--end::Card Body-->
                            </div>
                            <!--end::Card-->
                            <!--begin::Image-->
                            <div
                                class="bgi-no-repeat bgi-position-center bgi-size-contain h-300px h-md-auto h-lg-300px h-xxl-auto mw-100 w-650px mx-auto"
                                style="background-image: url('{{asset('assets/media/successpay.png')}}')"></div>
                            <!--end::Image-->


                        </div>

                        <!--end::Card Body-->
                    </div>
                    <!--end::Card-->
                </div>
                <!--end::Container-->
            </div>
            <!--end::Content-->
        </div>
        <!--end::Main-->
    </div>
@endsection
@push('scripts')
    <script>
        $(document).ready(function () {
            $('.js-select').select2().on('change', function (e) {
            });
        })
    </script>
@endpush
@section('footer_script')
    <script src="{{ asset('assets/plugins/global/persian-date.min.js') }}"></script>
    <script src="{{ asset('assets/plugins/global/persian-datepicker.min.js') }}"></script>
    <script src="{{ asset('assets/js/custom/general/wizard.js') }}"></script>
@endsection
