@extends('layouts.base')

@section('title')
    پنل کاربری
@endsection

@push('header_style_after')
    <style>
        .table-striped > tbody > tr td {
            text-align: right;
        }
        .pagination {
            justify-content: flex-end;
            direction: ltr;
        }
        .pg-text {
            text-align: left !important;
        }
        .cl-sort {
            margin-right: 10px;
            opacity: 0.5;
        }
        .sl-fr a {
            margin-right: 11px;
        }
    </style>
@endpush

@section('content')

    <div class=" container-xxl ">
        <div class="toolbar direction-rtl" id="kt_toolbar">
            <div class="d-flex flex-stack flex-wrap flex-sm-nowrap">
                <!--begin::Info-->
                <div class="d-flex flex-column align-items-start justify-content-center flex-wrap me-1">
                    <!--begin::Title-->
                    <h3 class="text-dark fw-bolder my-1">
                      تراکنش ها
                    </h3>
                    <!--end::Title-->

                    <!--begin::Breadcrumb-->
                    <ul class="breadcrumb breadcrumb-line bg-transparent text-muted fw-bold p-0 my-1 fs-7">
                        <li class="breadcrumb-item">
                            <a href="{{route('dashboard')}}" class="text-muted text-hover-primary">
                                داشبورد </a>
                        </li>
                        <li class="breadcrumb-item text-dark">
                            تراکنش ها
                        </li>
                    </ul>
                    <!--end::Breadcrumb-->
                </div>
                <!--end::Info-->

                <!--begin::Nav-->
                <div class="d-flex align-items-center flex-nowrap text-nowrap overflow-auto py-1">
                    {{--                <a href="/start/apps/calendar.html" class="btn btn-active-accent active  fw-bolder ">--}}
                    {{--                    Calendar--}}
                    {{--                </a>--}}
                    {{--                --}}
                </div>
                <!--end::Nav-->
            </div>
        </div>

        <div class="">
            <!--begin::Row-->
            <div class="row g-0 g-xl-5 g-xxl-8 direction-rtl">
                <div class="col-xl-12">
                    <div class="card">
                        <div class="card-body p-12">
                            <div class="row mb-12">
                                <livewire:payment.payment-transactions   theme="bootstrap-4" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
@endsection
