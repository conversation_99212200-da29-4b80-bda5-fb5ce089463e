@extends('layouts.base')

@section('title')
    مدیریت سشن ها
@endsection

@section('header_style')
    <link rel="stylesheet" type="text/css" href="{{asset('assets/plugins/datatable/datatables.min.css')}}"/>
    <link rel="stylesheet" type="text/css" href="{{asset('assets/plugins/custom/prismjs/prismjs.bundle.css')}}"/>
@endsection

@section('content')

    <!--begin::Inbox-->
    <div class="d-flex flex-column flex-lg-row">

        <!--begin::List-->
        <div class="flex-lg-row-fluid d-block" id="kt_inbox_list">
            <!--begin::Card-->
            <div class="card mt-10 mt-lg-0">
                <!--begin::Header-->
                <div class="card-header align-items-center card-px">
                    <!--begin::Toolbar-->
                    <div class="d-flex align-items-center">

                        <div class="d-flex align-items-center me-1 my-2">
                            <a href="#" onclick="event.preventDefault();" data-bs-toggle="tooltip" data-bs-original-title="پشتیبانی" data-bs-trigger="hover">
                                <button type="button" class="btn btn-icon btn-active-light-primary btn-sm me-2" data-bs-toggle="popover" data-bs-placement="bottom" data-bs-html="true"
                                        title="<div class='text-start'>‌راه‌های ارتباطی</div>"
                                        data-bs-content="<div class='direction-rtl'>
                                                            شماره تماس: <a href='tel:09111111111'>۰۹۱۱۱۱۱۱۱۱۱</a><br>
                                                            ایمیل: <a href='mailto: <EMAIL>'><EMAIL></a>
                                                        </div>">
                                    <i class="fas fa-user-nurse fs-6"></i>
                                </button>
                            </a>
                            <a href="#" id="guide-tooltip" onclick="event.preventDefault();" data-bs-toggle="tooltip" data-bs-original-title="راهنما" data-bs-trigger="hover">
                                <button type="button" class="btn btn-icon btn-active-light-primary btn-sm me-2" data-bs-toggle="modal" data-bs-target="#session_guide">
                                    <i class="fas fa-life-ring fs-6"></i>
                                </button>
                            </a>

                        </div>
                    </div>
                    <!--end::Toolbar-->
                    <!--begin::Pagination-->
                    <div class="d-flex align-items-center justify-content-sm-end text-end my-2">
                        <!--begin::Per Page Dropdown-->
                        <div class="d-flex align-items-center me-2">
                            <h3 class="text-muted fw-bold me-2">سشن های فعال شما</h3>
                        </div>
                        <!--end::Per Page Dropdown-->

                    </div>
                    <!--end::Pagination-->
                </div>
                <!--end::Header-->

                <!--begin::Body-->

                <livewire:users.active-sessions />

                <!--end::Body-->
            </div>
            <!--end::Card-->
        </div>
        <!--end::List-->
    </div>
    <!--end::Inbox-->
    <!--start::Modals-->
    <div class="modal fade direction-rtl" tabindex="-1" id="session_guide">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">راهنمای سشن های فعال</h5>

                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        <span class="svg-icon svg-icon-2x"></span>
                    </div>
                    <!--end::Close-->
                </div>

                <div class="modal-body">
                    <p>حداکثر تا 2 دستگاه قابلیت ورود دارند <br>باید از یک دستگاه خارج بشید</p>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">بستن</button>
                </div>
            </div>
        </div>
    </div>


@endsection
@section('footer_script')
    <script type="text/javascript" src="{{asset('assets/plugins/datatable/datatables.min.js')}}"></script>
    {{--    @include('layouts.components.datatable');--}}
    <script>
        $('[data-bs-toggle="tooltip"]').on('mouseleave', function(){
            $(this).tooltip('hide');
        });
    </script>

@endsection

