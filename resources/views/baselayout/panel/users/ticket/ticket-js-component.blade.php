
@push('scripts')
    <script>

        document.addEventListener('livewire:init',() => {
            // let progressSection = document.querySelector('#progressbar'),
            //     progressBar = progressSection.querySelector('#progress-fill');

            document.addEventListener('livewire-upload-start' , () => {});
            document.addEventListener('livewire-upload-finish' , () => {
                $('#uploading_message').slideUp('100');
                $('.progress-msg').html('');
            });
            document.addEventListener('livewire-upload-error' , () => {});
            document.addEventListener('livewire-upload-progress' , (event,uploadedFilename) => {
                // progressSection.style.display = "block";
                $('#uploading_message').slideDown('100');
                let valeur = event.detail.progress;
                console.log(event);
                $('.progress-bar').css('width', valeur+'%').attr('aria-valuenow', valeur);

                if(valeur == 100){
                    $('.progress-msg').html('در حال پردازش');
                }
            });
        });


    </script>
@endpush
