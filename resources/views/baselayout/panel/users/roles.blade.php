@extends('layouts.admin')

@section('title')
    پنل کاربری
@endsection

@section('header_style')
    <link rel="stylesheet" type="text/css" href="{{asset('assets/plugins/datatable/datatables.min.css')}}"/>
    <link rel="stylesheet" type="text/css" href="{{asset('assets/plugins/custom/prismjs/prismjs.bundle.css')}}"/>
@endsection
@section('content')

    <table class="table table-rounded table-striped border gy-7 gs-7 bg-white amanj-datatable direction-rtl">
        <thead>
        <tr class="fw-bold fs-6 text-gray-800 border-bottom border-gray-200">
            <th>ردیف</th>
            <th>نام کاربری</th>
            <th>نام</th>
            <th>نام خانوادگی</th>
            <th>ایمیل</th>
            <th>اقدامات</th>
        </tr>
        </thead>
        <tbody>
{{--        @foreach($users as $user)--}}
{{--            <tr>--}}
{{--                <td>--}}
{{--                    {{$user->id}}--}}
{{--                </td>--}}
{{--                <td>--}}
{{--                    {{$user->user_name}}--}}
{{--                </td>--}}
{{--                <td>--}}
{{--                    {{$user->first_name}}--}}
{{--                </td>--}}
{{--                <td>--}}
{{--                    {{$user->last_name}}--}}
{{--                </td>--}}
{{--                <td>--}}
{{--                    {{$user->email}}--}}
{{--                </td>--}}
{{--                <td>--}}
{{--                    d    </td>--}}
{{--            </tr>--}}

{{--        @endforeach--}}
        </tbody>
    </table>

@endsection
@section('footer_script')
    <script type="text/javascript" src="{{asset('assets/plugins/datatable/datatables.min.js')}}"></script>
    {{--    @include('layouts.components.datatable');--}}

    <script>
        $(document).ready(function (){

            window.table = $('.amanj-datatable').DataTable({
                retrieve: true,
                "deferRender": true,
                fixedHeader: {
                    header: true,
                    // footer: false,
                    headerOffset: 65
                },
                "order": [[0, "desc"]],
                columnDefs: [
                    { "targets": [0], "searchable": true, "orderable": false, "visible": true},
                    { "targets": [1], "searchable": true},
                ],
                "stateSave": true,
                "stateSaveParams": function (settings, data) {
                    data.search.search = "";
                },
                oLanguage: {
                    "sSearch": "جستجو: ",
                    "sShow": "تعداد نمایش در صفحه: ",
                    "next": "بعدی",
                    "sInfo": "نمایش _START_ تا _END_ از _TOTAL_ ورودی",
                    "sInfoEmpty": "بدون داده",
                    "sInfoFiltered": "( _MAX_ داده فیلتر شده است)",
                    "sLengthMenu": "تعداد نمایش در صفحه:  _MENU_ ",
                    "sZeroRecords": "بدون اطلاعات",
                    oPaginate: {
                        sNext: '<span class="pagination-default"></span><span class="pagination-fa"><i class="fa fa-chevron-right" ></i></span>',
                        sPrevious: '<span class="pagination-default"></span><span class="pagination-fa"><i class="fa fa-chevron-left" ></i></span>'

                    }
                },

                dom: 'fBlrtip',
                buttons: [
                    // 'colvis',
                    // 'csv',
                    // 'excel',
                    //  'print',
                    // 'pageLength',
                    // 'reload',
                    {
                        extend: 'excel',
                        text: 'دریافت Excel',
                        exportOptions: {
                            columns: ':visible'
                        },
                    },
                    // header: true,
                    {
                        extend: 'colvis',
                        text: 'فیلتر ستون ها ',
                    },
                    {
                        extend: 'print',
                        footer: true,
                        exportOptions: {
                            columns: ':visible',
                            filter: 'applied', order: 'current'
                        },

                        text: 'چاپ / PDF',
                        autoPrint: true,
                        customize: function (win) {
                            $(win.document.body).find('table').after('');
                            $(win.document.body).find('table').before('<div class="row"><div class="col-md-1"></div><div class="col-md-10">\n' +
                                '               <br /><br /> <div class="project-det2ail project-report" style="    direction: rtl;    text-align: right; border:0px">\n' +

                                '                    <span class="project-title">\n' + window.document.title +
                                '                    </span>\n' +
                                '                <br />    <br />   تاریخ چاپ گزارش: {{jdate('today')->format('%A, %d %B %y')}} <img src="{{asset('assets/media/logos/logo-compact.svg')}}" style="\n' +
                                '    float: left;\n' +
                                '    height: 70px;\n' +
                                '">\n' +
                                '\n' +
                                '                </div>\n' +
                                '            </div></div>');



                        }
                    },

                ],
                "footerCallback": function (row, data, start, end, display) {

                }

            });
        });
    </script>
@endsection

