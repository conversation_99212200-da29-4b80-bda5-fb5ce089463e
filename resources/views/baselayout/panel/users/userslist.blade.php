@extends('layouts.admin')

@section('title')
    پنل کاربری
@endsection

@section('header_style')
    <link rel="stylesheet" type="text/css" href="{{asset('assets/plugins/datatable/datatables.min.css')}}"/>
    <link rel="stylesheet" type="text/css" href="{{asset('assets/plugins/custom/prismjs/prismjs.bundle.css')}}"/>
@endsection
@section('content')

    <div>
        <div class="users-body-contianer">
            <ul class="users-body nav nav-pills nav-pills-sm nav-light">
                <li class="nav-item">
                    <a class="nav-link btn btn-active-light btn-color-muted py-2 px-4 active fw-bolder me-2"
                       data-bs-toggle="tab" href="#kt_tab_pane_1_1">کاربران</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link btn btn-active-light btn-color-muted py-2 px-4 fw-bolder me-2"
                       data-bs-toggle="tab" href="#kt_tab_pane_1_2">نقش های کاربری</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link btn btn-active-light btn-color-muted py-2 px-4 fw-bolder" data-bs-toggle="tab"
                       href="#kt_tab_pane_1_3">حق های دسترسی</a>
                </li>
            </ul>
        </div>
        <div class="card-body">



            <div class="tab-content" id="myTabContent">
                <div class="amanj-bg-table tab-pane fade show active" id="kt_tab_pane_1_1" role="tabpanel">
                    <table class="table table-rounded table-striped border gy-7 gs-7 bg-white amanj-datatable direction-rtl">

                        <thead>
                        <tr class="fw-bold fs-6 text-gray-800 border-bottom border-gray-200">
                            <th>ردیف</th>
                            <th>نام کاربری</th>
                            <th>نام</th>
                            <th>نام خانوادگی</th>
                            <th>ایمیل</th>
                            <th>اقدامات</th>
                        </tr>
                        </thead>
                        <tbody>
                        {{-- @foreach($users as $user)
                            <tr>
                                <td>
                                    {{$user->id}}
                                </td>
                                <td>
                                    {{$user->user_name}}
                                </td>
                                <td>
                                    {{$user->first_name}}
                                </td>
                                <td>
                                    {{$user->last_name}}
                                </td>
                                <td>
                                    {{$user->email}}
                                </td>
                                <td>
                                    <a class="btn btn-icon btn-sm me-2" data-bs-toggle="modal"
                                            data-bs-target="#edit_users" wire:click="edit({{ $user->id }})">
                                        <div class="edit-button svg-icon svg-icon-muted svg-icon-2x"></div>
                                    </a>

                                </td>
                            </tr>

                        @endforeach --}}
                        </tbody>
                    </table>
                </div>

                <div class="tab-pane fade" id="kt_tab_pane_1_2" role="tabpanel">
                    <livewire:admin.users.roles/>
                </div>

                <div class="tab-pane fade" id="kt_tab_pane_1_3" role="tabpanel">


                    <livewire:admin.users.permissions/>
                </div>
            </div>
        </div>
    </div>


    <livewire:users/>

@endsection


@section('footer_script')
    <script type="text/javascript" src="{{asset('assets/plugins/datatable/datatables.min.js')}}"></script>
    {{--    @include('layouts.components.datatable');--}}
    <script>
        $(document).ready(function () {
            $('.js-select').select2().on('change', function (e) {
                let livewire = $(this).data('livewire')
                eval(livewire).set('permission_category', $(this).val());
            });
        })
    </script>


    <script>
        $(document).ready(function () {

            $('.dataTables_filter input')
                .off()
                .on('keyup', function () {
                    console.log('this.value');
                    $('.amanj-datatable').DataTable().search(this.value.trim(), false, false).draw();
                });


            window.table = $('.amanj-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{!! route('users.ajax') !!}',
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'user_name', name: 'user_name'},
                    {data: 'first_name', name: 'first_name'},
                    {data: 'last_name', name: 'last_name'},
                    {data: 'email', name: 'email'},
                    {
                        data: 'id',
                        render: function (data, type, row) {
                            return '<a class="btn btn-icon btn-sm me-2" data-bs-toggle="modal" data-bs-target="#edit_users"  onclick="$(\'#fire_livewire\').attr(\'wire:click\',\'edit(' + data + ')\');$(\'#fire_livewire\').trigger(\'click\');"  wire:key="d' + data + '" ><div class="edit-button svg-icon svg-icon-muted svg-icon-2x"></div></a>'
                        }
                    },
                ],
                // retrieve: true,
                // "deferRender": true,
                // fixedHeader: {
                    // header: true,
                    // footer: false,
                    // headerOffset: $('#kt_header').outerHeight()
                // },
                "order": [[0, "desc"]],
                "stateSave": false,
                "stateSaveParams": function (settings, data) {
                    data.search.search = "";
                },
                oLanguage: {
                    "sSearch": "جستجو: ",
                    "sShow": "تعداد نمایش در صفحه: ",
                    "next": "بعدی",
                    "sInfo": "نمایش _START_ تا _END_ از _TOTAL_ ورودی",
                    "sInfoEmpty": "بدون داده",
                    "sInfoFiltered": "( _MAX_ داده فیلتر شده است)",
                    "sLengthMenu": "تعداد نمایش در صفحه:  _MENU_ ",
                    "sZeroRecords": "بدون اطلاعات",
                    oPaginate: {
                        sNext: '<span class="pagination-default"></span><span class="pagination-fa"><i class="fa fa-chevron-right" ></i></span>',
                        sPrevious: '<span class="pagination-default"></span><span class="pagination-fa"><i class="fa fa-chevron-left" ></i></span>'

                    }
                },

                dom: 'fBlrtip',
                buttons: [
                    // 'colvis',
                    // 'csv',
                    // 'excel',
                    //  'print',
                    // 'pageLength',
                    // 'reload',
                    {
                        extend: 'excel',
                        text: 'دریافت Excel',
                        exportOptions: {
                            columns: ':visible'
                        },
                    },
                    // header: true,
                    {
                        extend: 'colvis',
                        text: 'فیلتر ستون ها ',
                    },
                    {
                        extend: 'print',
                        footer: true,
                        exportOptions: {
                            columns: ':visible',
                            filter: 'applied', order: 'current'
                        },

                        text: 'چاپ / PDF',
                        autoPrint: false,
                        customize: function (win) {
                            $(win.document.body).find('table').after('');
                            $(win.document.body).find('table').before('<div class="row"><div class="col-md-1"></div><div class="col-md-10">\n' +
                                '               <br /><br /> <div class="project-det2ail project-report" style="    direction: rtl;    text-align: right; border:0px">\n' +

                                '                    <span class="project-title">\n' + window.document.title +
                                '                    </span>\n' +
                                '                <br />    <br />   تاریخ چاپ گزارش: {{jdate('today')->format('%A, %d %B %y')}} <img src="{{asset('assets/media/logos/logo-compact.svg')}}" style="\n' +
                                '    float: left;\n' +
                                '    height: 70px;\n' +
                                '">\n' +
                                '\n' +
                                '                </div>\n' +
                                '            </div></div>');


                        }
                    },

                ],
                "footerCallback": function (row, data, start, end, display) {

                }

            });

        });
    </script>

    <livewire:components.toast/>
@endsection

