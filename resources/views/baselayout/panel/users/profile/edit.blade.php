@extends('layouts.base')

@section('title')
    ویرایش اطلاعات کاربری
@endsection

@section('header_style')
    <link href="{{ asset('assets/plugins/global/persian-datepicker.min.css') }}" rel="stylesheet" type="text/css"/>
@endsection

@section('content')

    <div class=" container-xxl ">
        <!--begin::Main-->
        <div class="d-flex flex-column flex-column-fluid">
            <!--begin::toolbar-->
            <div class="toolbar direction-rtl" id="kt_toolbar">
                <div class="d-flex flex-stack flex-wrap flex-sm-nowrap">
                    <!--begin::Info-->
                    <div class="d-flex flex-column align-items-start justify-content-center flex-wrap me-1">
                        <!--begin::Title-->
                        <h3 class="text-dark fw-bolder my-1">ویرایش پروفایل</h3>
                        <!--end::Title-->
                        <!--begin::Breadcrumb-->
                        <ul class="breadcrumb breadcrumb-line bg-transparent text-muted fw-bold p-0 my-1 fs-7">
                            <li class="breadcrumb-item">
                                <a href="{{route('dashboard')}}" class="text-muted text-hover-primary">پنل کاربری</a>
                            </li>
                            <li class="breadcrumb-item">مشخصات</li>
                            <li class="breadcrumb-item text-dark">تنظیمات</li>
                        </ul>
                        <!--end::Breadcrumb-->
                    </div>
                    <!--end::Info-->
                    <!--begin::Nav-->
                    <div class="d-flex align-items-center flex-nowrap text-nowrap overflow-auto py-1">
                        <a href="{{route('users.profile.edit')}}" class="btn btn-active-accent  fw-bolder ms-3">پروفایل</a>
                        <a href="{{route('users.settings')}}" class="btn btn-active-accent  active fw-bolder ms-3">تنظیمات</a>
                    </div>
                    <!--end::Nav-->
                </div>
            </div>
            <!--end::toolbar-->
            <!--begin::Content-->
            <div class="content fs-6 d-flex flex-column-fluid direction-rtl" id="kt_content">
                <!--begin::Container-->
                <div class="col-md-12">
                    <!--begin::Card-->
                    <div class="card">
                        <!--begin::Card Body-->
                        <div class="card-body p-10 p-lg-15 p-xxl-30">




                            <!--begin::Stepper 1-->
                            <div class="stepper stepper-1 d-flex flex-column flex-xl-row flex-row-fluid"
                                 id="kt_stepper">
                                <!--begin::Aside-->
                                <!--begin::Aside-->
                                <!--begin::Content-->
                                <div class="d-flex flex-row-fluid justify-content-center">
                                    <!--begin::Form-->
                                    <div class="pt-10 w-100 w-md-400px w-xl-500px" novalidate="novalidate"
                                          id="kt_stepper_form">
                                        <!--begin::Step 1-->
                                        <div class=" current" data-kt-stepper-element="content">
                                            <div class="w-100">
                                                <livewire:users.profile.edit.user-base/>
                                            </div>
                                        </div>
                                        <!--end::Step 1-->

                                    </div>
                                    <!--end::Form-->
                                </div>
                                <!--end::Content-->
                            </div>
                            <!--end::Stepper 1-->
                        </div>
                        <!--end::Card Body-->
                    </div>
                    <!--end::Card-->
                </div>
                <!--end::Container-->
            </div>
            <!--end::Content-->
        </div>
        <!--end::Main-->
    </div>
@endsection
@push('scripts')
    <script>
        $(document).ready(function () {
            $('.js-select').select2().on('change', function (e) {
            });
        })
    </script>
@endpush
@section('footer_script')
    <script src="{{ asset('assets/plugins/global/persian-date.min.js') }}"></script>
    <script src="{{ asset('assets/plugins/global/persian-datepicker.min.js') }}"></script>
{{--    <script src="{{ asset('assets/js/custom/general/wizard.js') }}"></script>--}}
@endsection
