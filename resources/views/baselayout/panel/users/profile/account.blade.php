@extends('layouts.base')

@section('title')
   پروفایل کاربری
@endsection

@section('header_style')
    <link rel="stylesheet" type="text/css" href="{{asset('assets/plugins/datatable/datatables.min.css')}}"/>
    <link rel="stylesheet" type="text/css" href="{{asset('assets/js/croppie/croppie.css')}}"/>
    <link rel="stylesheet" type="text/css" href="{{asset('assets/plugins/custom/prismjs/prismjs.bundle.css')}}"/>
@endsection

@section('content')

    <div class=" container-xxl ">
    <!--begin::Main-->
    <div class=" d-flex flex-column flex-column-fluid">
        <!--begin::toolbar-->
        <div class="toolbar direction-rtl" id="kt_toolbar">
            <div class="d-flex flex-stack flex-wrap flex-sm-nowrap">
                <!--begin::Info-->
                <div class="d-flex flex-column align-items-start justify-content-center flex-wrap me-1">
                    <!--begin::Title-->
                    <h3 class="text-dark fw-bolder my-1">ویرایش پروفایل</h3>
                    <!--end::Title-->
                    <!--begin::Breadcrumb-->
                    <ul class="breadcrumb breadcrumb-line bg-transparent text-muted fw-bold p-0 my-1 fs-7">
                        <li class="breadcrumb-item">
                            <a href="{{route('dashboard')}}" class="text-muted text-hover-primary">پنل کاربری</a>
                        </li>
                        <li class="breadcrumb-item">مشخصات</li>
                        <li class="breadcrumb-item text-dark">ویرایش پروفایل</li>
                    </ul>
                    <!--end::Breadcrumb-->
                </div>
                <!--end::Info-->
                <!--begin::Nav-->
                <div class="d-flex align-items-center flex-nowrap text-nowrap overflow-auto py-1">
                    <a href="{{route('users.profile.edit')}}" class="btn btn-active-accent active fw-bolder ms-3">پروفایل</a>
                    <a href="{{route('users.settings')}}" class="btn btn-active-accent  fw-bolder ms-3">تنظیمات</a>
                </div>
                <!--end::Nav-->
            </div>
        </div>
        <!--end::toolbar-->
        <!--begin::Content-->
        <div class="content fs-6 d-flex flex-column-fluid" id="kt_content">
            <!--begin::Container-->
            <div class="col-12">
                <!--begin::Profile Account-->
                <div class="card">
                    <!--begin::Form-->
                    <form class="form d-flex flex-center">
                        <div class="card-body mw-800px py-20">

                            <livewire:users.profile.picture />

                        </div>
                    </form>
                    <!--end::Form-->
                </div>
                <!--end::Profile Account-->
            </div>
            <!--end::Container-->
        </div>
        <!--end::Content-->
    </div>
    <!--end::Main-->
    </div>
@endsection
@section('footer_script')
    <script src="{{asset('assets/js/croppie/croppie.js')}}"></script>
    <script>

        document.addEventListener('livewire:init',() => {
            // let progressSection = document.querySelector('#progressbar'),
            //     progressBar = progressSection.querySelector('#progress-fill');

            document.addEventListener('livewire-upload-start' , () => {});
            document.addEventListener('livewire-upload-finish' , () => {

            });
            document.addEventListener('livewire-upload-error' , () => {});
            document.addEventListener('livewire-upload-progress' , (event) => {
                // progressSection.style.display = "block";
                let valeur = event.detail.progress;
                console.log(`${event.detail.progress}%`);
                $('.progress-bar').css('width', valeur+'%').attr('aria-valuenow', valeur);

                if(valeur == 100){
                    $('#uploading_message').html('در حال پردازش');
                }
            });
        });
    </script>
{{--    <script src="{{asset('assets/js/dropzone-5.7.0/dist/min/dropzone.min.js')}}"/>--}}
@endsection

