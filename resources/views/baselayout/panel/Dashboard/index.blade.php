@extends('layouts.base')

@section('title')
    پنل کاربری
@endsection


@section('content')
    <div class=" container-xxl ">
        <!--begin::Row-->
        <div class="row g-0 g-xl-5 g-xxl-8">
            <div class="col-xl-4">
                <livewire:meeting.creator-wapper/>
            </div>

            <div class="col-xl-8">
                <livewire:meeting.licence-manager/>
            </div>
        </div>
        <!--end::Row-->


        <!--begin::Row-->
        <div class="row g-0 g-xl-5 g-xxl-8">
            <div class="col-md-12">
                <div class="card card-stretch mb-5 mb-xxl-8">
                    <!--begin::Body-->
                    <div class="card-body direction-ltr">
                        <!--begin::Stats-->
                        <div class="">
                            <!--begin::Row-->
                            <div class="row gap-3">
                                <div class="col">
                                    <button type="button"
                                            {{--                                            data-bs-toggle="modal" data-bs-target="#kt_modal_add_new_admin"--}}
                                            class="btn btn-outline btn-bg-light btn-color-gray-600 btn-active-light-success border-dashed border-active border-primary px-6 py-7 text-start w-100 min-w-150px  d-flex flex-stack">
                                        <!--begin::Svg Icon | path: icons/duotune/general/gen002.svg-->
                                        <span class="svg-icon svg-icon-2x ms-n1"><svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24" height="24"
                                                viewBox="0 0 24 24" fill="none">
                                        <path opacity="0.3"
                                              d="M4.05424 15.1982C8.34524 7.76818 13.5782 3.26318 20.9282 2.01418C21.0729 1.98837 21.2216 1.99789 21.3618 2.04193C21.502 2.08597 21.6294 2.16323 21.7333 2.26712C21.8372 2.37101 21.9144 2.49846 21.9585 2.63863C22.0025 2.7788 22.012 2.92754 21.9862 3.07218C20.7372 10.4222 16.2322 15.6552 8.80224 19.9462L4.05424 15.1982ZM3.81924 17.3372L2.63324 20.4482C2.58427 20.5765 2.5735 20.7163 2.6022 20.8507C2.63091 20.9851 2.69788 21.1082 2.79503 21.2054C2.89218 21.3025 3.01536 21.3695 3.14972 21.3982C3.28408 21.4269 3.42387 21.4161 3.55224 21.3672L6.66524 20.1802L3.81924 17.3372ZM16.5002 5.99818C16.2036 5.99818 15.9136 6.08615 15.6669 6.25097C15.4202 6.41579 15.228 6.65006 15.1144 6.92415C15.0009 7.19824 14.9712 7.49984 15.0291 7.79081C15.0869 8.08178 15.2298 8.34906 15.4396 8.55884C15.6494 8.76862 15.9166 8.91148 16.2076 8.96935C16.4986 9.02723 16.8002 8.99753 17.0743 8.884C17.3484 8.77046 17.5826 8.5782 17.7474 8.33153C17.9123 8.08486 18.0002 7.79485 18.0002 7.49818C18.0002 7.10035 17.8422 6.71882 17.5609 6.43752C17.2796 6.15621 16.8981 5.99818 16.5002 5.99818Z"
                                              fill="black"></path>
                                        <path
                                            d="M4.05423 15.1982L2.24723 13.3912C2.15505 13.299 2.08547 13.1867 2.04395 13.0632C2.00243 12.9396 1.9901 12.8081 2.00793 12.679C2.02575 12.5498 2.07325 12.4266 2.14669 12.3189C2.22013 12.2112 2.31752 12.1219 2.43123 12.0582L9.15323 8.28918C7.17353 10.3717 5.4607 12.6926 4.05423 15.1982ZM8.80023 19.9442L10.6072 21.7512C10.6994 21.8434 10.8117 21.9129 10.9352 21.9545C11.0588 21.996 11.1903 22.0083 11.3195 21.9905C11.4486 21.9727 11.5718 21.9252 11.6795 21.8517C11.7872 21.7783 11.8765 21.6809 11.9402 21.5672L15.7092 14.8442C13.6269 16.8245 11.3061 18.5377 8.80023 19.9442ZM7.04023 18.1832L12.5832 12.6402C12.7381 12.4759 12.8228 12.2577 12.8195 12.032C12.8161 11.8063 12.725 11.5907 12.5653 11.4311C12.4057 11.2714 12.1901 11.1803 11.9644 11.1769C11.7387 11.1736 11.5205 11.2583 11.3562 11.4132L5.81323 16.9562L7.04023 18.1832Z"
                                            fill="black"></path>
                                        </svg></span>
                                        <span class="text-gray-800 fw-bolder fs-6 d-block">
                                            <span
                                                class="badge badge-exclusive badge-light-success fw-semibold fs-8 px-2 py-1 ms-1"
                                                data-bs-toggle="tooltip" data-bs-placement="left"
                                                data-bs-original-title="ارسال پیامک به کاربران شما با خط خدماتی"
                                                data-kt-initialized="1">بزودی</span>
                           پنل پیامک

                        </span>
                                    </button>
                                </div>
                                <div class="col">
                                    <button type="button"
                                            data-bs-toggle="modal" data-bs-target="#kt_modal_add_new_guest"
                                            class="btn btn-outline  d-flex flex-stack btn-bg-light btn-color-gray-600 btn-active-light-danger border-dashed border-active px-6 py-7 text-start w-100 min-w-150px">
                                        <!--begin::Svg Icon | path: icons/duotune/general/gen005.svg-->
                                        <span class="svg-icon svg-icon-muted svg-icon-2x">
                                            <svg width="25" height="28" viewBox="0 0 25 28" fill="none"
                                                 xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M24.0259 11.4401H1.97259C1.69436 11.4505 1.43123 11.5693 1.2394 11.7711C1.04757 11.9729 0.942247 12.2417 0.945922 12.5201V20.0801C0.933592 21.0248 1.10836 21.9627 1.46016 22.8395C1.81196 23.7164 2.33382 24.515 2.99568 25.1892C3.65754 25.8635 4.4463 26.4001 5.3165 26.7681C6.1867 27.1361 7.12112 27.3282 8.06592 27.3334H17.9993C19.8855 27.288 21.6778 26.5012 22.988 25.1436C24.2983 23.7859 25.0208 21.9667 24.9993 20.0801V12.5201C25.0037 12.2504 24.9057 11.989 24.7251 11.7886C24.5445 11.5882 24.2947 11.4637 24.0259 11.4401ZM8.73259 21.8401C8.51017 21.84 8.29271 21.7744 8.1073 21.6515C7.92189 21.5287 7.77672 21.354 7.68989 21.1492C7.60306 20.9444 7.5784 20.7186 7.61899 20.5C7.65957 20.2813 7.76361 20.0794 7.91813 19.9194C8.07266 19.7594 8.27084 19.6484 8.48798 19.6003C8.70513 19.5522 8.93164 19.569 9.1393 19.6487C9.34695 19.7283 9.52658 19.8673 9.65578 20.0484C9.78499 20.2294 9.85807 20.4445 9.86592 20.6668C9.86241 20.965 9.74146 21.2499 9.5293 21.4595C9.31714 21.6692 9.03087 21.7868 8.73259 21.7868V21.8401ZM8.73259 17.5868C8.50844 17.5868 8.28932 17.5203 8.10294 17.3958C7.91657 17.2712 7.77131 17.0942 7.68553 16.8871C7.59975 16.6801 7.5773 16.4522 7.62103 16.2323C7.66476 16.0125 7.7727 15.8105 7.9312 15.652C8.0897 15.4935 8.29164 15.3856 8.51149 15.3419C8.73133 15.2981 8.95921 15.3206 9.1663 15.4064C9.37339 15.4921 9.55039 15.6374 9.67492 15.8238C9.79945 16.0102 9.86592 16.2293 9.86592 16.4534C9.86771 16.6028 9.83962 16.7509 9.7833 16.8892C9.72697 17.0276 9.64356 17.1532 9.53796 17.2588C9.43236 17.3644 9.30672 17.4478 9.1684 17.5041C9.03009 17.5605 8.88192 17.5886 8.73259 17.5868ZM12.9993 21.8401C12.701 21.8331 12.4175 21.7088 12.2104 21.4941C12.0032 21.2794 11.889 20.9917 11.8926 20.6934C11.8926 20.3964 12.0106 20.1115 12.2206 19.9015C12.4307 19.6914 12.7155 19.5734 13.0126 19.5734C13.3096 19.5734 13.5945 19.6914 13.8045 19.9015C14.0146 20.1115 14.1326 20.3964 14.1326 20.6934C14.1291 20.9917 14.0081 21.2765 13.796 21.4862C13.5838 21.6959 13.2975 21.8135 12.9993 21.8134V21.8401ZM12.9993 17.5868C12.701 17.5798 12.4175 17.4555 12.2104 17.2408C12.0032 17.0261 11.889 16.7384 11.8926 16.4401C11.8926 16.1431 12.0106 15.8582 12.2206 15.6481C12.4307 15.4381 12.7155 15.3201 13.0126 15.3201C13.3096 15.3201 13.5945 15.4381 13.8045 15.6481C14.0146 15.8582 14.1326 16.1431 14.1326 16.4401C14.1326 16.7384 14.015 17.0246 13.8054 17.2368C13.5957 17.449 13.3109 17.5699 13.0126 17.5734L12.9993 17.5868ZM17.2393 21.8401C16.9387 21.8401 16.6504 21.7207 16.4379 21.5082C16.2253 21.2956 16.1059 21.0073 16.1059 20.7068C16.1059 20.4062 16.2253 20.1179 16.4379 19.9054C16.6504 19.6928 16.9387 19.5734 17.2393 19.5734C17.5398 19.5734 17.8281 19.6928 18.0406 19.9054C18.2532 20.1179 18.3726 20.4062 18.3726 20.7068C18.3726 21.0073 18.2532 21.2956 18.0406 21.5082C17.8281 21.7207 17.5398 21.8401 17.2393 21.8401ZM17.2393 17.5868C16.9387 17.5868 16.6504 17.4674 16.4379 17.2548C16.2253 17.0423 16.1059 16.754 16.1059 16.4534C16.1059 16.1529 16.2253 15.8646 16.4379 15.652C16.6504 15.4395 16.9387 15.3201 17.2393 15.3201C17.5398 15.3201 17.8281 15.4395 18.0406 15.652C18.2532 15.8646 18.3726 16.1529 18.3726 16.4534C18.3726 16.754 18.2532 17.0423 18.0406 17.2548C17.8281 17.4674 17.5398 17.5868 17.2393 17.5868ZM24.6393 8.13343C24.7349 8.40774 24.7203 8.7085 24.5984 8.9722C24.4765 9.2359 24.2569 9.44192 23.9859 9.54677C23.8703 9.58813 23.7487 9.61063 23.6259 9.61343H2.62592C2.2723 9.61343 1.93316 9.47296 1.68311 9.22291C1.43306 8.97286 1.29259 8.63372 1.29259 8.2801C1.28883 8.11525 1.32066 7.95153 1.38592 7.8001C1.77683 6.84295 2.37003 5.98161 3.12487 5.27509C3.87972 4.56858 4.77837 4.03358 5.75926 3.70677V1.62677C5.75926 1.3863 5.85478 1.15569 6.02481 0.985655C6.19485 0.815622 6.42546 0.720099 6.66592 0.720099C6.90639 0.720099 7.137 0.815622 7.30703 0.985655C7.47707 1.15569 7.57259 1.3863 7.57259 1.62677V3.33343H12.3059V1.62677C12.2904 1.49938 12.3021 1.37015 12.3402 1.24761C12.3783 1.12508 12.442 1.01204 12.5271 0.915961C12.6122 0.819883 12.7167 0.74296 12.8337 0.690277C12.9507 0.637594 13.0776 0.610352 13.2059 0.610352C13.3343 0.610352 13.4611 0.637594 13.5781 0.690277C13.6952 0.74296 13.7997 0.819883 13.8847 0.915961C13.9698 1.01204 14.0335 1.12508 14.0716 1.24761C14.1098 1.37015 14.1215 1.49938 14.1059 1.62677V3.33343H18.3326V1.62677C18.3171 1.49938 18.3287 1.37015 18.3669 1.24761C18.405 1.12508 18.4687 1.01204 18.5538 0.915961C18.6389 0.819883 18.7434 0.74296 18.8604 0.690277C18.9774 0.637594 19.1043 0.610352 19.2326 0.610352C19.3609 0.610352 19.4878 0.637594 19.6048 0.690277C19.7218 0.74296 19.8263 0.819883 19.9114 0.915961C19.9965 1.01204 20.0602 1.12508 20.0983 1.24761C20.1364 1.37015 20.1481 1.49938 20.1326 1.62677V3.70677C21.1713 4.05261 22.1173 4.63121 22.8984 5.39839C23.6794 6.16557 24.2749 7.10105 24.6393 8.13343Z"
                                                    fill="currentColor"/>
                                            </svg>
                                        </span>
                                        <span class="text-gray-800 fw-bolder fs-6 d-block">
                                                  <span
                                                      class="badge badge-exclusive badge-light-success fw-semibold fs-8 px-2 py-1 ms-1"
                                                      data-bs-toggle="tooltip" data-bs-placement="left"
                                                      data-bs-original-title="زمان های خالی خود را تعریف کنید و کاربران شما میتوانند جلسات خود را با شما سازماندهی کنند"
                                                      data-kt-initialized="1">بزودی</span>

                                            پنل هماهنگی جلسات کاری و شخصی
                                            </span>
                                    </button>
                                </div>
                                <div class="col">
                                    <button type="button"
                                            {{--                                            data-bs-toggle="modal" data-bs-target="#kt_modal_upload_excel"--}}
                                            class="btn  d-flex flex-stack btn-outline btn-bg-light btn-color-gray-600 btn-active-light-warning border-dashed border-active px-6 py-7 text-start w-100 min-w-150px">
                                        <!--begin::Svg Icon | path: icons/duotune/files/fil005.svg-->
                                        <span class="svg-icon svg-icon-muted svg-icon-2x"><svg width="24" height="24"
                                                                                               viewBox="0 0 24 24"
                                                                                               fill="none"
                                                                                               xmlns="http://www.w3.org/2000/svg">
<path opacity="0.3"
      d="M18 20.3C20.2091 20.3 22 18.5092 22 16.3C22 14.0909 20.2091 12.3 18 12.3C15.7909 12.3 14 14.0909 14 16.3C14 18.5092 15.7909 20.3 18 20.3Z"
      fill="currentColor"/>
<path
    d="M18 18.3C17.4 18.3 17 17.9 17 17.3V15.3C17 14.7 17.4 14.3 18 14.3C18.6 14.3 19 14.7 19 15.3V17.3C19 17.9 18.6 18.3 18 18.3Z"
    fill="currentColor"/>
<path
    d="M14.4 18.1001C14.5 18.5001 14.4 19.0001 14 19.2001C13.8 19.3001 13.7 19.3 13.5 19.3C13.2 19.3 12.8 19.1 12.6 18.8V18.7001C12.1 18.9001 11.6 19.0001 11 19.1001V19.2001C11 19.8001 10.6 20.2001 10 20.2001C9.4 20.2001 9 19.8001 9 19.2001V19.1001C8.4 19.0001 7.89999 18.9001 7.39999 18.7001V18.8C7.19999 19.1 6.9 19.3 6.5 19.3C6.3 19.3 6.2 19.3001 6 19.2001C5.5 18.9001 5.40001 18.3 5.60001 17.8V17.7001C5.20001 17.4001 4.79999 17 4.39999 16.5H4.3C4.1 16.6 4 16.6001 3.8 16.6001C3.5 16.6001 3.09999 16.4001 2.89999 16.1001C2.59999 15.6001 2.8 15.0001 3.3 14.7001H3.39999C3.19999 14.2001 3.1 13.7001 3 13.1001C2.4 13.1001 2 12.7001 2 12.1001C2 11.5001 2.4 11.1001 3 11.1001H3.10001C3.20001 10.5001 3.3 10 3.5 9.5H3.39999C2.89999 9.2 2.8 8.6001 3 8.1001C3.3 7.6001 3.89999 7.50007 4.39999 7.70007H4.5C4.8 7.30007 5.2 6.9 5.7 6.5V6.40002C5.4 5.90002 5.60001 5.3 6.10001 5C6.60001 4.7 7.2 4.90002 7.5 5.40002V5.5C8 5.3 8.50001 5.2001 9.10001 5.1001V5C9.10001 4.4 9.50001 4 10.1 4C10.7 4 11.1 4.4 11.1 5V5.1001C11.7 5.2001 12.2 5.3 12.7 5.5V5.40002C13 4.90002 13.6 4.8 14.1 5C14.6 5.3 14.7 5.90002 14.5 6.40002V6.5C14.9 6.8 15.3 7.20007 15.7 7.70007H15.8C16.3 7.40007 16.9 7.6001 17.2 8.1001C17.5 8.6001 17.3 9.2 16.8 9.5H16.7C16.9 10 17 10.5001 17.1 11.1001H17.2C17.8 11.1001 18.2 11.5001 18.2 12.1001C16 12.1001 14.2 13.9001 14.2 16.1001C14 17.0001 14.2 17.6001 14.4 18.1001ZM11.8 8.40002H8.89999C8.59999 8.40002 8.4 8.5001 8.2 8.6001C8.1 8.7001 7.99999 9.00005 7.89999 9.30005L7.39999 11.9C7.39999 12.1 7.3 12.3 7.3 12.3C7.3 12.5 7.4 12.6001 7.5 12.7001C7.6 12.8001 7.8 12.9 8 12.9C8.2 12.9 8.40001 12.8001 8.60001 12.6001C8.90001 12.4001 9.1 12.3001 9.2 12.2001C9.3 12.1001 9.59999 12.1001 9.89999 12.1001C10.2 12.1001 10.4 12.2 10.6 12.3C10.8 12.4 11 12.6 11.1 12.9C11.2 13.2 11.3 13.5 11.3 13.8C11.3 14.1 11.2 14.4001 11.1 14.7001C11 15.0001 10.8 15.2 10.6 15.3C10.4 15.4 10.1 15.5 9.89999 15.5C9.59999 15.5 9.30001 15.4001 9.10001 15.2001C8.80001 15.0001 8.7 14.8 8.5 14.4C8.3 14 8.1 13.9 7.8 13.9C7.6 13.9 7.5 14.0001 7.3 14.1001C7.2 14.2001 7.10001 14.4 7.10001 14.5C7.10001 14.7 7.19999 15 7.39999 15.3C7.59999 15.6 7.9 15.9001 8.3 16.1001C8.7 16.3001 9.19999 16.5 9.89999 16.5C10.5 16.5 11 16.4001 11.5 16.1001C12 15.8001 12.3 15.5001 12.5 15.1001C12.7 14.7001 12.9 14.2001 12.9 13.6001C12.9 13.2001 12.8 12.9001 12.7 12.6001C12.6 12.3001 12.4 12 12.2 11.8C12 11.6 11.7 11.4 11.4 11.3C11.1 11.2 10.8 11.1001 10.4 11.1001C9.99999 11.1001 9.5 11.2 9 11.5L9.3 9.70007H11.9C12.2 9.70007 12.4 9.6 12.5 9.5C12.6 9.4 12.7 9.2 12.7 9C12.6 8.6 12.3 8.40002 11.8 8.40002Z"
    fill="currentColor"/>
</svg>
</span>
                                        <!--end::Svg Icon--> <span
                                            class="text-gray-800 fw-bolder fs-6 d-block">
                                                  <span
                                                      class="badge badge-exclusive badge-light-success fw-semibold fs-8 px-2 py-1 ms-1"
                                                      data-bs-toggle="tooltip" data-bs-placement="left"
                                                      data-bs-original-title="برای جلسات خود لندینگ فروش بسازید و کسب درآمد کنید"
                                                      data-kt-initialized="1">بزودی</span>
                                                پنل فروش بلیط
                                            </span>
                                    </button>
                                </div>

                                <div class="col">
                                    @if(\App\Models\FeatureFlag::isUserAllowed('ai-note-taking', auth()->id()))
                                        <a href="{{route('ai-bot.index')}}" type="button"
                                           class="btn d-flex flex-stack btn-outline btn-bg-light btn-color-gray-600 btn-active-light-success border-dashed border-active px-6 py-7 text-start w-100 min-w-150px">
                                            <!--begin::Svg Icon | path: icons/duotune/files/fil005.svg-->
                                            <span class="svg-icon svg-icon-muted svg-icon-2x">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                     xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M22 12C22 12.2 22 12.5 22 12.7L19.5 10.2L16.9 12.8C16.9 12.5 17 12.3 17 12C17 9.5 15.2 7.50001 12.8 7.10001L10.2 4.5L12.7 2C17.9 2.4 22 6.7 22 12ZM11.2 16.9C8.80001 16.5 7 14.5 7 12C7 11.7 7.00001 11.5 7.10001 11.2L4.5 13.8L2 11.3C2 11.5 2 11.8 2 12C2 17.3 6.09999 21.6 11.3 22L13.8 19.5L11.2 16.9Z"
                                                        fill="currentColor"/>
                                                    <path opacity="0.3"
                                                          d="M22 12.7C21.6 17.9 17.3 22 12 22C11.8 22 11.5 22 11.3 22L13.8 19.5L11.2 16.9C11.5 16.9 11.7 17 12 17C14.5 17 16.5 15.2 16.9 12.8L19.5 10.2L22 12.7ZM10.2 4.5L12.7 2C12.5 2 12.2 2 12 2C6.7 2 2.4 6.1 2 11.3L4.5 13.8L7.10001 11.2C7.50001 8.8 9.5 7 12 7C12.3 7 12.5 7.00001 12.8 7.10001L10.2 4.5Z"
                                                          fill="currentColor"/>
                                                </svg>
                                            </span>
                                            <!--end::Svg Icon-->
                                            <span
                                                class="text-gray-800 fw-bolder fs-6 d-block" dir="rtl">
                                                   یادداشت برداری توسط AI
                                                 <span
                                                     class="badge badge-exclusive badge-light-danger fw-semibold fs-8 px-2 py-1 ms-1"
                                                     data-bs-toggle="tooltip" data-bs-placement="left"
                                                     data-bs-original-title="به رایگان جلسات خود را یادداشت برداری کنید"
                                                     data-kt-initialized="1">رایگان</span>
                                            </span>
                                        </a>
                                    @else
                                        <a href="{{route('feature.waitlist', ['feature' => 'ai-note-taking'])}}" type="button"
                                           class="btn d-flex flex-stack btn-outline btn-bg-light btn-color-gray-600 btn-active-light-warning border-dashed border-active px-6 py-7 text-start w-100 min-w-150px">
                                            <!--begin::Svg Icon | path: icons/duotune/files/fil005.svg-->
                                            <span class="svg-icon svg-icon-muted svg-icon-2x">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                     xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M22 12C22 12.2 22 12.5 22 12.7L19.5 10.2L16.9 12.8C16.9 12.5 17 12.3 17 12C17 9.5 15.2 7.50001 12.8 7.10001L10.2 4.5L12.7 2C17.9 2.4 22 6.7 22 12ZM11.2 16.9C8.80001 16.5 7 14.5 7 12C7 11.7 7.00001 11.5 7.10001 11.2L4.5 13.8L2 11.3C2 11.5 2 11.8 2 12C2 17.3 6.09999 21.6 11.3 22L13.8 19.5L11.2 16.9Z"
                                                        fill="currentColor"/>
                                                    <path opacity="0.3"
                                                          d="M22 12.7C21.6 17.9 17.3 22 12 22C11.8 22 11.5 22 11.3 22L13.8 19.5L11.2 16.9C11.5 16.9 11.7 17 12 17C14.5 17 16.5 15.2 16.9 12.8L19.5 10.2L22 12.7ZM10.2 4.5L12.7 2C12.5 2 12.2 2 12 2C6.7 2 2.4 6.1 2 11.3L4.5 13.8L7.10001 11.2C7.50001 8.8 9.5 7 12 7C12.3 7 12.5 7.00001 12.8 7.10001L10.2 4.5Z"
                                                          fill="currentColor"/>
                                                </svg>
                                            </span>
                                            <!--end::Svg Icon-->
                                            <span
                                                class="text-gray-800 fw-bolder fs-6 d-block" dir="rtl">
                                                   یادداشت برداری توسط AI
                                                 <span
                                                     class="badge badge-exclusive badge-light-warning fw-semibold fs-8 px-2 py-1 ms-1">
                                                     درخواست دسترسی
                                                 </span>
                                            </span>
                                        </a>
                                    @endif
                                </div>
                            </div>
                            <!--end::Row-->
                        </div>
                        <!--end::Stats-->
                    </div>
                    <!--end::Body-->
                </div>
            </div>
            <div class="col-xl-12 mt-0">
                @if(empty(auth()->user()->phone_number))
                    <div class="direction-rtl">
                        <div class="alert alert-danger d-flex align-items-center p-5 mb-10">
                            <i class="ki-duotone ki-shield-tick fs-2hx text-danger me-4"><span
                                    class="path1"></span><span
                                    class="path2"></span></i>
                            <a class="btn btn-outline-dark btn-active-dark m-2 me-4" href="{{route('users.settings')}}">
                                <i class="las la-plus fs-2 me-2"></i> ثبت شماره موبایل
                            </a>
                            <div class="d-flex flex-column">
                                <h4 class="mb-1 text-danger">شما هنوز موبایل خود را وارد نکرده اید.</h4>
                                <span>
                            ما اطلاع رسانی های مهم را از طریق پیامک به شما ارسال میکنیم. پس پیشنهاد میکنیم حتما شماره موبایل خود را ثبت نمایید.
                        </span>
                            </div>
                        </div>
                    </div>
                @endif
                <livewire:meeting.list.room-list/>
            </div>
        </div>
        <!--end::Row-->
        @if(false)
            <!--begin::Row-->
            <div class="row g-0 g-xl-5 g-xxl-8">
                <div class="col-xl-4">
                    <!--begin::Stats Widget 1-->
                    <div class="card card-stretch mb-5 mb-xxl-8">
                        <!--begin::Header-->
                        <div class="card-header align-items-center border-0 mt-5">
                            <h3 class="card-title align-items-start flex-column">
                                <span class="fw-bolder text-dark fs-3">Sales Share</span>
                                <span class="text-muted mt-2 fw-bold fs-6">890,344 Sales</span>
                            </h3>
                            <div class="card-toolbar">
                                <!--begin::Dropdown-->
                                <button type="button"
                                        class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                                        data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                                    <!--begin::Svg Icon | path: icons/duotune/general/gen024.svg-->
                                    <span class="svg-icon svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg"
                                                                           width="24px"
                                                                           height="24px" viewBox="0 0 24 24">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
     <rect x="5" y="5" width="5" height="5" rx="1" fill="#000000"></rect>
        <rect x="14" y="5" width="5" height="5" rx="1" fill="#000000" opacity="0.3"></rect>
        <rect x="5" y="14" width="5" height="5" rx="1" fill="#000000" opacity="0.3"></rect>
        <rect x="14" y="14" width="5" height="5" rx="1" fill="#000000" opacity="0.3"></rect>
    </g>
</svg></span>
                                    <!--end::Svg Icon-->            </button>
                                <!--begin::Menu-->
                                <div
                                    class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold w-200px"
                                    data-kt-menu="true">
                                    <div class="menu-item px-3">
                                        <div class="menu-content fs-6 text-dark fw-bolder px-3 py-4">Manage</div>
                                    </div>

                                    <div class="separator mb-3 opacity-75"></div>

                                    <div class="menu-item px-3">
                                        <a href="#" class="menu-link px-3">
                                            Add User
                                        </a>
                                    </div>

                                    <div class="menu-item px-3">
                                        <a href="#" class="menu-link px-3">
                                            Add Role
                                        </a>
                                    </div>

                                    <div class="menu-item px-3" data-kt-menu-trigger="hover"
                                         data-kt-menu-placement="right-start" data-kt-menu-flip="left-start, top">
                                        <a href="#" class="menu-link px-3">
                                            <span class="menu-title">Add Group</span>
                                            <span class="menu-arrow"></span>
                                        </a>

                                        <div class="menu-sub menu-sub-dropdown w-200px py-4">
                                            <div class="menu-item px-3">
                                                <a href="#" class="menu-link px-3">
                                                    Admin Group
                                                </a>
                                            </div>

                                            <div class="menu-item px-3">
                                                <a href="#" class="menu-link px-3">
                                                    Staff Group
                                                </a>
                                            </div>

                                            <div class="menu-item px-3">
                                                <a href="#" class="menu-link px-3">
                                                    Member Group
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="menu-item px-3">
                                        <a href="#" class="menu-link px-3">
                                            Reports
                                        </a>
                                    </div>

                                    <div class="separator mt-3 opacity-75"></div>

                                    <div class="menu-item px-3">
                                        <div class="menu-content px-3 py-3">
                                            <a class="btn btn-primary fw-bold btn-sm px-4" href="#">
                                                Create New
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <!--end::Menu-->
                                <!--end::Dropdown-->
                            </div>
                        </div>
                        <!--end::Header-->

                        <!--begin::Body-->
                        <div class="card-body pt-12">
                            <!--begin::Chart-->
                            <div
                                class="d-flex flex-center position-relative bgi-no-repeat bgi-size-contain bgi-position-x-center bgi-position-y-center h-175px"
                                style="background-image:url('/start/assets/media/svg/illustrations/bg-1.svg')">
                                <div class="fw-bolder fs-1 text-gray-800 position-absolute">8,345</div>
                                <canvas id="kt_stats_widget_1_chart" width="344" height="175"
                                        style="display: block; box-sizing: border-box; height: 175px; width: 344px;"></canvas>
                            </div>
                            <!--end::Chart-->

                            <!--begin::Items-->
                            <div class="d-flex justify-content-around pt-18">
                                <!--begin::Item-->
                                <div class="">
                                    <span class="fw-bolder text-gray-800">48% SNT</span>
                                    <span class="bg-info w-25px h-5px d-block rounded mt-1"></span>
                                </div>
                                <!--end::Item-->

                                <!--begin::Item-->
                                <div class="">
                                    <span class="fw-bolder text-gray-800">20% REX</span>
                                    <span class="bg-primary w-25px h-5px d-block rounded mt-1"></span>
                                </div>
                                <!--end::Item-->

                                <!--begin::Item-->
                                <div class="">
                                    <span class="fw-bolder text-gray-800">32% SAP</span>
                                    <span class="bg-warning w-25px h-5px d-block rounded mt-1"></span>
                                </div>
                                <!--end::Item-->
                            </div>
                            <!--end::Items-->
                        </div>
                        <!--end: Card Body-->
                    </div>
                    <!--end::Stats Widget 1-->
                </div>

            </div>
            <!--end::Row-->
        @endif
        <!--begin::Modals-->

        <!--begin::Modal - Select Location-->
        <div class="modal fade" id="kt_modal_select_location" tabindex="-1" aria-hidden="true">
            <!--begin::Modal dialog-->
            <div class="modal-dialog mw-1000px">
                <!--begin::Modal content-->
                <div class="modal-content">
                    <!--begin::Modal header-->
                    <div class="modal-header">
                        <!--begin::Title-->
                        <h2>Select Location</h2>
                        <!--end::Title-->

                        <!--begin::Close-->
                        <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
                            <span class="svg-icon svg-icon-1"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                                                   height="24" viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)"
      fill="black"></rect>
<rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
</svg></span>
                            <!--end::Svg Icon-->                </div>
                        <!--end::Close-->
                    </div>
                    <!--end::Modal header-->

                    <!--begin::Modal body-->
                    <div class="modal-body">
                        <div id="kt_modal_select_location_map" class="w-100 rounded" style="height:450px"></div>
                    </div>
                    <!--end::Modal body-->

                    <!--begin::Modal footer-->
                    <div class="modal-footer d-flex justify-content-end">
                        <a href="#" class="btn btn-active-light me-5" data-bs-dismiss="modal">Cancel</a>

                        <button type="button" id="kt_modal_select_location_button" class="btn btn-primary"
                                data-bs-dismiss="modal">Apply
                        </button>
                    </div>
                    <!--end::Modal footer-->
                </div>
                <!--end::Modal content-->
            </div>
            <!--end::Modal dialog-->
        </div>
        <!--end::Modal - Select Location--><!--end::Modals--></div>
@endsection

@push('scripts')

    <script>
        // Select elements
        // const target = document.getElementById('kt_clipboard_4');
        // const button = target.nextElementSibling;

        clipboard = new ClipboardJS('.kt_clipboard_4', {
            target: function (trigger) {
                return trigger.nextElementSibling;
            },
            text: function (trigger) {
                return trigger.getAttribute('aria-label');
            },
        });
        var button = null;
        $('.kt_clipboard_').on('click', function () {
            button = this;
        })

        // var clipboard = new ClipboardJS('.kt_clipboard_4');

        // Success action handler
        clipboard.on('success', function (e) {
            console.log(clipboard.target)
            let target = clipboard.target;
            let button = e.trigger;
            var checkIcon = button.querySelector('.bi.bi-check');
            var svgIcon = button.querySelector('.svg-icon');

            // Exit check icon when already showing
            if (checkIcon) {
                return;
            }

            // Create check icon
            checkIcon = document.createElement('i');
            checkIcon.classList.add('bi');
            checkIcon.classList.add('bi-check');
            checkIcon.classList.add('fs-2x');

            // Append check icon
            button.appendChild(checkIcon);

            // Highlight target
            const classes = ['text-success', 'fw-boldest'];
            // target.classList.add(...classes);

            // Highlight button
            button.classList.add('btn-success');

            // Hide copy icon
            svgIcon.classList.add('d-none');

            // Revert button label after 3 seconds
            setTimeout(function () {
                // Remove check icon
                svgIcon.classList.remove('d-none');

                // Revert icon
                button.removeChild(checkIcon);

                // Remove target highlight
                // target.classList.remove(...classes);

                // Remove button highlight
                button.classList.remove('btn-success');
            }, 3000)
        });
    </script>

@endpush
