@extends('layouts.base')

@section('title')
    Meeting Audit Reports
@endsection

@section('content')
    <div class="container-xxl">
        <div class="toolbar direction-rtl" id="kt_toolbar">
            <div class="d-flex flex-stack flex-wrap flex-sm-nowrap  overflow-auto  flex-sm-row flex-row">
                <!--begin::Info-->
                <div class="d-flex flex-column align-items-start justify-content-center flex-wrap me-1">
                    <!--begin::Title-->
                    <h3 class="text-dark fw-bolder my-1">
                        گزارشات اتاق {{$roomTitle}}
                    </h3>
                    <!--end::Title-->

                    <!--begin::Breadcrumb-->
                    <ul class="breadcrumb breadcrumb-line bg-transparent text-muted fw-bold p-0 my-1 fs-7">
                        <li class="breadcrumb-item">
                            <a href="{{route('dashboard')}}" class="text-muted text-hover-primary">
                                داشبورد </a>
                        </li>
                        <li class="breadcrumb-item">
                            اتاق ها
                        </li>
                        <li class="breadcrumb-item text-dark">
                            فایل ها
                        </li>
                    </ul>
                    <!--end::Breadcrumb-->
                </div>
                <!--end::Info-->

                <!--begin::Nav-->
                <div class="d-flex align-items-center flex-nowrap text-nowrap overflow-auto py-1">
                    <a href="{{route('room.users',[$meeting->meeting_id])}}"
                       class="btn btn-active-accent  fw-bolder ms-3">مدیریت کاربران</a>
                    <a href="{{route('room.index',[$meeting->meeting_id])}}"
                       class="btn btn-active-accent  fw-bolder ms-3">تنظیمات اتاق </a>
                    <a href="{{route('room.files',[$meeting->meeting_id])}}"
                       class="btn btn-active-accent  fw-bolder ms-3">مدیریت فایل ها</a>
                    <a href="{{route('room.audits',[$meeting->meeting_id])}}"
                       class="btn btn-active-accent active  fw-bolder ms-3">گزارشات</a>
                </div>
                <!--end::Nav-->
            </div>
        </div>

        <div class="card mb-5">
            <div class="card-header direction-rtl">
                <h3 class="card-title text-dark">خلاصه گزارش</h3>

            </div>
            <div class="card-body">
                <!-- Summary Stats -->
                <div class="row g-5 g-xl-8 mb-5">
                    <div class="col-xl-3">
                        <div class="card ">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="symbol symbol-40px ms-3">
                                    <span class="symbol-label bg-gray-200">
                                        <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                xmlns="http://www.w3.org/2000/svg">
<path
    d="M16.0173 9H15.3945C14.2833 9 13.263 9.61425 12.7431 10.5963L12.154 11.7091C12.0645 11.8781 12.1072 12.0868 12.2559 12.2071L12.6402 12.5183C13.2631 13.0225 13.7556 13.6691 14.0764 14.4035L14.2321 14.7601C14.2957 14.9058 14.4396 15 14.5987 15H18.6747C19.7297 15 20.4057 13.8774 19.912 12.945L18.6686 10.5963C18.1487 9.61425 17.1285 9 16.0173 9Z"
    fill="currentColor"/>
<rect opacity="0.3" x="14" y="4" width="4" height="4" rx="2" fill="currentColor"/>
<path
    d="M4.65486 14.8559C5.40389 13.1224 7.11161 12 9 12C10.8884 12 12.5961 13.1224 13.3451 14.8559L14.793 18.2067C15.3636 19.5271 14.3955 21 12.9571 21H5.04292C3.60453 21 2.63644 19.5271 3.20698 18.2067L4.65486 14.8559Z"
    fill="currentColor"/>
<rect opacity="0.3" x="6" y="5" width="6" height="6" rx="3" fill="currentColor"/>
</svg>
</span>
                                    </span>
                                    </div>
                                    <div>
                                        <div
                                            class="text-gray-800 fs-4 fw-bold ">{{ $reportEventsUsers->unique('identifier')->count() }}</div>
                                        <div class="fs-7 text-gray-800 ">Participants</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3">
                        <div class="card ">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="symbol symbol-40px ms-3">
                                    <span class="symbol-label bg-gray-200">
                                       <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24" height="24"
                                                                                               viewBox="0 0 24 24"
                                                                                               fill="none"
                                                                                               xmlns="http://www.w3.org/2000/svg">
<path
    d="M12.6 7C12 7 11.6 6.6 11.6 6V3C11.6 2.4 12 2 12.6 2C13.2 2 13.6 2.4 13.6 3V6C13.6 6.6 13.2 7 12.6 7ZM10 7.59998C10.5 7.29998 10.6 6.69995 10.4 6.19995L9 3.80005C8.7 3.30005 8.10001 3.20002 7.60001 3.40002C7.10001 3.70002 7.00001 4.30005 7.20001 4.80005L8.60001 7.19995C8.80001 7.49995 9.1 7.69995 9.5 7.69995C9.7 7.69995 9.9 7.69998 10 7.59998ZM8 9.30005C8.3 8.80005 8.10001 8.20002 7.60001 7.90002L5.5 6.69995C5 6.39995 4.40001 6.59998 4.10001 7.09998C3.80001 7.59998 4 8.2 4.5 8.5L6.60001 9.69995C6.80001 9.79995 6.90001 9.80005 7.10001 9.80005C7.50001 9.80005 7.9 9.70005 8 9.30005ZM7.20001 12C7.20001 11.4 6.80001 11 6.20001 11H4C3.4 11 3 11.4 3 12C3 12.6 3.4 13 4 13H6.20001C6.70001 13 7.20001 12.6 7.20001 12Z"
    fill="currentColor"/>
<path opacity="0.3"
      d="M17.4 5.5C17.4 6.1 17 6.5 16.4 6.5C15.8 6.5 15.4 6.1 15.4 5.5C15.4 4.9 15.8 4.5 16.4 4.5C17 4.5 17.4 5 17.4 5.5ZM5.80001 17.1L7.40001 16.1C7.90001 15.8 8.00001 15.2 7.80001 14.7C7.50001 14.2 6.90001 14.1 6.40001 14.3L4.80001 15.3C4.30001 15.6 4.20001 16.2 4.40001 16.7C4.60001 17 4.90001 17.2 5.30001 17.2C5.50001 17.3 5.60001 17.2 5.80001 17.1ZM8.40001 20.2C8.20001 20.2 8.10001 20.2 7.90001 20.1C7.40001 19.8 7.3 19.2 7.5 18.7L8.30001 17.3C8.60001 16.8 9.20002 16.7 9.70002 16.9C10.2 17.2 10.3 17.8 10.1 18.3L9.30001 19.7C9.10001 20 8.70001 20.2 8.40001 20.2ZM12.6 21.2C12 21.2 11.6 20.8 11.6 20.2V18.8C11.6 18.2 12 17.8 12.6 17.8C13.2 17.8 13.6 18.2 13.6 18.8V20.2C13.6 20.7 13.2 21.2 12.6 21.2ZM16.7 19.9C16.4 19.9 16 19.7 15.8 19.4L15.2 18.5C14.9 18 15.1 17.4 15.6 17.1C16.1 16.8 16.7 17 17 17.5L17.6 18.4C17.9 18.9 17.7 19.5 17.2 19.8C17 19.9 16.8 19.9 16.7 19.9ZM19.4 17C19.2 17 19.1 17 18.9 16.9L18.2 16.5C17.7 16.2 17.6 15.6 17.8 15.1C18.1 14.6 18.7 14.5 19.2 14.7L19.9 15.1C20.4 15.4 20.5 16 20.3 16.5C20.1 16.8 19.8 17 19.4 17ZM20.4 13H19.9C19.3 13 18.9 12.6 18.9 12C18.9 11.4 19.3 11 19.9 11H20.4C21 11 21.4 11.4 21.4 12C21.4 12.6 20.9 13 20.4 13ZM18.9 9.30005C18.6 9.30005 18.2 9.10005 18 8.80005C17.7 8.30005 17.9 7.70002 18.4 7.40002L18.6 7.30005C19.1 7.00005 19.7 7.19995 20 7.69995C20.3 8.19995 20.1 8.79998 19.6 9.09998L19.4 9.19995C19.3 9.19995 19.1 9.30005 18.9 9.30005Z"
      fill="currentColor"/>
</svg>
</span>
                                    </span>
                                    </div>
                                    <div>
                                        <div class="text-gray-800 fs-4 fw-bold ">
                                            {{ round($reportEventsUsers->max('duration_seconds')/60) }}
                                            min
                                        </div>
                                        <div class="fs-7  text-gray-800 ">Max Duration</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="symbol symbol-40px ms-3">
                                    <span class="symbol-label bg-gray-200">
                                       <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24" height="24"
                                                                                               viewBox="0 0 24 24"
                                                                                               fill="none"
                                                                                               xmlns="http://www.w3.org/2000/svg">
<path d="M2 16C2 16.6 2.4 17 3 17H21C21.6 17 22 16.6 22 16V15H2V16Z" fill="currentColor"/>
<path opacity="0.3" d="M21 3H3C2.4 3 2 3.4 2 4V15H22V4C22 3.4 21.6 3 21 3Z" fill="currentColor"/>
<path opacity="0.3" d="M15 17H9V20H15V17Z" fill="currentColor"/>
</svg>
</span>
                                    </span>
                                    </div>
                                    <div>
                                        <div
                                            class="text-gray-800 fs-4 fw-bold ">{{ $reportEventsUsers->unique('device_type')->count() }}</div>
                                        <div class="fs-7 text-dark">Device Types</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3">
                        <div class="card  mb-5  mb-xxl-8">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="symbol symbol-40px ms-3">
                                    <span class="symbol-label bg-gray-200">
                                        <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                xmlns="http://www.w3.org/2000/svg">
<path
    d="M21 6.30005C20.5 5.30005 19.9 5.19998 18.7 5.09998C17.5 4.99998 14.5 5 11.9 5C9.29999 5 6.29998 4.99998 5.09998 5.09998C3.89998 5.19998 3.29999 5.30005 2.79999 6.30005C2.19999 7.30005 2 8.90002 2 11.9C2 14.8 2.29999 16.5 2.79999 17.5C3.29999 18.5 3.89998 18.6001 5.09998 18.7001C6.29998 18.8001 9.29999 18.8 11.9 18.8C14.5 18.8 17.5 18.8001 18.7 18.7001C19.9 18.6001 20.5 18.4 21 17.5C21.6 16.5 21.8 14.9 21.8 11.9C21.8 9.00002 21.5 7.30005 21 6.30005ZM9.89999 15.7001V8.20007L14.5 11C15.3 11.5 15.3 12.5 14.5 13L9.89999 15.7001Z"
    fill="currentColor"/>
</svg>
</span>
                                    </span>
                                    </div>
                                    <div>
                                        <div
                                            class="text-gray-800 fs-4 fw-bold ">{{  round(($reportEventsUsers->sum('video_send_seconds') + $reportEventsUsers->sum('video_recv_seconds'))/3600, 1) }}</div>
                                        <div class="fs-7  text-gray-800 text-bold">Video hours share</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-5 g-xl-12 mt-5">
            <div class="col-xl-12">
                <div class="card mb-5">
                    <div class="card-header direction-rtl">
                        <h3 class="card-title">تعداد شرکت کنندگان در طول زمان</h3>
                    </div>
                    <div class="card-body">
                        <div id="participantCountChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>
        </div>


        <div class="row g-5 g-xl-12">
            <div class="col-xl-12">
                <div class="card mb-5">
                    <div class="card-header direction-rtl">
                        <h3 class="card-title">میزان حضور شرکت کنندگان</h3>

                    </div>
                    <div class="card-body">
                        <div id="participationChart" style="height: 950px;"></div>
                    </div>
                </div>
            </div>
        </div>


        <div class="row g-5 g-xl-6">
            <div class="col-xl-6">
                <div class="card mb-5">
                    <div class="card-header direction-rtl">
                        <h3 class="card-title text-dark">تنوع دستگاه ها</h3>

                    </div>
                    <div class="card-body">
                        <div id="deviceTypeChart" style="height: 350px;"></div>
                    </div>

                </div>
            </div>
            <div class="col-xl-6">
                <div class="card mb-5">
                    <div class="card-header direction-rtl">
                        <h3 class="card-title text-dark">تعامل شرکت کنندگان</h3>

                    </div>
                    <div class="card-body">
                        <div id="engagementChart" style="height: 350px;"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card mb-5">
            <div class="card-header direction-rtl">
                <h3 class="card-title text-dark">جدول اقدامات</h3>

            </div>
            <div class="card-body">
                <!-- Raw Data Table -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Raw Event Data</h3>
                        <div class="card-toolbar">
                            <button type="button" class="btn btn-sm btn-light-primary me-2" id="printRawData">
                                <i class="fas fa-print"></i> Print
                            </button>
                            <button type="button" class="btn btn-sm btn-light-success" id="exportExcel">
                                <i class="fas fa-file-excel"></i> Export Excel
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4"
                                   id="rawDataTable">
                                <thead>
                                <tr class="fw-bolder text-muted">
                                    <th>User</th>
                                    <th>Identifier</th>
                                    <th>event</th>
                                    <th>Device</th>
                                    <th>Duration</th>
                                    <th>Video Send</th>
                                    <th>Video Receive</th>
                                    <th>Audio Send</th>
                                    <th>Audio Receive</th>
                                    <th>Screen Send</th>
                                    <th>Screen Receive</th>
                                    <th>Network</th>
                                    <th>Start Time</th>
                                    <th>End Time</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($reportEvents as $event)
                                    <tr>
                                        <td>{{ $event->display_name ?? $event->actor_email ?? 'Unknown' }}</td>
                                        <td>{{ $event->identifier ?? 'N/A' }}</td>
                                        <td>{{ $event->event_name ?? 'N/A' }}</td>
                                        <td>{{ $event->device_type ?? 'Unknown' }}</td>
                                        <td>{{ round($event->duration_seconds/60, 1) }} min</td>
                                        <td>{{ round($event->video_send_seconds/60, 1) }} min</td>
                                        <td>{{ round($event->video_recv_seconds/60, 1) }} min</td>
                                        <td>{{ round($event->audio_send_seconds/60, 1) }} min</td>
                                        <td>{{ round($event->audio_recv_seconds/60, 1) }} min</td>
                                        <td>{{ round($event->screencast_send_seconds/60, 1) }} min</td>
                                        <td>{{ round($event->screencast_recv_seconds/60, 1) }} min</td>
                                        <td>{{ $event->network_estimated_download_kbps_mean ?? 0 }} kbps</td>
                                        <td>{{ \Morilog\Jalali\Jalalian::fromDateTime($event->start_time)->format('Y-m-d H:i:s') ?? ' '}}</td>
                                        <td>{{ \Morilog\Jalali\Jalalian::fromDateTime($event->end_time)->format('Y-m-d H:i:s') ?? ' '}}</td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
@endsection

@push('header_style_after')
    <!-- DataTables CSS -->
    <link href="{{ asset('assets/plugins/datatable/datatables.css') }}" rel="stylesheet" type="text/css"/>
    <link href="{{ asset('assets/plugins/datatable/DataTables-1.10.24/css/dataTables.bootstrap4.min.css') }}"
          rel="stylesheet" type="text/css"/>
    <link href="{{ asset('assets/plugins/datatable/Buttons-1.7.0/css/buttons.bootstrap4.min.css') }}" rel="stylesheet"
          type="text/css"/>

    <style>
        div#rawDataTable_wrapper > div {
            width: 100% !important;
            overflow: auto;
        }

        div#rawDataTable_length {
            display: flex;
            justify-content: space-between;
        }

        div#rawDataTable_length > label {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-direction: row-reverse;
        }

        div#rawDataTable_filter label {
            display: flex;
            align-items: center;
            gap: 11px;
            direction: ltr;
        }

        div#rawDataTable_wrapper {
            direction: rtl !important;
        }

        div#rawDataTable_paginate {
            display: flex;
            justify-content: end;
        }

        .card-toolbar {
            gap: 14px;
        }
    </style>
@endpush

@push('scripts')
    <!-- ApexCharts for visualization -->
    <script src="{{ asset('assets/js/apexcharts.js') }}"></script>

    <!-- DataTables JS -->
    <script src="{{ asset('assets/plugins/datatable/DataTables-1.10.24/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('assets/plugins/datatable/DataTables-1.10.24/js/dataTables.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('assets/plugins/datatable/Buttons-1.7.0/js/dataTables.buttons.min.js') }}"></script>
    <script src="{{ asset('assets/plugins/datatable/Buttons-1.7.0/js/buttons.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('assets/plugins/datatable/JSZip-2.5.0/jszip.min.js') }}"></script>
    <script src="{{ asset('assets/plugins/datatable/Buttons-1.7.0/js/buttons.html5.min.js') }}"></script>
    <script src="{{ asset('assets/plugins/datatable/Buttons-1.7.0/js/buttons.print.min.js') }}"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Get data from PHP - with proper JSON encoding
            let reportEvents;
            let reportEventsUsers;
            try {
                reportEvents = JSON.parse('{!! addslashes(json_encode($reportEvents)) !!}');
                reportEventsUsers = JSON.parse('{!! addslashes(json_encode($reportEventsUsers)) !!}');

            } catch (e) {
                console.error('Error parsing JSON data:', e);
                reportEvents = [];
            }

            // Render all charts
            renderParticipationChart(reportEventsUsers);
            renderParticipantCountChart(reportEventsUsers);
            renderDeviceTypeChart(reportEventsUsers);
            renderEngagementChart(reportEventsUsers);

            // Initialize DataTable for raw data
            if ($.fn.DataTable) {
                $('#rawDataTable').DataTable({
                    paging: true,
                    ordering: true,
                    info: true,
                    searching: true,
                    responsive: true
                });
            }

            // Print and Excel export buttons setup
            setupExportButtons(reportEvents);
        });

        function setupExportButtons(reportEvents) {
            // Print button - direct implementation
            document.getElementById('printRawData').addEventListener('click', function () {
                const printWindow = window.open('', '_blank');
                printWindow.document.write('<title>IROOM.LIVE - Meeting Audit Report - Raw Data</title>');
                printWindow.document.write('<style>body { font-size: 10pt; } table { width: 100%; border-collapse: collapse; } th, td { padding: 8px; border: 1px solid #ddd; } th { background-color: #f2f2f2; }</style>');
                printWindow.document.write('</h ead > <body>');
                printWindow.document.write('<h1>IROOM.LIVE - Meeting Audit Report - Raw Data</h1>');
                printWindow.document.write('<table border="1">');

                // Add headers
                printWindow.document.write('<thead><tr>');
                const headers = [
                    'User', 'Identifier', 'Event Name', 'Device', 'Duration (min)',
                    'Video Send (min)', 'Video Receive (min)',
                    'Audio Send (min)', 'Audio Receive (min)',
                    'Screen Send (min)', 'Screen Receive (min)',
                    'Network (kbps)',
                    'Start Time',
                    'End Time'
                ];
                headers.forEach(header => {
                    printWindow.document.write(`<th>${header}</th>`);
                });
                printWindow.document.write('</tr></thead>');

                // Add data rows
                printWindow.document.write('<tbody>');
                reportEvents.forEach(event => {
                    printWindow.document.write('<tr>');
                    printWindow.document.write(`<td>${event.display_name || event.actor_email || 'Unknown'}</td>`);
                    printWindow.document.write(`<td>${event.identifier || 'N/A'}</td>`);
                    printWindow.document.write(`<td>${event.event_name || 'N/A'}</td>`);
                    printWindow.document.write(`<td>${event.device_type || 'Unknown'}</td>`);
                    printWindow.document.write(`<td>${Math.round((event.duration_seconds || 0) / 60 * 10) / 10}</td>`);
                    printWindow.document.write(`<td>${Math.round((event.video_send_seconds || 0) / 60 * 10) / 10}</td>`);
                    printWindow.document.write(`<td>${Math.round((event.video_recv_seconds || 0) / 60 * 10) / 10}</td>`);
                    printWindow.document.write(`<td>${Math.round((event.audio_send_seconds || 0) / 60 * 10) / 10}</td>`);
                    printWindow.document.write(`<td>${Math.round((event.audio_recv_seconds || 0) / 60 * 10) / 10}</td>`);
                    printWindow.document.write(`<td>${Math.round((event.screencast_send_seconds || 0) / 60 * 10) / 10}</td>`);
                    printWindow.document.write(`<td>${Math.round((event.screencast_recv_seconds || 0) / 60 * 10) / 10}</td>`);
                    printWindow.document.write(`<td>${event.network_estimated_download_kbps_mean || 0}</td>`);
                    printWindow.document.write(`<td>${event.start_time || 0}</td>`);
                    printWindow.document.write(`<td>${event.end_time || 0}</td>`);
                    printWindow.document.write('</tr>');
                });
                printWindow.document.write('</tbody></table>');
                printWindow.document.write('</body></html>');
                printWindow.document.close();
                printWindow.focus();
                setTimeout(function () {
                    printWindow.print();
                    printWindow.close();
                }, 1000);
            });

            // Excel export button - using direct CSV generation
            document.getElementById('exportExcel').addEventListener('click', function () {
                try {
                    // Create CSV content
                    let csvContent = "data:text/csv;charset=utf-8,";

                    // Add headers
                    const headers = [
                        'User', 'Identifier', 'Device', 'Duration (min)',
                        'Video Send (min)', 'Video Receive (min)',
                        'Audio Send (min)', 'Audio Receive (min)',
                        'Screen Send (min)', 'Screen Receive (min)',
                        'Network (kbps)',
                        'Start Time',
                        'End Time'
                    ];
                    csvContent += headers.join(',') + "\r\n";

                    // Add data rows
                    reportEvents.forEach(event => {
                        const row = [
                            csvEscape(event.display_name || event.actor_email || 'Unknown'),
                            csvEscape(event.identifier || 'N/A'),
                            csvEscape(event.device_type || 'Unknown'),
                            Math.round((event.duration_seconds || 0) / 60 * 10) / 10,
                            Math.round((event.video_send_seconds || 0) / 60 * 10) / 10,
                            Math.round((event.video_recv_seconds || 0) / 60 * 10) / 10,
                            Math.round((event.audio_send_seconds || 0) / 60 * 10) / 10,
                            Math.round((event.audio_recv_seconds || 0) / 60 * 10) / 10,
                            Math.round((event.screencast_send_seconds || 0) / 60 * 10) / 10,
                            Math.round((event.screencast_recv_seconds || 0) / 60 * 10) / 10,
                            event.network_estimated_download_kbps_mean || 0,
                            event.start_time || 0,
                            event.end_time || 0
                        ];
                        csvContent += row.join(',') + "\r\n";
                    });

                    // Create download link
                    const encodedUri = encodeURI(csvContent);
                    const link = document.createElement("a");
                    link.setAttribute("href", encodedUri);
                    link.setAttribute("download", "Meeting_Audit_Report.csv");
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } catch (error) {
                    console.error('Excel export error:', error);
                    alert('An error occurred during Excel export. Please check the console for details.');
                }
            });
        }

        // Helper function to escape CSV values
        function csvEscape(value) {
            const str = String(value || '');
            if (str.includes(',') || str.includes('"') || str.includes('\n')) {
                return '"' + str.replace(/"/g, '""') + '"';
            }
            return str;
        }

        function renderParticipationChart(data) {
            // Prepare data for chart
            const participationData = [];
            const userSessions = {};
            const userTotalDurations = {};

            // Group sessions by user
            data.forEach(item => {
                const user = item.display_name || item.actor_email || 'Unknown';

                // Initialize user arrays if they don't exist
                if (!userSessions[user]) {
                    userSessions[user] = [];
                    userTotalDurations[user] = 0;
                }

                // Only add sessions with valid start and end times
                if (item.start_time && item.end_time) {
                    const startTime = new Date(item.start_time).getTime();
                    const endTime = new Date(item.end_time).getTime();

                    // Only add if end time is after start time
                    if (endTime > startTime) {
                        userSessions[user].push({
                            startTime: startTime,
                            endTime: endTime,
                            duration: item.duration_seconds || 0,
                            device: item.device_type || 'Unknown'
                        });

                        // Add to total duration
                        userTotalDurations[user] += (item.duration_seconds || 0);
                    }
                }
            });

            // Modern color palette
            const colorPalette = {
                high: '#3B82F6',    // Modern blue
                medium: '#8B5CF6',  // Modern purple
                low: '#EC4899'      // Modern pink
            };

            // Get all durations to calculate engagement levels
            const allDurations = Object.values(userTotalDurations);
            const maxDuration = Math.max(...allDurations);

            // Define engagement thresholds (33% and 66% of max duration)
            const lowThreshold = maxDuration * 0.33;
            const mediumThreshold = maxDuration * 0.66;

            // Process each user's sessions
            Object.keys(userSessions).forEach(user => {
                const sessions = userSessions[user];
                const totalDuration = userTotalDurations[user];
                const durationMinutes = Math.round(totalDuration / 60);

                // Determine engagement level and color based on total duration
                let engagementLevel, color;

                if (totalDuration < lowThreshold) {
                    engagementLevel = 'Low';
                    color = colorPalette.low;
                } else if (totalDuration < mediumThreshold) {
                    engagementLevel = 'Medium';
                    color = colorPalette.medium;
                } else {
                    engagementLevel = 'High';
                    color = colorPalette.high;
                }

                // Create a data entry for each session
                sessions.forEach((session, index) => {
                    participationData.push({
                        x: user + ` (${durationMinutes} min)`,
                        y: [session.startTime, session.endTime],
                        duration: durationMinutes,
                        sessionDuration: Math.round(session.duration / 60),
                        device: session.device,
                        engagementLevel: engagementLevel,
                        color: color,
                        fillColor: color, // Add explicit fillColor property
                        // Store the total number of sessions for this user
                        totalSessions: sessions.length,
                        // Store the session index for reference
                        sessionIndex: index + 1
                    });
                });
            });

            // Sort by total duration (descending) and keep sessions for the same user together
            participationData.sort((a, b) => {
                // First compare by user name
                if (a.x !== b.x) {
                    // Extract duration from the label
                    const durationA = parseInt(a.x.match(/\((\d+) min\)/)[1]);
                    const durationB = parseInt(b.x.match(/\((\d+) min\)/)[1]);
                    return durationB - durationA;
                }
                // If same user, sort by start time
                return a.y[0] - b.y[0];
            });

            // Calculate dynamic height based on number of unique users
            const uniqueUsers = new Set(participationData.map(item => item.x));
            const participantCount = uniqueUsers.size;
            const chartHeight = Math.max(350, participantCount * 40);

            // Update the chart container height
            document.querySelector("#participationChart").style.height = chartHeight + 'px';

            const options = {
                series: [{
                    name: 'Participation',
                    data: participationData
                }],
                chart: {
                    height: chartHeight,
                    type: 'rangeBar',
                    fontFamily: 'inherit',
                    toolbar: {
                        show: true,
                        tools: {
                            download: true,
                            selection: true,
                            zoom: true,
                            zoomin: true,
                            zoomout: true,
                            pan: true,
                            reset: true
                        }
                    },
                    animations: {
                        enabled: true,
                        easing: 'easeinout',
                        speed: 800,
                        animateGradually: {
                            enabled: true,
                            delay: 150
                        },
                        dynamicAnimation: {
                            enabled: true,
                            speed: 350
                        }
                    },
                    background: '#F8FAFC',
                    dropShadow: {
                        enabled: true,
                        top: 3,
                        left: 2,
                        blur: 4,
                        opacity: 0.1
                    }
                },
                plotOptions: {
                    bar: {
                        horizontal: true,
                        barHeight: '70%',
                        rangeBarGroupRows: true,
                        borderRadius: 8
                    }
                },
                colors: ['#3B82F6', '#8B5CF6', '#EC4899'], // Default colors, will be overridden
                fill: {
                    type: 'gradient',
                    gradient: {
                        shade: 'light',
                        type: 'horizontal',
                        shadeIntensity: 0.25,
                        inverseColors: false,
                        opacityFrom: 0.85,
                        opacityTo: 0.85
                    }
                },
                states: {
                    hover: {
                        filter: {
                            type: 'lighten',
                            value: 0.1
                        }
                    },
                    active: {
                        allowMultipleDataPointsSelection: false,
                        filter: {
                            type: 'darken',
                            value: 0.35
                        }
                    }
                },
                xaxis: {
                    type: 'datetime',
                    labels: {
                        formatter: function (val) {
                            return new Date(val).toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'});
                        },
                        style: {
                            colors: '#64748B',
                            fontSize: '12px',
                            fontFamily: 'inherit',
                            fontWeight: 400
                        }
                    },
                    axisBorder: {
                        show: true,
                        color: '#E2E8F0'
                    },
                    axisTicks: {
                        show: true,
                        color: '#E2E8F0'
                    }
                },
                yaxis: {
                    labels: {
                        style: {
                            colors: '#334155',
                            fontSize: '12px',
                            fontFamily: 'inherit',
                            fontWeight: 500
                        }
                    }
                },
                grid: {
                    borderColor: '#F1F5F9',
                    strokeDashArray: 4,
                    xaxis: {
                        lines: {
                            show: true
                        }
                    },
                    yaxis: {
                        lines: {
                            show: false
                        }
                    },
                    padding: {
                        top: 0,
                        right: 0,
                        bottom: 0,
                        left: 10
                    }
                },
                tooltip: {
                    theme: 'light',
                    style: {
                        fontSize: '12px',
                        fontFamily: 'inherit'
                    },
                    x: {
                        format: 'HH:mm:ss'
                    },
                    custom: function ({series, seriesIndex, dataPointIndex, w}) {
                        const data = w.config.series[seriesIndex].data[dataPointIndex];
                        const sessionInfo = data.totalSessions > 1 ?
                            `<div style="display: flex; align-items: center; margin-bottom: 4px;">
                                <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background: ${data.color}; margin-right: 6px;"></span>
                                <span style="color: #475569;">Session: <b>${data.sessionIndex} of ${data.totalSessions}</b></span>
                            </div>
                            <div style="display: flex; align-items: center; margin-bottom: 4px;">
                                <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background: ${data.color}; margin-right: 6px;"></span>
                                <span style="color: #475569;">Session Duration: <b>${data.sessionDuration} minutes</b></span>
                            </div>` : '';

                        return `
                        <div class="apexcharts-tooltip-box" style="padding: 8px; border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                            <div style="font-weight: 600; margin-bottom: 6px; color: #1E293B;">${data.x.split(' (')[0]}</div>
                            ${sessionInfo}
                            <div style="display: flex; align-items: center; margin-bottom: 4px;">
                                <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background: ${data.color}; margin-right: 6px;"></span>
                                <span style="color: #475569;">Total Duration: <b>${data.duration} minutes</b></span>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background: ${data.color}; margin-right: 6px;"></span>
                                <span style="color: #475569;">Engagement: <b>${data.engagementLevel}</b></span>
                            </div>
                        </div>`;
                    }
                },
                legend: {
                    show: true,
                    showForSingleSeries: true,
                    customLegendItems: ['High Engagement', 'Medium Engagement', 'Low Engagement'],
                    markers: {
                        fillColors: [colorPalette.high, colorPalette.medium, colorPalette.low],
                        radius: 6,
                        offsetX: 0,
                        offsetY: 0
                    },
                    position: 'top',
                    horizontalAlign: 'left',
                    floating: false,
                    fontSize: '13px',
                    fontFamily: 'inherit',
                    fontWeight: 500,
                    labels: {
                        colors: '#334155'
                    },
                    itemMargin: {
                        horizontal: 20,
                        vertical: 20
                    }
                }
            };

            // Create the chart
            const chart = new ApexCharts(document.querySelector("#participationChart"), options);

            // Override the default color function to use our custom colors
            chart.beforeMount = function () {
                chart.w.globals.seriesRangeBarTimeline = participationData.map(item => {
                    return {
                        ...item,
                        fillColor: item.color
                    };
                });
            };

            chart.render();
        }

        function renderDeviceTypeChart(data) {
            // Count device types
            const deviceCounts = {};
            data.forEach(item => {
                if (item.device_type) {
                    deviceCounts[item.device_type] = (deviceCounts[item.device_type] || 0) + 1;
                } else {
                    deviceCounts['Unknown'] = (deviceCounts['Unknown'] || 0) + 1;
                }
            });

            const options = {
                series: Object.values(deviceCounts),
                labels: Object.keys(deviceCounts),
                chart: {
                    type: 'donut',
                    height: 350
                },
                legend: {
                    position: 'bottom'
                },
                plotOptions: {
                    pie: {
                        donut: {
                            size: '50%'
                        }
                    }
                },
                colors: ['#009EF7', '#F1416C', '#7239EA', '#FFC700', '#50CD89']
            };

            new ApexCharts(document.querySelector("#deviceTypeChart"), options).render();
        }

        function renderEngagementChart(data) {
            // Get all user durations
            const userDurations = {};
            data.forEach(item => {
                const user = item.display_name || item.actor_email || 'Unknown';
                if (!userDurations[user]) {
                    userDurations[user] = 0;
                }
                userDurations[user] += (item.duration_seconds || 0);
            });

            // Convert to minutes array
            const durationMinutes = Object.values(userDurations).map(seconds => Math.round(seconds / 60));

            // Find min and max durations (ensure min is at least 1)
            const minDuration = Math.max(1, Math.min(...durationMinutes));
            const maxDuration = Math.max(...durationMinutes);

            // Calculate range and create 3 evenly distributed categories
            const range = maxDuration - minDuration;
            const step = range / 3;

            const categories = [
                {
                    name: "Low Engagement",
                    min: minDuration,
                    max: minDuration + step
                },
                {
                    name: "Medium Engagement",
                    min: minDuration + step,
                    max: minDuration + (2 * step)
                },
                {
                    name: "High Engagement",
                    min: minDuration + (2 * step),
                    max: maxDuration + 1 // Add 1 to include the max value
                }
            ];

            // Count participants in each category
            const categoryCounts = categories.map(category => {
                return {
                    category: category.name + ` (${Math.round(category.min)}-${Math.round(category.max)} min)`,
                    count: 0
                };
            });

            // Categorize each user based on their total duration
            durationMinutes.forEach(duration => {
                for (let i = 0; i < categories.length; i++) {
                    if (duration >= categories[i].min && duration < categories[i].max) {
                        categoryCounts[i].count++;
                        break;
                    }
                }
            });

            // Prepare data for radar chart
            const seriesData = categoryCounts.map(item => item.count);
            const labels = categoryCounts.map(item => item.category);

            const options = {
                series: [{
                    name: 'Participants',
                    data: seriesData
                }],
                chart: {
                    height: 350,
                    type: 'radar',
                },
                dataLabels: {
                    enabled: true,
                    background: {
                        enabled: true,
                        borderRadius: 2,
                    }
                },
                plotOptions: {
                    radar: {
                        size: 140,
                        polygons: {
                            strokeColors: '#e9e9e9',
                            fill: {
                                colors: ['#f8f8f8', '#fff']
                            }
                        }
                    }
                },
                title: {
                    text: 'Participant Engagement by Duration'
                },
                colors: ['#FF4560'],
                markers: {
                    size: 4,
                    colors: ['#FF4560'],
                    strokeColor: '#FF4560',
                    strokeWidth: 2,
                },
                tooltip: {
                    y: {
                        formatter: function (val) {
                            return val + ' participants';
                        }
                    }
                },
                xaxis: {
                    categories: labels
                },
                yaxis: {
                    min: 0
                }
            };

            new ApexCharts(document.querySelector("#engagementChart"), options).render();
        }

        function renderParticipantCountChart(data) {
            // Find all unique start and end times
            const timeEvents = [];

            // Extract all start and end times from the data
            data.forEach(item => {
                if (item.start_time) {
                    timeEvents.push({
                        time: new Date(item.start_time).getTime(),
                        type: 'join'
                    });
                }
                if (item.end_time) {
                    timeEvents.push({
                        time: new Date(item.end_time).getTime(),
                        type: 'leave'
                    });
                }
            });

            // Sort events by time
            timeEvents.sort((a, b) => a.time - b.time);

            // If no events, return
            if (timeEvents.length === 0) {
                console.warn('No time events found for participant count chart');
                return;
            }

            // Find the global start and end time
            const startTime = timeEvents[0].time;
            const endTime = timeEvents[timeEvents.length - 1].time;
            const totalDuration = endTime - startTime;

            // Create time intervals (100 intervals as requested)
            const numIntervals = 100;
            const intervalDuration = totalDuration / numIntervals;

            // Create time points for each interval
            const timePoints = [];
            for (let i = 0; i < numIntervals; i++) {
                timePoints.push(startTime + (i * intervalDuration));
            }

            // Calculate participant count at each time point
            const participantCounts = [];
            let currentCount = 0;

            timePoints.forEach(timePoint => {
                // Process all events that happened before this time point
                while (timeEvents.length > 0 && timeEvents[0].time <= timePoint) {
                    const event = timeEvents.shift();
                    if (event.type === 'join') {
                        currentCount++;
                    } else if (event.type === 'leave') {
                        currentCount = Math.max(0, currentCount - 1); // Ensure count doesn't go below 0
                    }
                }

                // Add the current count at this time point
                participantCounts.push({
                    x: timePoint,
                    y: currentCount
                });
            });

            // Find peak times (local maxima)
            const peakTimes = [];
            for (let i = 1; i < participantCounts.length - 1; i++) {
                if (participantCounts[i].y > participantCounts[i - 1].y &&
                    participantCounts[i].y >= participantCounts[i + 1].y) {
                    peakTimes.push({
                        time: participantCounts[i].x,
                        count: participantCounts[i].y
                    });
                }
            }

            // Sort peaks by count (descending)
            peakTimes.sort((a, b) => b.count - a.count);

            // Take top 3 peaks
            const topPeaks = peakTimes.slice(0, 3);

            // Create annotations for peak times
            const annotations = {
                points: topPeaks.map(peak => ({
                    x: peak.time,
                    y: peak.count,
                    marker: {
                        size: 6,
                        fillColor: '#FF4560',
                        strokeColor: '#fff',
                        strokeWidth: 2,
                        radius: 2
                    },
                    label: {
                        borderColor: '#FF4560',
                        offsetY: 0,
                        style: {
                            color: '#fff',
                            background: '#FF4560',
                            fontSize: '10px',
                            fontWeight: 'bold',
                            padding: {
                                left: 5,
                                right: 5,
                                top: 2,
                                bottom: 2
                            }
                        },
                        text: `Peak: ${peak.count} participants`
                    }
                }))
            };

            // Calculate average participant count
            const avgCount = participantCounts.reduce((sum, point) => sum + point.y, 0) / participantCounts.length;

            // Create chart options
            const options = {
                series: [{
                    name: 'Participants',
                    data: participantCounts
                }],
                chart: {
                    type: 'area',
                    height: 400,
                    fontFamily: 'inherit',
                    toolbar: {
                        show: true,
                        tools: {
                            download: true,
                            selection: true,
                            zoom: true,
                            zoomin: true,
                            zoomout: true,
                            pan: true,
                            reset: true
                        }
                    },
                    animations: {
                        enabled: true,
                        easing: 'easeinout',
                        speed: 800
                    },
                    dropShadow: {
                        enabled: true,
                        top: 3,
                        left: 2,
                        blur: 4,
                        opacity: 0.1
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 3
                },
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.3,
                        stops: [0, 90, 100],
                        colorStops: [
                            {
                                offset: 0,
                                color: '#009ef7',
                                opacity: 0.8
                            },
                            {
                                offset: 100,
                                color: '#009ef7',
                                opacity: 0.2
                            }
                        ]
                    }
                },
                markers: {
                    size: 0,
                    strokeWidth: 3,
                    strokeColors: '#009ef7',
                    strokeOpacity: 0.9,
                    strokeDashArray: 0,
                    fillOpacity: 1,
                    shape: 'circle',
                    radius: 2,
                    hover: {
                        size: 6
                    }
                },
                xaxis: {
                    type: 'datetime',
                    labels: {
                        formatter: function (val) {
                            return new Date(val).toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'});
                        },
                        style: {
                            colors: '#555',
                            fontSize: '12px',
                            fontFamily: 'inherit',
                            fontWeight: 400
                        }
                    },
                    axisBorder: {
                        show: true,
                        color: '#e0e0e0'
                    },
                    axisTicks: {
                        show: true,
                        color: '#e0e0e0'
                    }
                },
                yaxis: {
                    title: {
                        text: 'Number of Participants',
                        style: {
                            color: '#3F4254',
                            fontSize: '14px',
                            fontFamily: 'inherit',
                            fontWeight: 600
                        }
                    },
                    min: 0,
                    forceNiceScale: true,
                    labels: {
                        style: {
                            colors: '#3F4254',
                            fontSize: '12px',
                            fontFamily: 'inherit',
                            fontWeight: 400
                        },
                        formatter: function (val) {
                            return Math.round(val);
                        }
                    }
                },
                grid: {
                    borderColor: '#f1f1f1',
                    strokeDashArray: 4,
                    xaxis: {
                        lines: {
                            show: true
                        }
                    },
                    yaxis: {
                        lines: {
                            show: true
                        }
                    },
                    padding: {
                        top: 0,
                        right: 0,
                        bottom: 0,
                        left: 10
                    }
                },
                tooltip: {
                    x: {
                        format: 'HH:mm:ss'
                    },
                    y: {
                        formatter: function (val) {
                            return Math.round(val) + ' participants';
                        }
                    },
                    style: {
                        fontSize: '12px',
                        fontFamily: 'inherit'
                    },
                    marker: {
                        show: true
                    }
                },
                annotations: annotations,
                colors: ['#009ef7'],
                legend: {
                    show: false
                }
            };

            // Add a horizontal line for average participant count
            options.annotations.yaxis = [{
                y: avgCount,
                borderColor: '#FEB019',
                borderWidth: 2,
                borderDash: [5, 5],
                label: {
                    borderColor: '#FEB019',
                    style: {
                        color: '#fff',
                        background: '#FEB019'
                    },
                    text: `Avg: ${Math.round(avgCount)} participants`
                }
            }];

            // Render the chart
            new ApexCharts(document.querySelector("#participantCountChart"), options).render();
        }
    </script>
@endpush
