@extends('layouts.base')

@section('title')
    پنل کاربری
@endsection


@section('content')
    <div class=" container-xxl ">
        <div class="toolbar direction-rtl" id="kt_toolbar">
            <div class="d-flex flex-stack flex-wrap flex-sm-nowrap  overflow-auto  flex-sm-row flex-row">
                <!--begin::Info-->
                <div class="d-flex flex-column align-items-start justify-content-center flex-wrap me-1">
                    <!--begin::Title-->
                    <h3 class="text-dark fw-bolder my-1">
                        تنظیمات سازمانی اتاق {{$roomTitle}}
                    </h3>
                    <!--end::Title-->

                    <!--begin::Breadcrumb-->
                    <ul class="breadcrumb breadcrumb-line bg-transparent text-muted fw-bold p-0 my-1 fs-7">
                        <li class="breadcrumb-item">
                            <a href="{{route('dashboard')}}" class="text-muted text-hover-primary">
                                داشبورد </a>
                        </li>
                        <li class="breadcrumb-item">
                            اتاق ها
                        </li>
                        <li class="breadcrumb-item text-dark">
                            تنظیمات سازمانی
                        </li>
                    </ul>
                    <!--end::Breadcrumb-->
                </div>
                <!--end::Info-->


                <!--begin::Nav-->
                <div class="d-flex align-items-center flex-nowrap text-nowrap overflow-auto py-1">
                    <a href="{{route('room.users',[$meeting->meeting_id])}}"
                       class="btn btn-active-accent  fw-bolder ms-3">مدیریت کاربران</a>
                    <a href="{{route('room.index',[$meeting->meeting_id])}}"
                       class="btn btn-active-accent  fw-bolder ms-3">تنظیمات اتاق </a>
                    <a href="{{route('room.files',[$meeting->meeting_id])}}"
                       class="btn btn-active-accent   fw-bolder ms-3">مدیریت فایل ها</a>
                    <a href="{{route('room.audits',[$meeting->meeting_id])}}"
                       class="btn btn-active-accent  fw-bolder ms-3">گزارشات</a>
                </div>
                <!--end::Nav-->
            </div>
        </div>

        <div class="">
            <div class="card direction-rtl">
                <!--begin::Card header-->
                <div class="card-header">
                    <h2 class="card-title fw-bold">
                        دسترسی سازمانی
                    </h2>

                    <div class="card-toolbar">
                        <button class="btn btn-flex btn-primary" data-bs-toggle="modal"
                                data-bs-target="#kt_modal_add_member">
                            <!--begin::Svg Icon | path: /var/www/preview.keenthemes.com/keenthemes/start/docs/core/html/src/media/icons/duotune/general/gen041.svg-->
                            <span class="svg-icon svg-icon-muted "><svg width="24" height="24" viewBox="0 0 24 24"
                                                                        fill="none" xmlns="http://www.w3.org/2000/svg">
<rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
<rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
<rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
</svg>
</span>
                            <!--end::Svg Icon-->
                            اضافه کردن عضو
                        </button>
                    </div>
                </div>
                <!--end::Card header-->

                <!--begin::Card body-->
                <!--begin::Modal - Create App-->
                <div class="modal fade" id="kt_modal_add_member" tabindex="-1" aria-hidden="true" wire:ignore.self>
                    <!--begin::Modal dialog-->
                    <div class="modal-dialog modal-dialog-centered mw-600px" wire:ignore.self>
                        <!--begin::Modal content-->
                        <div class="modal-content">
                            <!--begin::Modal header-->
                            <div class="modal-header">
                                <!--begin::Modal title-->
                                <h2>اضافه کردن عضو جدید</h2>
                                <!--end::Modal title-->

                                <!--begin::Close-->
                                <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                                    <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
                                    <span class="svg-icon svg-icon-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24"
                                 height="24" viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)"
      fill="black"></rect>
<rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
</svg></span>
                                    <!--end::Svg Icon-->                </div>
                                <!--end::Close-->
                            </div>
                            <!--end::Modal header-->

                            <!--begin::Modal body-->
                            <livewire:room.room-enter-price-add-user :meetingId="$meeting->meeting_id"/>
                            <!--end::Modal body-->
                        </div>
                        <!--end::Modal content-->
                    </div>
                    <!--end::Modal dialog-->
                </div>
                <!--end::Modal - Create App-->
                <!--end::Card body-->
            </div>
            <!--begin::Row-->
            <div class="mt-5 g-0 g-xl-5 g-xxl-8 direction-rtl">

                <div class="row g-6 g-xl-9">
                    @if($invitedUsers->count() < 1)
                        <!--begin::Col-->
                        <div class="col-md-6 col-xxl-4">
                            <!--begin::Card-->
                            <div class="card">
                                <!--begin::Card body-->
                                <div class="card-body d-flex flex-center flex-column p-9">
                                    <!--begin::Wrapper-->
                                    <div class="py-10 text-center">
                                        <img src="{{asset('assets/media/svg/illustrations/enterprise.svg')}}"
                                             class="theme-light-show w-200px" alt="">
                                    </div>
                                    <!--end::Wrapper-->

                                    <!--begin::Name-->
                                    <h1 class="fw-semibold text-gray-800 text-center lh-lg">
                                        هنوز کسی عضو نشده!
                                    </h1>
                                    <!--end::Name-->

                                    <!--begin::Position-->
                                    <div class="fw-semibold text-gray-500 mb-6">میتوانید مدیران خود را اضافه کنید</div>
                                    <!--end::Position-->


                                    <!--begin::Link-->
                                    <button class="btn btn-sm btn-light-primary fw-bold" data-bs-toggle="modal"
                                            data-bs-target="#kt_modal_add_member">
                                        افزودن جدید
                                    </button>
                                    <!--end::Link-->
                                </div>
                                <!--begin::Card body-->
                            </div>
                            <!--begin::Card-->
                        </div>
                        <!--end::Col-->
                    @else
                        @foreach($invitedUsers as $invitedUser)
                            <!--begin::Col-->
                            <div class="col-md-6 col-xxl-4 " id="wrapper-user-{{\Vinkla\Hashids\Facades\Hashids::encode($invitedUser->id)}}">
                                <!--begin::Card-->
                                <div class="card">
                                    <!--begin::Card body-->
                                    <div class="card-body d-flex flex-center flex-column p-9">
                                        <!--begin::Wrapper-->
                                        <div class="mb-5">
                                            <!--begin::Avatar-->
                                            <div class="symbol  symbol-75px symbol-circle ">
                                                <img alt="Pic" src="{{$invitedUser->invitedUser->profile_photo}}">
                                            </div><!--end::Avatar-->
                                        </div>
                                        <!--end::Wrapper-->

                                        <!--begin::Name-->
                                        <a class="fs-4 text-gray-800 text-hover-primary fw-bold mb-0">
                                            {{$invitedUser->invitedUser->name}}
                                        </a>
                                        <!--end::Name-->

                                        <!--begin::Position-->
                                        <div
                                            class="fw-semibold text-gray-500 mb-6"> {{$invitedUser->invitedUser->email}}</div>
                                        <!--end::Position-->

                                        <!--begin::Info-->
                                        <div class="d-flex flex-center flex-wrap mb-5">
                                            <!--begin::Stats-->
                                            <div
                                                class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 mx-3 mb-3 text-center">
                                                <div class="fs-6 fw-bold text-gray-700">تاریخ پذیرش</div>
                                                <div
                                                    class="fw-semibold text-gray-500">{{\Morilog\Jalali\Jalalian::fromDateTime($invitedUser->invited_at)->format('d F Y')}}</div>
                                            </div>
                                            <!--end::Stats-->
                                        </div>
                                        <!--end::Info-->

                                        <!--begin::Link-->
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-sm btn-light-primary fw-bold"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#kt_modal_edit_permissions_{{\Vinkla\Hashids\Facades\Hashids::encode($invitedUser->id)}}">
                                                تغییر دسترسی ها
                                            </button>
                                            <livewire:enterprise.remove-user-from-company :invitedUser="$invitedUser->id" :meeting-id="$meeting->meeting_id"/>
                                        </div>
                                        <!--end::Link-->

                                        <!-- Modal -->
                                        <div class="modal fade"
                                             id="kt_modal_edit_permissions_{{\Vinkla\Hashids\Facades\Hashids::encode($invitedUser->id)}}"
                                             tabindex="-1"
                                             aria-hidden="true">
                                            <div class="modal-dialog modal-dialog-centered">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">تغییر دسترسی
                                                            های {{$invitedUser->invitedUser->name}} در
                                                            اتاق {{$roomTitle}}</h5>
                                                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2"
                                                             data-bs-dismiss="modal"
                                                             aria-label="Close">
                                                            <div class="btn btn-sm btn-icon btn-active-color-primary"
                                                                 data-bs-dismiss="modal">
                                                                <!--begin::Svg Icon -->
                                                                <span class="svg-icon svg-icon-1">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24"
                                                                         height="24" viewBox="0 0 24 24" fill="none">
                                                                    <rect opacity="0.5" x="6" y="17.3137" width="16"
                                                                          height="2" rx="1"
                                                                          transform="rotate(-45 6 17.3137)"
                                                                          fill="black"></rect>
                                                                    <rect x="7.41422" y="6" width="16" height="2" rx="1"
                                                                          transform="rotate(45 7.41422 6)"
                                                                          fill="black"></rect>
                                                                    </svg>
                                                                </span>
                                                                <!--end::Svg Icon-->
                                                            </div>

                                                        </div>
                                                    </div>
                                                    <div class="modal-body">
                                                        <livewire:enterprise.edit-user-permissions
                                                            :enterpriseUser="$invitedUser"
                                                            :wire:key="'user-permissions-'.\Vinkla\Hashids\Facades\Hashids::encode($invitedUser->id)"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--begin::Card body-->
                                </div>
                                <!--begin::Card-->
                            </div>
                            <!--end::Col-->
                        @endforeach

                    @endif
                </div>
            </div>
            <!--end::Row-->
        </div>
        @endsection

        @push('scripts')
            <script>
                Livewire.on('close_modal', () => {
                    $('.modal').modal('hide');
                });
            </script>
    @endpush
