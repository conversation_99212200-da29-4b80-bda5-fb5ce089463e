@extends('layouts.base')

@section('title')
    پنل کاربری
@endsection


@section('content')
    <div class=" container-xxl ">
        <div class="toolbar direction-rtl" id="kt_toolbar">
            <div class="d-flex flex-stack flex-wrap flex-sm-nowrap  overflow-auto  flex-sm-row flex-row">
                <!--begin::Info-->
                <div class="d-flex flex-column align-items-start justify-content-center flex-wrap me-1">
                    <!--begin::Title-->
                    <h3 class="text-dark fw-bolder my-1">
                        کاربران اتاق {{$roomTitle}}
                    </h3>
                    <!--end::Title-->

                    <!--begin::Breadcrumb-->
                    <ul class="breadcrumb breadcrumb-line bg-transparent text-muted fw-bold p-0 my-1 fs-7">
                        <li class="breadcrumb-item">
                            <a href="{{route('dashboard')}}" class="text-muted text-hover-primary">
                                داشبورد </a>
                        </li>
                        <li class="breadcrumb-item">
                            اتاق ها
                        </li>
                        <li class="breadcrumb-item text-dark">
                            کاربران
                        </li>
                    </ul>
                    <!--end::Breadcrumb-->
                </div>
                <!--end::Info-->


                <!--begin::Nav-->
                <div class="d-flex align-items-center flex-nowrap text-nowrap overflow-auto py-1">
                    <a href="{{route('room.users',[$meeting->meeting_id])}}"
                       class="btn btn-active-accent active  fw-bolder ms-3">مدیریت کاربران</a>
                    <a href="{{route('room.index',[$meeting->meeting_id])}}"
                       class="btn btn-active-accent  fw-bolder ms-3">تنظیمات اتاق </a>
                    <a href="{{route('room.files',[$meeting->meeting_id])}}"
                       class="btn btn-active-accent   fw-bolder ms-3">مدیریت فایل ها</a>

                    <a href="{{route('room.audits',[$meeting->meeting_id])}}"
                       class="btn btn-active-accent  fw-bolder ms-3">گزارشات</a>
                </div>
                <!--end::Nav-->
            </div>
        </div>

        <div class="">
            <!--begin::Row-->
            <div class="row g-0 g-xl-5 g-xxl-8 direction-rtl">
                <div class="col-md-12">
                    <div class="card card-stretch mb-5 mb-xxl-8">
                        <!--begin::Body-->
                        <div class="card-body direction-ltr">
                            <!--begin::Stats-->
                            <div class="">
                                <!--begin::Row-->
                                <div class="row gap-3">
                                    <div class="col">
                                        <button type="button"
                                                data-bs-toggle="modal" data-bs-target="#kt_modal_add_new_admin"
                                                class="btn btn-outline btn-bg-light btn-color-gray-600 btn-active-light-primary border-dashed border-active border-primary px-6 py-7 text-start w-100 min-w-150px  d-flex flex-stack">
                                            <!--begin::Svg Icon | path: icons/duotune/general/gen002.svg-->
                                            <span class="svg-icon svg-icon-2x ms-n1"><svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none">
<path opacity="0.3"
      d="M4.05424 15.1982C8.34524 7.76818 13.5782 3.26318 20.9282 2.01418C21.0729 1.98837 21.2216 1.99789 21.3618 2.04193C21.502 2.08597 21.6294 2.16323 21.7333 2.26712C21.8372 2.37101 21.9144 2.49846 21.9585 2.63863C22.0025 2.7788 22.012 2.92754 21.9862 3.07218C20.7372 10.4222 16.2322 15.6552 8.80224 19.9462L4.05424 15.1982ZM3.81924 17.3372L2.63324 20.4482C2.58427 20.5765 2.5735 20.7163 2.6022 20.8507C2.63091 20.9851 2.69788 21.1082 2.79503 21.2054C2.89218 21.3025 3.01536 21.3695 3.14972 21.3982C3.28408 21.4269 3.42387 21.4161 3.55224 21.3672L6.66524 20.1802L3.81924 17.3372ZM16.5002 5.99818C16.2036 5.99818 15.9136 6.08615 15.6669 6.25097C15.4202 6.41579 15.228 6.65006 15.1144 6.92415C15.0009 7.19824 14.9712 7.49984 15.0291 7.79081C15.0869 8.08178 15.2298 8.34906 15.4396 8.55884C15.6494 8.76862 15.9166 8.91148 16.2076 8.96935C16.4986 9.02723 16.8002 8.99753 17.0743 8.884C17.3484 8.77046 17.5826 8.5782 17.7474 8.33153C17.9123 8.08486 18.0002 7.79485 18.0002 7.49818C18.0002 7.10035 17.8422 6.71882 17.5609 6.43752C17.2796 6.15621 16.8981 5.99818 16.5002 5.99818Z"
      fill="black"></path>
<path
    d="M4.05423 15.1982L2.24723 13.3912C2.15505 13.299 2.08547 13.1867 2.04395 13.0632C2.00243 12.9396 1.9901 12.8081 2.00793 12.679C2.02575 12.5498 2.07325 12.4266 2.14669 12.3189C2.22013 12.2112 2.31752 12.1219 2.43123 12.0582L9.15323 8.28918C7.17353 10.3717 5.4607 12.6926 4.05423 15.1982ZM8.80023 19.9442L10.6072 21.7512C10.6994 21.8434 10.8117 21.9129 10.9352 21.9545C11.0588 21.996 11.1903 22.0083 11.3195 21.9905C11.4486 21.9727 11.5718 21.9252 11.6795 21.8517C11.7872 21.7783 11.8765 21.6809 11.9402 21.5672L15.7092 14.8442C13.6269 16.8245 11.3061 18.5377 8.80023 19.9442ZM7.04023 18.1832L12.5832 12.6402C12.7381 12.4759 12.8228 12.2577 12.8195 12.032C12.8161 11.8063 12.725 11.5907 12.5653 11.4311C12.4057 11.2714 12.1901 11.1803 11.9644 11.1769C11.7387 11.1736 11.5205 11.2583 11.3562 11.4132L5.81323 16.9562L7.04023 18.1832Z"
    fill="black"></path>
</svg></span>
                                            <span class="text-gray-800 fw-bolder fs-6 d-block">
                            افزودن مدیر
                        </span>
                                        </button>
                                    </div>
                                    <div class="col">
                                        <button type="button"
                                                data-bs-toggle="modal" data-bs-target="#kt_modal_add_new_guest"
                                                class="btn btn-outline  d-flex flex-stack btn-bg-light btn-color-gray-600 btn-active-light-primary border-dashed border-active px-6 py-7 text-start w-100 min-w-150px">
                                            <!--begin::Svg Icon | path: icons/duotune/general/gen005.svg-->
                                            <span class="svg-icon svg-icon-muted svg-icon-2x">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                     xmlns="http://www.w3.org/2000/svg">
                                                    <path opacity="0.3"
                                                          d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z"
                                                          fill="currentColor"/>
                                                    <path
                                                        d="M12.0006 11.1542C13.1434 11.1542 14.0777 10.22 14.0777 9.0771C14.0777 7.93424 13.1434 7 12.0006 7C10.8577 7 9.92348 7.93424 9.92348 9.0771C9.92348 10.22 10.8577 11.1542 12.0006 11.1542Z"
                                                        fill="currentColor"/>
                                                    <path
                                                        d="M15.5652 13.814C15.5108 13.6779 15.4382 13.551 15.3566 13.4331C14.9393 12.8163 14.2954 12.4081 13.5697 12.3083C13.479 12.2993 13.3793 12.3174 13.3067 12.3718C12.9257 12.653 12.4722 12.7981 12.0006 12.7981C11.5289 12.7981 11.0754 12.653 10.6944 12.3718C10.6219 12.3174 10.5221 12.2902 10.4314 12.3083C9.70578 12.4081 9.05272 12.8163 8.64456 13.4331C8.56293 13.551 8.49036 13.687 8.43595 13.814C8.40875 13.8684 8.41781 13.9319 8.44502 13.9864C8.51759 14.1133 8.60828 14.2403 8.68991 14.3492C8.81689 14.5215 8.95295 14.6757 9.10715 14.8208C9.23413 14.9478 9.37925 15.0657 9.52439 15.1836C10.2409 15.7188 11.1026 15.9999 11.9915 15.9999C12.8804 15.9999 13.7421 15.7188 14.4586 15.1836C14.6038 15.0748 14.7489 14.9478 14.8759 14.8208C15.021 14.6757 15.1661 14.5215 15.2931 14.3492C15.3838 14.2312 15.4655 14.1133 15.538 13.9864C15.5833 13.9319 15.5924 13.8684 15.5652 13.814Z"
                                                        fill="currentColor"/>
                                                </svg>
                                            </span>
                                            <span class="text-gray-800 fw-bolder fs-6 d-block">
                                                افزودن مهمان
                                            </span>
                                        </button>
                                    </div>
                                    <div class="col">
                                        <button type="button"
                                                data-bs-toggle="modal" data-bs-target="#kt_modal_upload_excel"
                                                class="btn  d-flex flex-stack btn-outline btn-bg-light btn-color-gray-600 btn-active-light-primary border-dashed border-active px-6 py-7 text-start w-100 min-w-150px">
                                            <!--begin::Svg Icon | path: icons/duotune/files/fil005.svg-->
                                            <span class="svg-icon svg-icon-2x ms-n1"><svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none">
<path opacity="0.3"
      d="M19 22H5C4.4 22 4 21.6 4 21V3C4 2.4 4.4 2 5 2H14L20 8V21C20 21.6 19.6 22 19 22ZM16 13H13V10C13 9.4 12.6 9 12 9C11.4 9 11 9.4 11 10V13H8C7.4 13 7 13.4 7 14C7 14.6 7.4 15 8 15H11V18C11 18.6 11.4 19 12 19C12.6 19 13 18.6 13 18V15H16C16.6 15 17 14.6 17 14C17 13.4 16.6 13 16 13Z"
      fill="black"></path>
<path d="M15 8H20L14 2V7C14 7.6 14.4 8 15 8Z" fill="black"></path>
</svg></span>
                                            <!--end::Svg Icon--> <span
                                                class="text-gray-800 fw-bolder fs-6 d-block">
                                                آپلود لیست اکسل
                                            </span>
                                        </button>
                                    </div>
                                    <livewire:room.room-users-export-to-excel :meetingId="$meeting->meeting_id"/>
                                    <div class="col ">
                                        <a href="{{route('room.audits',[$meeting->meeting_id])}}" type="button"
                                           class="btn btn-outline  d-flex flex-stack btn-bg-light btn-color-gray-600 btn-active-light-primary border-dashed border-active px-6 py-7  text-start w-100 min-w-150px">
                                            <!--begin::Svg Icon | path: icons/duotune/files/fil023.svg-->
                                            <span class="svg-icon svg-icon-2x ms-n1"><svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none">
<path opacity="0.3"
      d="M5 15C3.3 15 2 13.7 2 12C2 10.3 3.3 9 5 9H5.10001C5.00001 8.7 5 8.3 5 8C5 5.2 7.2 3 10 3C11.9 3 13.5 4 14.3 5.5C14.8 5.2 15.4 5 16 5C17.7 5 19 6.3 19 8C19 8.4 18.9 8.7 18.8 9C18.9 9 18.9 9 19 9C20.7 9 22 10.3 22 12C22 13.7 20.7 15 19 15H5ZM5 12.6H13L9.7 9.29999C9.3 8.89999 8.7 8.89999 8.3 9.29999L5 12.6Z"
      fill="black"></path>
<path d="M17 17.4V12C17 11.4 16.6 11 16 11C15.4 11 15 11.4 15 12V17.4H17Z" fill="black"></path>
<path opacity="0.3" d="M12 17.4H20L16.7 20.7C16.3 21.1 15.7 21.1 15.3 20.7L12 17.4Z" fill="black"></path>
<path d="M8 12.6V18C8 18.6 8.4 19 9 19C9.6 19 10 18.6 10 18V12.6H8Z" fill="black"></path>
</svg></span>
                                            <!--end::Svg Icon--> <span
                                                class="text-gray-800 fw-bolder fs-6 d-block ">
                            گزارشات

                        </span>
                                        </a>
                                    </div>
                                </div>
                                <!--end::Row-->
                            </div>
                            <!--end::Stats-->
                        </div>
                        <!--end::Body-->
                    </div>
                </div>

                <div class="col-md-12 mt-0">
                    <livewire:room.room-user-manager :meetingId="$meeting->meeting_id"/>
                </div>

            </div>
            <!--end::Row-->
        </div>
    </div>
    <!-- Excel Upload Modal -->
    <div class="modal fade" tabindex="-1" id="kt_modal_upload_excel" wire:ignore>
        <div class="modal-dialog modal-lg direction-rtl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">آپلود لیست کاربران از فایل اکسل</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal">
                        <span class="svg-icon svg-icon-2x"></span>
                    </div>
                </div>

                <div class="modal-body">
                    <livewire:room.excel-user-upload :meetingId="$meeting->meeting_id"/>
                </div>
            </div>
        </div>
    </div>
@endsection
