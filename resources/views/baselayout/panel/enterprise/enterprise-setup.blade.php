@extends('layouts.base')

@section('title')
    پنل کاربری - تنظیمات سازمانی
@endsection


@section('content')

    <style>
        .stepper.stepper-pills .stepper-nav:before {
            content: "";
            position: absolute;
            top: 50%;
            width: calc(100% - 377px);
            height: 2px;
            background: #f2f2f2;
            z-index: 0;
        }

        .stepper.stepper-pills .stepper-nav {
            position: relative;
            justify-content: space-around;
        }

        .stepper.stepper-pills .stepper-nav > div {
            position: relative;
        }
        .max-w-250px{
            max-width: 250px;
        }
    </style>
    <div class=" container-xxl ">
        <div class="toolbar direction-rtl" id="kt_toolbar">
            <div class="d-flex flex-stack flex-wrap flex-sm-nowrap  overflow-auto  flex-sm-row flex-row">
                <!--begin::Info-->
                <div class="d-flex flex-column align-items-start justify-content-center flex-wrap me-1">
                    <!--begin::Title-->
                    <h3 class="text-dark fw-bolder my-1">

                    </h3>
                    <!--end::Title-->

                    <!--begin::Breadcrumb-->
                    <ul class="breadcrumb breadcrumb-line bg-transparent text-muted fw-bold p-0 my-1 fs-7">
                        <li class="breadcrumb-item">
                            <a href="{{route('dashboard')}}" class="text-muted text-hover-primary">
                                داشبورد </a>
                        </li>
                        <li class="breadcrumb-item">
                            اتاق ها
                        </li>
                        <li class="breadcrumb-item text-dark">
                            تنظیمات سازمانی
                        </li>
                    </ul>
                    <!--end::Breadcrumb-->
                </div>
                <!--end::Info-->


                <!--begin::Nav-->

                <!--end::Nav-->
            </div>
        </div>

        <div class="">
            <div class="card direction-rtl">
                <!--begin::Card header-->
                <div class="card-header">
                    <h2 class="card-title fw-bold">
                        تنظیم سازمان
                    </h2>

                    <div class="card-toolbar">

                    </div>
                </div>
                <div class="card-body">
                   <livewire:enterprise.enterprise-register-component :roomId="$roomId" />
                </div>
                <!--end::Card header-->

                <!--begin::Card body-->
                <!--begin::Modal - Create App-->
                <div class="modal fade" id="kt_modal_add_member" tabindex="-1" aria-hidden="true" wire:ignore.self>
                    <!--begin::Modal dialog-->
                    <div class="modal-dialog modal-dialog-centered mw-600px" wire:ignore.self>
                        <!--begin::Modal content-->
                        <div class="modal-content">
                            <!--begin::Modal header-->
                            <div class="modal-header">
                                <!--begin::Modal title-->
                                <h2>اضافه کردن عضو جدید</h2>
                                <!--end::Modal title-->

                                <!--begin::Close-->
                                <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                                    <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
                                    <span class="svg-icon svg-icon-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24"
                                 height="24" viewBox="0 0 24 24" fill="none">
<rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)"
      fill="black"></rect>
<rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
</svg></span>
                                    <!--end::Svg Icon-->                </div>
                                <!--end::Close-->
                            </div>
                            <!--end::Modal header-->

                            <!--begin::Modal body-->

                            <!--end::Modal body-->
                        </div>
                        <!--end::Modal content-->
                    </div>
                    <!--end::Modal dialog-->
                </div>
                <!--end::Modal - Create App-->
                <!--end::Card body-->
            </div>
            <!--begin::Row-->
            <div class="mt-5 g-0 g-xl-5 g-xxl-8 direction-rtl">

            </div>
            <!--end::Row-->
        </div>
@endsection

@push('scripts')
@endpush
