<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/vite.svg">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>داشبورد رونویسی جلسات هوش مصنوعی</title>
    <style class="automa-element-selector">
        .automa-element-selector {
            direction: rtl
        }

        .space-x-4 {
            gap: 10px;
        }

        [automa-isDragging] {
            user-select: none
        }

        [automa-el-list] {
            outline: 2px dashed #6366f1;
        }

        /* RTL Support */
        [dir="rtl"] {
            direction: rtl;
            text-align: right;
        }

        [dir="rtl"] .flex {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .space-x-2 > * + *,
        [dir="rtl"] .space-x-3 > * + *,
        [dir="rtl"] .space-x-4 > * + *,
        [dir="rtl"] .space-x-6 > * + * {
            margin-left: 0;
            margin-right: var(--tw-space-x-reverse, 0) * 0.5rem;
        }

        [dir="rtl"] .space-x-2 > * + * { margin-right: 0.5rem; }
        [dir="rtl"] .space-x-3 > * + * { margin-right: 0.75rem; }
        [dir="rtl"] .space-x-4 > * + * { margin-right: 1rem; }
        [dir="rtl"] .space-x-6 > * + * { margin-right: 1.5rem; }

        [dir="rtl"] .justify-between {
            flex-direction: row-reverse;
        }
        [dir="rtl"] .flex-col.flex {
            flex-direction: column;
        }

        [dir="rtl"] .text-left {
            text-align: right;
        }

        [dir="rtl"] .left-3 {
            left: auto;
            right: 0.75rem;
        }

        [dir="rtl"] .left-7 {
            left: auto;
            right: 1.75rem;
        }

        [dir="rtl"] .-right-1 {
            right: auto;
            left: -0.25rem;
        }

        [dir="rtl"] .-bottom-1 {
            bottom: -0.25rem;
        }

        [dir="rtl"] .pl-10 {
            padding-left: 1rem;
            padding-right: 2.5rem;
        }

        [dir="rtl"] .flex-row-reverse {
            flex-direction: row;
        }
        .flex.flex-row {
            flex-direction: row;
        }
    </style>
    <link rel="stylesheet" href="{{asset('css/public-index-aibot.css')}}">
</head>
<body class="min-h-screen transition-all duration-500 bg-gray-50 theme-light">
<div id="root">
    <livewire:a-i-bot.a-i-bbot-public :token="$token" />
</div>

<script>
    class ThemeManager {
        constructor() {
            this.isDarkMode = localStorage.getItem('darkMode') === 'true';
            this.init();
        }

        init() {
            this.applyTheme();
            this.setupToggleButton();
        }

        getThemeClasses() {
            return {
                bgname: this.isDarkMode ? 'theme-dark' : 'theme-light',
                bg: this.isDarkMode ? 'bg-gray-950' : 'bg-gray-50',
                cardBg: this.isDarkMode ? 'bg-gray-900/50' : 'bg-white/70',
                text: this.isDarkMode ? 'text-gray-100' : 'text-gray-900',
                textSecondary: this.isDarkMode ? 'text-gray-400' : 'text-gray-600',
                textMuted: this.isDarkMode ? 'text-gray-500' : 'text-gray-500',
                border: this.isDarkMode ? 'border-gray-800/60' : 'border-gray-200/60',
                hover: this.isDarkMode ? 'hover:bg-gray-800/50' : 'hover:bg-gray-100/50',
                accent: 'from-blue-600 via-purple-600 to-indigo-600',
                accentSecondary: this.isDarkMode ? 'from-gray-800 to-gray-900' : 'from-gray-50 to-white',
                glass: this.isDarkMode ? 'backdrop-blur-xl bg-gray-900/20' : 'backdrop-blur-xl bg-white/20'
            };
        }

        applyTheme() {
            const theme = this.getThemeClasses();
            // Apply background
            const body = document.querySelector('body');
            if (body && body.className) {
                body.className = body.className.replace(/bg-gray-(50|950)/, theme.bg);
                body.className = body.className.replace(/theme-(light|dark)/, theme.bgname);
            }

            // Apply header glass effect
            const header = document.querySelector('header');
            if (header && header.className) {
                header.className = header.className.replace(/backdrop-blur-xl bg-white\/20|backdrop-blur-xl bg-gray-900\/20/, theme.glass);
            }

            // Apply card backgrounds
            const cards = document.querySelectorAll('[class*="bg-white/70"], [class*="bg-gray-900/50"]');
            cards.forEach(card => {
                if (card.className) {
                    card.className = card.className.replace(/bg-white\/70|bg-gray-900\/50/, theme.cardBg);
                }
            });

            // Apply text colors
            const texts = document.querySelectorAll('[class*="text-gray-900"], [class*="text-gray-100"]');
            texts.forEach(text => {
                if (text.className) {
                    text.className = text.className.replace(/text-gray-900|text-gray-100/, theme.text);
                }
            });


            // Apply borders
            const borders = document.querySelectorAll('[class*="border-gray-200/60"], [class*="border-gray-800/60"]');
            borders.forEach(border => {
                if (border.className) {
                    border.className = border.className.replace(/border-gray-200\/60|border-gray-800\/60/, theme.border);
                }
            });

            // Apply hover effects
            const hovers = document.querySelectorAll('[class*="hover:bg-gray-100/50"], [class*="hover:bg-gray-800/50"]');
            hovers.forEach(hover => {
                if (hover.className) {
                    hover.className = hover.className.replace(/hover:bg-gray-100\/50|hover:bg-gray-800\/50/, theme.hover);
                }
            });

            // Apply gradient backgrounds
            const gradients = document.querySelectorAll('[class*="from-gray-50"], [class*="from-gray-800"]');
            gradients.forEach(gradient => {
                if (gradient.className) {
                    gradient.className = gradient.className.replace(/from-gray-50 to-white|from-gray-800 to-gray-900/, theme.accentSecondary);
                }
            });

            this.updateThemeIcon();
        }

        updateThemeIcon() {
            const themeButton = document.querySelector('button .lucide-moon').parentElement;
            const icon = themeButton.querySelector('svg');

            if (this.isDarkMode) {
                // Sun icon
                icon.innerHTML = '<circle cx="12" cy="12" r="4"></circle><path d="m12 2 0 2"></path><path d="m12 20 0 2"></path><path d="m4.93 4.93 1.41 1.41"></path><path d="m17.66 17.66 1.41 1.41"></path><path d="m2 12 2 0"></path><path d="m20 12 2 0"></path><path d="m6.34 17.66-1.41 1.41"></path><path d="m19.07 4.93-1.41 1.41"></path>';
            } else {
                // Moon icon
                icon.innerHTML = '<path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>';
            }
        }

        setupToggleButton() {
            const themeButton = document.querySelector('button .lucide-moon').parentElement;
            themeButton.addEventListener('click', () => this.toggle());
        }

        toggle() {
            this.isDarkMode = !this.isDarkMode;
            localStorage.setItem('darkMode', this.isDarkMode);
            this.applyTheme();
        }
    }

    // Initialize theme manager when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
        new ThemeManager();
    });
</script>
</body>
</html>


