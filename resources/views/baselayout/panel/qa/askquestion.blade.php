@extends('layouts.base')

@section('title')
    پرسش
@endsection

@push('header_style_after')
	<link rel="stylesheet" href="{{ asset('assets/plugins/custom/prismjs/prismjs.bundle.css') }}">
@endpush

@section('content')
<div class="d-flex flex-column flex-column-fluid direction-rtl">
	<!--begin::toolbar-->
	<div class="toolbar" id="kt_toolbar">
		<div class="d-flex flex-stack flex-wrap flex-sm-nowrap">
			<!--begin::Info-->
			<div class="d-flex flex-column align-items-start justify-content-center flex-wrap me-1">
				<!--begin::Title-->
				<h3 class="text-dark fw-bolder my-1">پرسش</h3>
				<!--end::Title-->
				<!--begin::Breadcrumb-->
				<ul class="breadcrumb breadcrumb-line bg-transparent text-muted fw-bold p-0 my-1 fs-7">
					<li class="breadcrumb-item">
						<a href="{{route('dashboard')}}" class="text-muted text-hover-primary">خانه</a>
					</li>
					<li class="breadcrumb-item">سوالات</li>
					<li class="breadcrumb-item text-dark">پرسش</li>
				</ul>
				<!--end::Breadcrumb-->
			</div>
			<!--end::Info-->
			<!--begin::Nav-->
			<div class="d-flex align-items-center flex-nowrap text-nowrap overflow-auto py-1">
				<a href="faq.html" class="btn btn-active-accent fw-bolder">تمام سوالات</a>
				<a href="pricing.html" class="btn btn-active-accent fw-bolder ms-3">دسته بندی</a>
				<a href="invoice.html" class="btn btn-active-accent fw-bolder ms-3">بهترین سوالات</a>
				<a href="login.html" class="btn btn-active-accent fw-bolder ms-3">برترین پرسشگران</a>
				<a href="wizard.html" class="btn btn-active-accent fw-bolder ms-3">برترین پاسخ دهندگان</a>
				<a href="error.html" class="btn btn-active-accent active fw-bolder ms-3">پرسش سوال</a>
			</div>
			<!--end::Nav-->
		</div>
	</div>
	<!--end::toolbar-->
	<!--begin::Content-->
	<div class="content fs-6 d-flex flex-column-fluid pb-14" id="kt_content">
		<!--begin::Container-->
		<div class="col-md-12">
			<!--begin::Page Layout-->
			<div class="d-flex flex-column flex-md-row">
				<!--begin::Aside-->
				@include("layouts.components.qasidebar")
				<!--end::Aside-->
				<!--begin::Layout-->
				<div class="flex-md-row-fluid ms-md-12 position-relative">
					<!--begin::askQuestion-->
					<div class="card direction-ltr h-100" id="kt_inbox_reply">
						<div class="card-body p-0">
							<!--begin::Form-->
							<form id="kt_inbox_reply_form" class="h-100 d-flex flex-column justify-content-between" method="post" action="{{route('questions.store')}}">
                            {{ csrf_field() }}
								<!--begin::Body-->
								<div class="d-block">
									<!--begin::To-->
									<div class="direction-rtl d-flex align-items-center border-bottom inbox-to px-8 min-h-50px">
										<div class="text-gray-600 w-75px">کلیت سوال: </div>
										<div class="d-flex align-items-center flex-grow-1">

                                                <input class="form-control form-control-lg form-control-solid" name="tags" value='{{ old('tags') }}' id="kt_tagify_2"/>

                                            @push('scripts')
                                                <script>
                                                    // The DOM elements you wish to replace with Tagify

                                                    var input2 = document.querySelector("#kt_tagify_2");

                                                    // Initialize Tagify components on the above inputs

                                                    new Tagify(input2);
                                                </script>
                                            @endpush
										</div>
									</div>
									<!--end::To-->
									<!--begin::Subject-->
									<div class="border-bottom direction-rtl">
										<input class="form-control border-0 px-8 min-h-45px" name="title" value="{{ old('title') }}" placeholder="موضوع:">
									</div>
									<!--end::Subject-->
									<!--begin::Editor-->
                                    {{-- <textarea name="body" class="form-control" rows="15" required>{{ old('content') }}</textarea> --}}
									<div wire:ignore>
										@include("layouts.components.editor_not_livewire", ["model_name"=>"body"])
									</div>
									{{-- <div id="editor" name="dasdsadsdd" class="min-h-200px rounded-0"></div> --}}
									<!--end::Editor-->
                                    @if ($errors->has('body'))
                                        <span class="invalid-feedback d-block" role="alert">
                                                <strong>{{ $errors->first('body') }}</strong>
                                    </span>
                                    @endif

								</div>

								<!--end::Body-->
								<!--begin::Footer-->
								<div class="d-flex flex-stack py-5 ps-8 pe-5 border-top">

									<!--begin::Actions-->
									<div class="d-flex align-items-center ms-3">
										<!--begin::Send-->
										<button class="btn btn-primary fw-bold px-6 ms-4">ثبت سوال</button>
										<!--end::Send-->
									</div>
									<!--end::Actions-->
								</div>
								<!--end::Footer-->
							</form>
							<!--end::Form-->
						</div>
					</div>
					<!--end::askQuestion-->
				</div>
				<!--end::Layout-->
			</div>
			<!--end::Page Layout-->
		</div>
		<!--end::Container-->
	</div>
	<!--end::Content-->
</div>
@endsection


@push('scripts')
<script src={{ asset('assets/js/custom/widgets.js') }}></script>
<script src={{ asset('assets/js/custom/documentation/documentation.js') }}></script>
<script src={{ asset('assets/plugins/custom/prismjs/prismjs.bundle.js') }}></script>
<script src="{{ asset('assets/js/tinymce/tinymce.min.js') }}"></script>

{{-- <script>
	var quill = new Quill('#editor', {
		modules: {
			toolbar: [
				[{ 'header': [1, 3, 5, false] }],
				['bold', 'italic', 'underline', 'code-block', 'link', 'list'],
				[{ 'direction': 'rtl' }, { 'list': 'ordered'}, { 'list': 'bullet' }, 'image'],
				['clean']
			]
		},
		theme: 'snow'
	});
	quill.format('align', 'right');
	quill.format('direction', 'rtl');
</script> --}}
@endpush
