<!DOCTYPE html>
<html lang="fa">
<!--begin::Head-->
<head>
    @include("layouts.components.meta")
    <title>آی روم | ورود</title>
    <link rel="canonical" href=""/>
    <link rel="shortcut icon" href="{{asset('index-assets/')}}/svg/iroom-logo.svg">
    <!--begin::Global Stylesheets Bundle(used by all pages)-->
    <link href="{{ asset('assets/plugins/global/plugins.bundle.css') }}" rel="stylesheet" type="text/css"/>
    <link href="{{ asset('assets/css/style.bundle.css') }}" rel="stylesheet" type="text/css"/>
    <!--end::Global Stylesheets Bundle-->
</head>
<!--end::Head-->
<!--begin::Body-->
<body id="kt_body" data-sidebar="on"
      class="bg-white header-fixed header-tablet-and-mobile-fixed toolbar-enabled sidebar-enabled">
<!--begin::Main-->
<div class="d-flex flex-column flex-root">
    <!--begin::Login-->
    <div class="d-flex flex-column flex-lg-row flex-column-fluid" id="kt_login">
        <!--begin::Aside-->
        <div class="d-flex flex-column flex-lg-row-auto bg-primary w-lg-600px pt-15 pt-lg-0">
            <!--begin::Aside Top-->
            <div class="d-flex flex-column-auto flex-column pt-lg-40 pt-15 text-center" style="
    background: none;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background:      linear-gradient(240.75deg,#226ce9 -23.29%,#cd56eb -23.29%,#120e71 32.29%,#000537 87.51%);
">
                <!--begin::Aside Logo-->
                <a href="/" class="mb-6">
                    <img alt="Logo" src="{{asset('assets/BVI/iroom-logo-dark.svg')}}"
                         class="h-75px"/>
                </a>
                <!--end::Aside Logo-->
                <!--begin::Aside Subtitle-->
                <h3 class="fw-bolder fs-2x text-white lh-lg">
                    <br/>
                    سرویس تجاری گوگل میت
                </h3>
                <!--end::Aside Subtitle-->
            </div>
            <!--end::Aside Top-->
            <!--begin::Aside Bottom-->
{{--            <div--}}
{{--                class="d-flex flex-row-fluid bgi-size-contain bgi-no-repeat bgi-position-y-bottom bgi-position-x-center min-h-350px"--}}
{{--                style="background-image: url({{ asset('assets/media/svg/illustrations/login-1.svg') }})"></div>--}}
            <!--end::Aside Bottom-->
        </div>
        <!--begin::Aside-->
        <!--begin::Content-->
        <div
            class="direction-rtl login-content flex-lg-row-fluid d-flex flex-column justify-content-center position-relative overflow-hidden py-20 px-10 p-lg-7 mx-auto mw-450px w-100">
            <!--begin::Wrapper-->
            <div class="d-flex flex-column-fluid flex-center py-10">
                <!--begin::Signin Form-->
                <form class="form w-100" method="POST" action="{{ route('login') }}" novalidate="novalidate"
                      id="kt_login_signin_form" data-after-login-url="{{ route('login') }}">
                    <!--begin::Title-->
                    @csrf
                    <div class="pb-5 pb-lg-15">
                        <h3 class="fw-bolder text-dark display-6">به آی روم خوش آمدید</h3>
                        <div class="text-muted fw-bold fs-3">هنوز عضو نشدید؟
                            <a href="#" class="text-primary fw-bolder" id="kt_login_signin_form_singup_button">عضویت</a>
                        </div>
                    </div>
                    <!--begin::Title-->
                    <!--begin::Form group-->
                    <div class="fv-row mb-10">
                        <label class="label-required form-label fs-6 fw-bolder text-dark">ایمیل</label>
                        <input class="direction-ltr form-control form-control-lg form-control-solid" type="text"
                               name="email"
                               autocomplete="off" tabindex="1"/>
                        @if ($errors->has('email') &&  empty(session('register') && empty('forgot_pass')))
                            <div class="fv-plugins-message-container">
                                <div data-field="email" data-validator="emailAddress" class="fv-help-block">
                                    {{ $errors->first('email') }}
                                </div>
                            </div>
                        @endif
                    </div>
                    <!--end::Form group-->
                    <!--begin::Form group-->
                    <div class="fv-row mb-10">
                        <div class="d-flex justify-content-between mt-n5">
                            <label class="label-required form-label fs-6 fw-bolder text-dark pt-5">رمزعبور</label>
                            <a href="#" class="text-primary fs-6 fw-bolder text-hover-primary pt-5"
                               id="kt_login_signin_form_password_reset_button">رمز خود را فراموش کردید؟</a>
                        </div>
                        <input class="direction-ltr form-control form-control-lg form-control-solid" type="password"
                               name="password"
                               autocomplete="off" tabindex="2"/>

                    </div>

                    <!-- Remember Me -->
                    <div class="block mt-4">
                        <label for="remember_me"
                               class="inline-flex items-center form-check form-check-custom form-check-solid mb-5">
                            <input id="remember_me" type="checkbox" tabindex="3"
                                   class="form-check-input rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                   name="remember">
                            <label
                                class="text-sm form-check-label fw-bold text-gray-600">{{ __('مرا به خاطر بسپار') }}</label>
                        </label>
                    </div>

                    <!--end::Form group-->
                    <!--begin::Action-->
                    <div class="text-align-left pb-lg-0 pb-5">
                        <button type="submit" id="kt_login_signin_form_submit_button" tabindex="4"
                                class="btn btn-primary fw-bolder fs-6 px-8 py-4 my-3 me-3">ورود
                        </button>
                        <a href="{{ url('auth/google') }}" type="button"
                           class="btn btn-light-primary fw-bolder px-8 py-4 my-3 fs-6">
                            <img src="{{ asset('assets/media/svg/social-icons/google.svg') }}"
                                 class="w-20px h-20px me-3" alt=""/>ورود با گوگل
                        </a>
                    </div>
                    <!--end::Action-->
                </form>
                <!--end::Signin Form-->
                <!--begin::Signup Form-->
                <form class="form d-none w-100" novalidate="novalidate" id="kt_login_signup_form" method="POST"
                      action="{{ route('register') }}">
                    <!--begin::Title-->
                    @csrf

                    @if (session()->has('message'))
                        <div class="alert alert-success">
                            {{ session('message') }}
                        </div>
                    @endif
                    @if (session()->has('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <div class="pb-5 pb-lg-15">
                        <h3 class="fw-bolder text-dark display-6">عضویت</h3>
                        <p class="text-muted fw-bold fs-3">برای ساختن اکانت اطلاعات خود را وارد کنید. اطلاعاتی که اجباری
                            نیستند را بعدا هم میتوانید تکمیل کنید</p>
                    </div>
                    <!--end::Title-->
                    <!--begin::Form section-->
                    <div style="max-height: 40vh; overflow-y: auto; margin-bottom: 30px; padding-left: 10px;">
                        <!--begin::Form group-->
                        <div class="fv-row mb-5">
                            <label class="label-required form-label fs-6 fw-bolder text-dark pt-5">ایمیل</label>
                            <input class="direction-ltr form-control form-control-lg form-control-solid" type="email"
                                   placeholder=""
                                   name="email" autocomplete="off" value="{{old('email')}}"/>
                            @if ($errors->has('email'))
                                <span class="invalid-feedback d-block" role="alert">
                                            <strong>{{ $errors->first('email') }}</strong>
                                        </span>
                            @endif

                        </div>
                        <!--end::Form group-->
                        <!--begin::Form group-->
                        <div class="fv-row mb-5">
                            <label for="confirmation" class="label-required form-label fs-6 fw-bolder text-dark pt-5">رمز
                                عبور</label>
                            <input class="direction-ltr form-control form-control-lg form-control-solid" type="password"
                                   placeholder=""
                                   name="password" autocomplete="off" id="confirmation"/>
                            @if ($errors->has('password'))
                                <span class="invalid-feedback d-block" role="alert">
                                            <strong>{{ $errors->first('password') }}</strong>
                                </span>
                            @endif
                        </div>
                        <!--end::Form group-->
                        <!--begin::Form group-->
                        <div class="fv-row mb-10">
                            <label class="label-required form-label fs-6 fw-bolder text-dark pt-5">تایید رمز
                                عبور</label>
                            <input class="direction-ltr form-control form-control-lg form-control-solid" type="password"
                                   placeholder=""
                                   name="password_confirmation" autocomplete="off"/>
                            @if ($errors->has('password_confirmation'))
                                <span class="invalid-feedback d-block" role="alert">
                                            <strong>{{ $errors->first('password_confirmation') }}</strong>
                                </span>
                            @endif
                        </div>
                        <div class="fv-row mb-5">
                            <label class="label-required form-label fs-6 fw-bolder text-dark pt-5">شماره تماس</label>
                            <input class="direction-ltr form-control form-control-lg form-control-solid" type="text"
                                   placeholder=""
                                   name="phone_number" data-inputmask="'mask': '(+99)9999999999'" autocomplete="off"
                                   value="{{ empty(old('phone_number')) ? '98' : old('phone_number') }}"/>
                            @if ($errors->has('phone_number'))
                                <span class="invalid-feedback d-block" role="alert">
                                            <strong>{{ $errors->first('phone_number') }}</strong>
                                </span>
                            @endif
                        </div>
                        <!--end::Form group-->

                        <!--begin::Form group-->
                        <div class="fv-row mb-10">
                            <div class="form-check form-check-custom form-check-solid mb-5">
                                <input name="agree" class="form-check-input" type="checkbox" id="kt_login_toc_agree"
                                       value="1"/>
                                <label class="form-check-label fw-bold text-gray-600" for="kt_login_toc_agree">با
                                    <a href="#" class="ms-1">شرایط و قوانین</a> موافق هستم.</label>
                            </div>
                        </div>
                        <!--end::Form group-->
                    </div>
                    <!--end::Form section-->
                    <!--begin::Form group-->
                    <div class="justify-content-end d-flex flex-wrap pb-lg-0 pb-5">
                        <button type="button" id="kt_login_signup_form_submit_button"
                                class="btn btn-primary fw-bolder fs-6 px-8 py-4 my-3 me-4">ثبت نام
                        </button>
                        <button type="button" id="kt_login_signup_form_cancel_button"
                                class="btn btn-light-primary fw-bolder fs-6 px-8 py-4 my-3">لغو
                        </button>
                    </div>
                    <!--end::Form group-->
                </form>
                <!--end::Signup Form-->
                <!--begin::Password Reset Form-->
                <form class="form d-none w-100" novalidate="novalidate" id="kt_login_password_reset_form" method="POST"
                      action="{{ route('password.email') }}">
                    <!--begin::Title-->
                    @csrf
                    <div class="pb-5 pb-lg-10">
                        <h3 class="fw-bolder text-dark display-6">بازیابی رمز عبور</h3>
                        <p class="text-muted fw-bold fs-3">برای بازیابی ایمیل خود را وارد کنید</p>


                        @if ($errors->any())
                            <div class="invalid-feedback d-block">
                                <div class="font-medium text-red-600">
                                    {{ __('یک مشکل در ارسال وجود داشت.') }}
                                </div>

                                <ul class="mt-3 list-disc list-inside text-sm text-red-600">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        @if (isset($status))
                            <div class="valid-feedback d-block">
                                {{ $status }}
                            </div>
                        @endif


                    </div>
                    <!--end::Title-->
                    <!--begin::Form group-->
                    <div class="fv-row mb-10">
                        <label class="label-required form-label fs-6 fw-bolder text-dark pt-5">ایمیل</label>
                        <input class="direction-ltr form-control form-control-lg form-control-solid" type="email"
                               placeholder=""
                               name="email" autocomplete="off" value="{{old('email')}}"/>
                        {{--                        @if ($errors->has('email'))--}}
                        {{--                            <span class="invalid-feedback d-block" role="alert">--}}
                        {{--                                        <strong>{{ $errors->first('email') }}</strong>--}}
                        {{--                            </span>--}}
                        {{--                        @endif--}}
                    </div>
                    <!--end::Form group-->
                    <!--begin::Form group-->
                    <div class="justify-content-end d-flex flex-wrap pb-lg-0">
                        <button type="button" id="kt_login_password_reset_form_submit_button"
                                class="btn btn-primary fw-bolder fs-6 px-8 py-4 my-3 me-4">تایید
                        </button>
                        <button type="button" id="kt_login_password_reset_form_cancel_button"
                                class="btn btn-light-primary fw-bolder fs-6 px-8 py-4 my-3">لغو
                        </button>
                    </div>
                    <!--end::Form group-->
                </form>
                <!--end::Password Reset Form-->
            </div>
            <!--end::Wrapper-->
            <!--begin::Footer-->
            <div class="d-flex justify-content-lg-start justify-content-center align-items-center py-7 py-lg-0">
                <a href="/" class="text-primary fw-bolder fs-4">خانه</a>
                <a href="#" class="text-primary  ms-10 fw-bolder fs-4">قوانین</a>
                <a href="#" class="text-primary ms-10 fw-bolder fs-4">برنامه ها</a>
                <a href="#" class="text-primary ms-10 fw-bolder fs-4">ارتباط با ما</a>
            </div>
            <!--end::Footer-->
        </div>
        <!--end::Content-->
    </div>
    <!--end::Login-->
</div>
<!--end::Main-->
<!--begin::Javascript-->
<!--begin::Global Javascript Bundle(used by all pages)-->
<script src="{{ asset('assets/plugins/global/plugins.bundle.js') }}"></script>
<script src="{{ asset('assets/js/scripts.bundle.js') }}"></script>
<!--end::Global Javascript Bundle-->
<!--begin::Page Custom Javascript(used by this page)-->
<script src="{{ asset('assets/js/custom/general/login.js') }}"></script>
<!--end::Page Custom Javascript-->
<!--end::Javascript-->

<script>
    @if(session('register'))
    $(document).ready(function () {
        $('#kt_login_signin_form').addClass('d-none');
        $('#kt_login_signup_form').removeClass('d-none');
    })

    @elseif(session('forgot_pass'))
    $(document).ready(function () {
        $('#kt_login_signin_form').addClass('d-none');
        $('#kt_login_password_reset_form').removeClass('d-none');
    })
    @endif


</script>

</body>
<!--end::Body-->

</html>
