<!DOCTYPE html>
<html lang="fa">
<head>
    <meta charset="utf-8">
    @if(isset($pageTitle))
        <title>{{$pageTitle}}</title>
    @else
        <title>آی روم</title>
    @endif

    <!-- SEO Meta Tags -->
    @if(!empty($detail->summery))
        <meta name="description" content="{{$detail->summery}}">
    @else
        <meta name="description" content="آی روم - سرویس تجاری گوگل میت">
    @endif
    <meta name="author" content="IROOM">

    <!-- Viewport -->
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Favicon and Touch Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="{{asset('index-assets/')}}/svg/iroom-logo.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="{{asset('index-assets/')}}/svg/iroom-logo.svg">
    <link rel="icon" type="image/png" sizes="16x16" href="{{asset('index-assets/')}}/svg/iroom-logo.svg">
    <link rel="manifest" href="{{asset('index-assets/')}}/webmanifest/site.webmanifest">
    <link rel="mask-icon" href="{{asset('index-assets/')}}/svg/iroom-logo.svg" color="#3b49dc">
    <link rel="shortcut icon" href="{{asset('index-assets/')}}/svg/iroom-logo.svg">
    <meta name="msapplication-TileColor" content="#080032">
    <meta name="msapplication-config" content="{{asset('index-assets/')}}/xml/browserconfig.xml">
    <meta name="theme-color" content="#4044ee">
{{--    <link rel="manifest" href="{{ asset('/manifest.json') }}?ver={{config('app.app_version')}}">--}}

    @yield('meta')
    @livewireStyles
    @stack('style')

    <!-- Google Tag Manager -->
</head>



@yield('header')
<!-- Body -->

    @yield('content')

@yield('footer')


@livewireScripts

<script>
    function copyText() {
        // Get the text field
        var copyText = document.getElementById("meetingCode");

        // Select the text field
        copyText.select();
        copyText.setSelectionRange(0, 99999); // For mobile devices

        // Copy the text inside the text field
        navigator.clipboard.writeText(copyText.value);

        // Alert the copied text
        alert("با موفقیت کپی شد");
    }
    function openGoogleMeet(meetingCode) {

// Check if the user is on a mobile device
        var isMobile = /Android|webOS|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        if (isMobile) {
            // Open the Google Meet app with the meeting code
            window.location.href = "intent://meet.google.com/" + meetingCode + "#Intent;scheme=https;package=com.google.android.apps.tachyon;end";
        } else {
            // Open the Google Meet website with the meeting code
            window.open("https://meet.google.com/" + meetingCode, "_blank");
        }

    }
</script>

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-V23DVKTK4D"></script>
<script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-V23DVKTK4D');
</script>
</body>
</html>
