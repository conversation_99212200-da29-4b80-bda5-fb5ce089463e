<!DOCTYPE html>
<html lang="fa">
<head>
    <meta charset="utf-8">
    @if(isset($pageTitle))
        <title>{{$pageTitle}}</title>
    @else
        <title>آی روم</title>
    @endif
    <!-- SEO Meta Tags -->
    <meta name="description"
          content="با ما میتوانید نسخه تجاری و بدون محدودیت گوگل میت رو به همراه امکانات ویژه تر داشته باشید و بدون دغدغهء اختلال از یک سرویس باکیفیت جهانی استفاده کنید.">
    <meta name="author" content="Hamed">
    <meta property="og:title" content="جلسات خود را با گوگل میت پرو و بدون محدودت برگزار کنید">
    <meta property="og:description"
          content="با ما میتوانید نسخه تجاری و بدون محدودیت گوگل میت رو به همراه امکانات ویژه تر داشته باشید و بدون دغدغهء اختلال از یک سرویس باکیفیت جهانی استفاده کنید.">
    <meta property="og:url" content="https://iroom.live/">
    <meta property="og:type" content="product">
    <meta property="og:site_name" content="آی روم">
    <meta property="og:locale" content="fa-IR">
    <meta property="og:image" content="https://iroom.live/index-assets/png/iroom-creator-min.png">
    <meta property="og:image_width" content="1200">
    <meta property="og:image_height" content="787">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="جلسات خود را با گوگل میت پرو و بدون محدودت برگزار کنید">
    <meta name="twitter:description"
          content="با iroom می تونید نسخه تجاری و بدون محدودیت گوگل میت رو به همراه امکانات ویژه تر داشته باشید و بدون دغدغهء اختلال و اشکالات فنی از یک سرویس مطمئن و باکیفیت جهانی استفاده کنید.">
    <meta name="twitter:image" content="https://iroom.live/index-assets/png/iroom-creator-min.png">

    <!-- Viewport -->
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- Favicon and Touch Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="{{asset('index-assets/')}}/svg/iroom-logo.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="{{asset('index-assets/')}}/svg/iroom-logo.svg">
    <link rel="icon" type="image/png" sizes="16x16" href="{{asset('index-assets/')}}/svg/iroom-logo.svg">
    <link rel="manifest" href="{{asset('index-assets/')}}/webmanifest/site.webmanifest">
    <link rel="mask-icon" href="{{asset('index-assets/')}}/svg/iroom-logo.svg" color="#3b49dc">
    <link rel="shortcut icon" href="{{asset('index-assets/')}}/svg/iroom-logo.svg">
    <meta name="msapplication-TileColor" content="#080032">
    <meta name="msapplication-config" content="{{asset('index-assets/')}}/xml/browserconfig.xml">
    <meta name="theme-color" content="#4044ee">
    <link rel="manifest" href="{{ asset('/manifest.json') }}?ver={{config('app.app_version')}}">
    @yield('meta')
    <!-- Vendor Styles -->
    <link rel="stylesheet" media="screen" href="{{asset('index-assets/')}}/css/boxicons.min.css"/>
    <link rel="stylesheet" media="screen" href="{{asset('index-assets/')}}/css/swiper-bundle.min.css"/>

    <!-- Main Theme Styles + Bootstrap -->
    <link rel="stylesheet" media="screen" href="{{asset('index-assets/')}}/css/theme.min.css">
    <link rel="stylesheet" media="screen"
          href="{{asset('index-assets/')}}/css/theme-chanaged.css?ver={{config('app.app_version')}}">
    @livewireStyles
    <!-- Page loading styles -->
    <style>
        li.nav-item.dropdown a.dropdown-item {
            text-align: right;
        }

        .logo-list img {
            height: 67px;
        }

        .page-loading {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            -webkit-transition: all .4s .2s ease-in-out;
            transition: all .4s .2s ease-in-out;
            background-color: #fff;
            opacity: 0;
            visibility: hidden;
            z-index: 9999;
        }

        .dark-mode .page-loading {
            background-color: #0b0f19;
        }

        .page-loading.active {
            opacity: 1;
            visibility: visible;
        }

        .page-loading-inner {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            text-align: center;
            -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
            -webkit-transition: opacity .2s ease-in-out;
            transition: opacity .2s ease-in-out;
            opacity: 0;
        }

        .page-loading.active > .page-loading-inner {
            opacity: 1;
        }

        .page-loading-inner > span {
            display: block;
            font-size: 1rem;
            font-weight: normal;
            color: #9397ad;
        }

        .dark-mode .page-loading-inner > span {
            color: #fff;
            opacity: .6;
        }

        .page-spinner {
            display: inline-block;
            width: 2.75rem;
            height: 2.75rem;
            margin-bottom: .75rem;
            vertical-align: text-bottom;
            border: .15em solid #b4b7c9;
            border-right-color: transparent;
            border-radius: 50%;
            -webkit-animation: spinner .75s linear infinite;
            animation: spinner .75s linear infinite;
        }

        .dark-mode .page-spinner {
            border-color: rgba(255, 255, 255, .4);
            border-right-color: transparent;
        }

        @-webkit-keyframes spinner {
            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }

        @keyframes spinner {
            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }
    </style>

    <!-- Theme mode -->
    <script>
        let mode = window.localStorage.getItem('mode'),
            root = document.getElementsByTagName('html')[0];
        if (mode !== null && mode === 'dark') {
            root.classList.add('dark-mode');
        } else {
            root.classList.remove('dark-mode');
        }
    </script>
    <script type="text/javascript">
        (function (c, l, a, r, i, t, y) {
            c[a] = c[a] || function () {
                (c[a].q = c[a].q || []).push(arguments)
            };
            t = l.createElement(r);
            t.async = 1;
            t.src = "https://www.clarity.ms/tag/" + i;
            y = l.getElementsByTagName(r)[0];
            y.parentNode.insertBefore(t, y);
        })(window, document, "clarity", "script", "okfe6f79ry");
    </script>
    <!-- Page loading scripts -->
    {{--    <script>--}}
    {{--        (function () {--}}
    {{--            window.onload = function () {--}}
    {{--                const preloader = document.querySelector('.page-loading');--}}
    {{--                preloader.classList.remove('active');--}}
    {{--                setTimeout(function () {--}}
    {{--                    preloader.remove();--}}
    {{--                }, 1000);--}}
    {{--            };--}}
    {{--        })();--}}
    {{--    </script>--}}

    <!-- Google Tag Manager -->
</head>


<!-- Body -->
<body>
<div
    style="width:100vw;min-height:100vh;position:fixed;z-index: -1;display:flex;justify-content:center;padding:120px 24px 160px 24px;pointer-events:none">
    <div
        style="background:radial-gradient(circle, rgba(2, 0, 36, 0) 0, #fafafa 100%);position:absolute;content:&quot;&quot;;z-index:2;width:100%;height:100%;top:0"></div>
    <div
        style="content:&quot;&quot;;background-image:url({{asset('index-assets/svg/grid.svg')}});z-index:1;position:absolute;width:100%;height:100%;top:0;opacity:0.4;filter:invert(1)"></div>
    <div style="z-index:3;width:100%;max-width:640px;background-image:radial-gradient(at 27% 37%, hsla(215, 98%, 61%, 1) 0px, transparent 0%),
                      radial-gradient(at 97% 21%, hsla(125, 98%, 72%, 1) 0px, transparent 50%),
                      radial-gradient(at 52% 99%, hsla(354, 98%, 61%, 1) 0px, transparent 50%),
                      radial-gradient(at 10% 29%, hsla(256, 96%, 67%, 1) 0px, transparent 50%),
                      radial-gradient(at 97% 96%, hsla(38, 60%, 74%, 1) 0px, transparent 50%),
                      radial-gradient(at 33% 50%, hsla(222, 67%, 73%, 1) 0px, transparent 50%),
                      radial-gradient(at 79% 53%, hsla(343, 68%, 79%, 1) 0px, transparent 50%);position:absolute;height:100%;filter:blur(100px) saturate(150%);top:80px;opacity:0.15"></div>
</div>
@yield('header')
<main class="page-wrapper">
    @yield('content')
</main>

@yield('footer')


<!-- Back to top button -->
<a href="#top" class="btn-scroll-top" data-scroll>
    <span class="btn-scroll-top-tooltip text-muted fs-sm me-2">Top</span>
    <i class="btn-scroll-top-icon bx bx-chevron-up"></i>
</a>

{{--<script src="{{asset('index-assets/lottie/dottie-player.js')}}" defer async></script>--}}

<script src="https://unpkg.com/@dotlottie/player-component@latest/dist/dotlottie-player.mjs" type="module"></script>

<!-- Vendor Scripts -->
<script src="{{asset('index-assets/')}}/js/bootstrap.bundle.min.js" sync></script>
<script src="{{asset('index-assets/')}}/js/smooth-scroll.polyfills.min.js" defer></script>
{{--<script src="{{asset('index-assets/')}}/js/parallax.min.js" defer></script>--}}
{{--<script src="{{asset('index-assets/')}}/js/jarallax.min.js" defer></script>--}}
<script src="{{asset('index-assets/')}}/js/rellax.min.js" sync></script>
<script src="{{asset('index-assets/')}}/js/swiper-bundle.min.js" async></script>

<!-- Main Theme Script -->
<script src="{{asset('index-assets/')}}/js/theme.min.js?ver={{config('app.app_version')}}" defer></script>

@livewireScripts

<script src="{{ asset('/sw.js') }}" defer></script>
<script>
    if (typeof navigator.serviceWorker !== 'undefined') {
        navigator.serviceWorker.register('sw.js')
    }
</script>
<!-- Google tag (gtag.js) -->
<script defer src="https://www.googletagmanager.com/gtag/js?id=G-MWV5102MN9"></script>


</body>
</html>
