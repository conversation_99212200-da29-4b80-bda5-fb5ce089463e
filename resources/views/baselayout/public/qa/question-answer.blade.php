@extends('layouts.public')

@section('content')
<div class="d-flex flex-column flex-column-fluid direction-rtl">
	<!--begin::toolbar-->
	<div class="toolbar" id="kt_toolbar">
		<div class="container d-flex flex-stack flex-wrap flex-sm-nowrap">
			<!--begin::Info-->
			<div class="d-flex flex-column align-items-start justify-content-center flex-wrap me-1">
				<!--begin::Title-->
				<h3 class="text-dark fw-bolder my-1">پرسش و پاسخ</h3>
				<!--end::Title-->
				<!--begin::Breadcrumb-->
				<ul class="breadcrumb breadcrumb-line bg-transparent text-muted fw-bold p-0 my-1 fs-7">
					<li class="breadcrumb-item">
						<a href="{{route('dashboard')}}" class="text-muted text-hover-primary">خانه</a>
					</li>
					<li class="breadcrumb-item">سوالات</li>
					<li class="breadcrumb-item text-dark">پرسش و پاسخ</li>
				</ul>
				<!--end::Breadcrumb-->
			</div>
			<!--end::Info-->
			<!--begin::Nav-->
			<div class="d-flex align-items-center flex-nowrap text-nowrap overflow-auto py-1">
				<a href="faq.html" class="btn btn-active-accent active fw-bolder">تمام سوالات</a>
				<a href="pricing.html" class="btn btn-active-accent fw-bolder ms-3">دسته بندی</a>
				<a href="invoice.html" class="btn btn-active-accent fw-bolder ms-3">بهترین سوالات</a>
				<a href="login.html" class="btn btn-active-accent fw-bolder ms-3">برترین پرسشگران</a>
				<a href="wizard.html" class="btn btn-active-accent fw-bolder ms-3">برترین پاسخ دهندگان</a>
				<a href="{{route('questions.create')}}" class="btn btn-active-accent fw-bolder ms-3">پرسش سوال</a>
			</div>
			<!--end::Nav-->
		</div>
	</div>
	<!--end::toolbar-->
	<!--begin::Content-->
	<div class="content fs-6 d-flex flex-column-fluid pb-14" id="kt_content">
		<!--begin::Container-->
		<div class="container">
			<!--begin::Page Layout-->
			<div class="d-flex flex-column flex-md-row">
				<!--begin::Aside-->
				@include("layouts.components.qasidebar")
				<!--end::Aside-->
				<!--begin::Layout-->
				<div class="flex-md-row-fluid ms-md-12 position-relative">
                    @forelse($questions as $question)
					<!--begin::Card-->
					<div class="card mb-5">
						<div class="direction-rtl d-flex align-items-center bg-hover-light card-px py-3" data-inbox="message">
							<!--begin::Toolbar-->
							<div class="d-flex align-items-center">
								<!--begin::Actions-->
								<div class="d-flex align-items-center me-3" data-inbox="actions">
									<span class="btn btn-icon btn-sm active" data-bs-toggle="tooltip" data-bs-placement="right" title="" data-bs-original-title="پاسخ داده شده">
										{{$question->count_answer()}}
										<i class="ms-1 bi bi-chat-fill"></i>
									</span>
									<span class="btn btn-icon btn-sm" data-bs-toggle="tooltip" data-bs-placement="right" title="" data-bs-original-title="دیده شده">
										{{$question->views}}
										<i class="ms-1 bi bi-eye-fill"></i>
									</span>
								</div>
								<!--end::Actions-->
								<!--begin::Author-->
								<div class="d-flex align-items-center flex-wrap w-xxl-200px me-3" data-bs-toggle="view">
									<!--begin::Symbol-->
									<div class="symbol symbol-40px me-4">
										<span class="symbol-label bg-light">
											<img src="{{$question->user->profile_photo ?? ""}}" class="rounded h-100 align-self-end" alt="">
										</span>
									</div>
									<!--end::Symbol-->
									<a href="#" class="fw-bold text-gray-800 text-hover-primary">{{$question->user->full_name ?? ""}}</a>
								</div>
								<!--end::Author-->
							</div>
							<!--end::Toolbar-->
							<!--begin::Info-->
							<div class="flex-grow-1 mt-2 me-2 lh-xl" data-bs-toggle="view">
								<div>
                                    <a href="{{route('questions.show_with_slug',([$question->id,$question->slug]))}}">
                                        <span class="fw-bolder fs-6 me-2 btn-color-dark">{{$question->title}}</span>
                                        <span class="text-muted">
                                            {{Str::limit(( strip_tags(html_entity_decode($question->content))),70) }}
                                    </span>
                                    </a>

								</div>
								<div class="mt-4">
									<span class="badge badge-light-primary me-1">{{$question->statusName()}}</span>
									<span class="badge badge-light-info">جاوا اسکریپت</span>
								</div>
							</div>
							<!--end::Info-->
							<!--begin::Datetime-->
							<div class="mt-2 me-3 fw-bolder text-center" data-bs-toggle="view">
                                {{jdate($question->created_at)->format('Y/m/d')}}
                                <br/>
                                {{jdate($question->created_at)->format('H:i')}}
                            </div>
							<!--end::Datetime-->
						</div>
					</div>
					<!--end::Card-->

                    @empty
                        هیچ سوالی موجود نیست
                    @endforelse
                    {{$questions->links()}}

				</div>
				<!--end::Layout-->
			</div>
			<!--end::Page Layout-->
		</div>
		<!--end::Container-->
	</div>
	<!--end::Content-->
</div>
@endsection

@push('scripts')
<script src={{ asset('assets/js/custom/widgets.js') }}></script>
@endpush
