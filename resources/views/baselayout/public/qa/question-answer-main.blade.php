@extends('layouts.public')

@section('title')
    {{-- TODO: question title --}}
@endsection

@push('header_style_after')
    <link rel="stylesheet" href="{{ asset('assets/plugins/custom/prismjs/prismjs.bundle.css') }}">
@endpush


@section('content')
    <div class="d-flex flex-column flex-column-fluid direction-rtl">
        <!--begin::toolbar-->
        <div class="toolbar" id="kt_toolbar">
            <div class="container d-flex flex-stack flex-wrap flex-sm-nowrap">
                <!--begin::Info-->
                <div class="d-flex flex-column align-items-start justify-content-center flex-wrap me-1">
                    <!--begin::Title-->
                    <h3 class="text-dark fw-bolder my-1">پرسش و پاسخ</h3>
                    <!--end::Title-->
                    <!--begin::Breadcrumb-->
                    <ul class="breadcrumb breadcrumb-line bg-transparent text-muted fw-bold p-0 my-1 fs-7">
                        <li class="breadcrumb-item">
                            <a href="{{route('dashboard')}}" class="text-muted text-hover-primary">خانه</a>
                        </li>
                        <li class="breadcrumb-item">سوالات</li>
                        <li class="breadcrumb-item text-dark">پرسش و پاسخ</li>
                    </ul>
                    <!--end::Breadcrumb-->
                </div>
                <!--end::Info-->
                <!--begin::Nav-->
                <div class="d-flex align-items-center flex-nowrap text-nowrap overflow-auto py-1">
                    <a href="faq.html" class="btn btn-active-accent active fw-bolder">تمام سوالات</a>
                    <a href="pricing.html" class="btn btn-active-accent fw-bolder ms-3">دسته بندی</a>
                    <a href="invoice.html" class="btn btn-active-accent fw-bolder ms-3">بهترین سوالات</a>
                    <a href="login.html" class="btn btn-active-accent fw-bolder ms-3">برترین پرسشگران</a>
                    <a href="wizard.html" class="btn btn-active-accent fw-bolder ms-3">برترین پاسخ دهندگان</a>
                    <a href="{{route('questions.create')}}" class="btn btn-active-accent fw-bolder ms-3">پرسش سوال</a>
                </div>
                <!--end::Nav-->
            </div>
        </div>
        <!--end::toolbar-->
        <!--begin::Content-->
        <div class="content fs-6 d-flex flex-column-fluid pb-14" id="kt_content">
            <!--begin::Container-->
            <div class="container">
                <!--begin::Page Layout-->
                <div class="d-flex flex-column flex-md-row">
                    <!--begin::Aside-->
                @include("layouts.components.qasidebar")
                <!--end::Aside-->
                    <!--begin::Layout-->
                    <div class="flex-md-row-fluid ms-md-12 position-relative">

                        <!--begin::question-->
                        <div class="question">
                            <!--begin::Card-->
                            <div class="card mb-5">
                                <!--begin::Header-->
                                <div class="card-header align-items-center flex-wrap justify-content-between">
                                    <!--begin::Left-->
                                    <div class="d-flex align-items-center">
                                        <div class="my-2">
                                            <span
                                                class="badge badge-light-primary me-1">{{$question->statusName()}}</span>
                                            <span class="badge badge-light-info">جاوا اسکریپت</span>
                                        </div>
                                    </div>
                                    <!--end::Left-->
                                    <!--begin::Right-->
                                    @if (Auth::check())
                                        <div class="d-flex align-items-center justify-content-end text-end my-2">
                                            <button class="btn btn-icon btn-active-light-primary btn-sm ms-2"
                                                    data-bs-toggle="modal" data-bs-container="body"
                                                    data-bs-target="#kt_modal_1">
                                                <i class="fas fa-reply fs-6"></i>
                                            </button>
                                        </div>
                                        <livewire:question.question-answer-component
                                            :data="['question_id' => $question->id,'title' => $question->title]"/>
                                @endif
                                <!--end::Right-->
                                </div>
                                <!--end::Header-->
                                <!--begin::Body-->
                                <div class="card-body">
                                    <!--begin::Header-->
                                    <div class="d-flex flex-stack flex-wrap py-5">
                                        <!--begin::Title-->
                                        <div class="d-flex align-items-center flex-wrap me-2 py-2">
                                            <div class="fw-bold fs-2 me-3">{{$question->title}}</div>
                                        </div>
                                        <!--end::Title-->
                                        <!--begin::Toolbar-->
                                        <livewire:question.vote-component
                                            :data="['vote_id' => $question->id, 'vote_category' => 'QUESTION_VOTE' ]"/>
                                        <!--end::Toolbar-->
                                    </div>
                                    <!--end::Header-->
                                    <!--begin::Message-->
                                    <div class="mb-3">
                                        <div
                                            class="d-flex py-6 flex-column flex-md-row flex-lg-column flex-xxl-row justify-content-between">
                                            <div class="d-flex align-items-center">
                                                <!--begin::Symbol-->
                                                <div class="symbol symbol-40px me-4">
												<span class="symbol-label bg-light">
													<img src="{{$question->user->profile_photo}}"
                                                         class="rounded h-100 align-self-end" alt="">
												</span>
                                                </div>
                                                <!--end::Symbol-->
                                                <div class="d-flex flex-column flex-grow-1 flex-wrap me-2">
                                                    <div class="d-flex align-items-center flex-wrap">
                                                        <a href="#"
                                                           class="fs-6 fw-bolder text-gray-800 text-hover-primary me-2">{{$question->user->full_name}}</a>
                                                        <div class="fw-bold fs-7 text-muted">
                                                            <span
                                                                class="bullet bullet-dot bg-primary w-6px h-6px me-2"></span>{{ $question->ago_time() }}
                                                        </div>
                                                        @if(auth()->check() && $question->user_id == auth()->user()->id)
                                                            <livewire:question.edit-question :question="$question->id"
                                                                                           :wire:key="'user-edit-one-'.$question->id"/>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div
                                                class="d-flex my-2 my-xxl-0 align-items-md-center align-items-lg-start align-items-xxl-center flex-column flex-md-row flex-lg-column flex-xxl-row">
                                                <div class="fw-bold text-muted mx-2">آخرین
                                                    آپدیت: {{jdate($question->updated_at)->format('Y/m/d H:i')}}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="py-3 lh-xl">
                                            <livewire:question.question-content :question="$question->id"
                                                                                :wire:key="'show-content-'.$question->id"/>
                                        </div>
                                    </div>
                                    <!--end::Message-->
                                </div>
                                <!--end::Body-->
                            </div>
                            <!--end::Card-->
                        </div>
                        <!--end::question-->
                        <div class="d-flex flex-row mb-3 justify-content-evenly">
                            <b>مرتب بر اساس: </b>
                            <a href="{{Request::url()}}?sort_by=vote" {{ ($sort_by=='vote') ? "active" : ""  }}>بیشترین امتیاز</a>
                            <a href="{{Request::url()}}?sort_by=oldest" {{ ($sort_by=='oldest') ? "active" : ""  }}>قدیمی ها</a>
                            <a href="{{Request::url()}}?sort_by=newest"  {{ ($sort_by=='newest' || empty($sort_by)) ? "active" : ""  }}>جدیدترین ها</a>

                        </div>
                        <!--begin::answers-->
                        <div class="answers">
                            <livewire:question.show-answers
                                :data="['question_id' => $question->id,'creator_id'=> $question->user_id ]"/>


                        </div>
                        <!--begin::Pagination-->

                        <!--end::Pagination-->
                    </div>
                    <!--end::Layout-->
                </div>
                <!--end::Page Layout-->
            </div>
            <!--end::Container-->
        </div>
        <!--end::Content-->
    </div>

    <livewire:report.report-component :myurl="['url'   => Request::path()]" />
@endsection



@push('scripts')
    <script src={{ asset('assets/js/custom/widgets.js') }}></script>

    <script src={{ asset('assets/js/custom/documentation/documentation.js') }}></script>
    <script src={{ asset('assets/plugins/custom/prismjs/prismjs.bundle.js') }}></script>

    <script src="{{ asset('assets/js/tinymce/tinymce.min.js') }}"></script>
    <script>
         Livewire.on('open_answer_component', (param) => {
            if (param['kind'] == "down") {
                $('#answer_comment_' + param['id']).slideDown(300);
            } else {
                $('#answer_comment_' + param['id']).slideUp(300);
            }

        });
    </script>

@endpush

