<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', fn(Request $request) => $request->user());

//Route::post('/start-bot-from-ext', function (Request $request) {
//    return response($request->toArray(),200);
//    dd($request->toArray());
//});
//
//Route::post('/stop-bot-from-ext', function (Request $request) {
//    return response([],200);
//    dd($request->toArray());
//});
//
//Route::post('/pause-bot-from-ext', function (Request $request) {
//    return response([],200);
//    dd($request->toArray());
//});
//
//Route::post('/add-new-message', function (Request $request) {
//    return response([],200);
//    dd($request->toArray());
//});
//
//Route::post('/resume-bot-from-ext', function (Request $request) {
//    return response([],200);
//    dd($request->toArray());
//});
//
//
//Route::post('/api/get-notes', function (Request $request) {
//    $response =[
//        [
//            'text' => 'hamed',
//            'timestamp' => $request->timestamp,
//        ],
//
//        [
//            'text' => 'hamed2',
//            'timestamp' => $request->timestamp,
//        ]
//    ];
//    return response($response,200);
//});
