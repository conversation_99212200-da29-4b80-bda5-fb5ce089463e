APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravelv8
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"



MAIL_FETCH_HOST=smtp.yourmail.com
MAIL_FETCH_PORT=110
MAIL_FETCH_USERNAME=<EMAIL>
MAIL_FETCH_PASSWORD=secret-password
MAIL_FETCH_OPTIONS=pop3
MAIL_FETCH_USE_SSL=true

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

ARVAN_ACCESS_KEY_ID=
ARVAN_SECRET_ACCESS_KEY=
ARVAN_DEFAULT_REGION=iran
ARVAN_BUCKET=testmyhamed
ARVAN_ENDPOINT=https://s3.ir-thr-at1.arvanstorage.com
ARVAN_URL=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"


KAVENEGAR_API_KEY=47704C594134564E56465043527A646633306565334B46355458537467464564
KAVENEGAR_SENDER=1000596446

LIVEWIRE_URL = http://127.0.0.1:8000
PROFILE_PHOTO_SIZE = 200
TICKET_FILE_SIZE = 2048

SCOUT_DRIVER=meilisearch
SCOUT_QUEUE=true
MEILISEARCH_HOST=http://127.0.0.1:7700
MEILISEARCH_KEY=masterKey


