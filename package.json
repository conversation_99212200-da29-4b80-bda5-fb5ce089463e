{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "devDependencies": {"@tailwindcss/forms": "^0.2.1", "alpinejs": "^2.7.3", "autoprefixer": "^10.1.0", "axios": "^0.21", "laravel-mix": "^6.0.6", "lodash": "^4.17.19", "postcss": "^8.1.14", "postcss-import": "^12.0.1", "tailwindcss": "^2.1.1", "@babel/core": "^7.13.14", "@babel/plugin-transform-modules-commonjs": "^7.13.8", "@babel/preset-env": "^7.13.12", "@babel/register": "^7.13.14", "copy-webpack-plugin": "^8.1.0", "css-loader": "^5.2.0", "css-minimizer-webpack-plugin": "^1.3.0", "del": "^6.0.0", "extract-loader": "^5.1.0", "file-loader": "^6.2.0", "fs-extra": "^10.0.0", "gulp": "^4.0.2", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-connect": "^5.7.0", "gulp-dart-sass": "^1.0.2", "gulp-if": "^3.0.0", "gulp-rename": "^2.0.0", "gulp-rewrite-css": "^1.1.2", "gulp-rtlcss": "^1.4.1", "gulp-sourcemaps": "^3.0.0", "gulp-terser": "^2.0.1", "imports-loader": "^1.2.0", "lazypipe": "^1.0.2", "merge-stream": "^2.0.0", "mini-css-extract-plugin": "^1.3.4", "postcss-loader": "^4.0.4", "pretty": "^2.0.0", "replace-in-file-webpack-plugin": "^1.0.6", "rtlcss-webpack-plugin": "^4.0.6", "sass-loader": "^10.1.0", "script-loader": "^0.7.2", "terser-webpack-plugin": "^5.0.3", "url-loader": "^4.1.1", "webpack": "^5.28.0", "webpack-cli": "^4.6.0", "webpack-dev-server": "^3.11.2", "webpack-exclude-assets-plugin": "^0.1.1", "webpack-merge-and-include-globally": "^2.3.4", "webpack-messages": "^2.0.4", "yargs": "^16.2.0", "yarn-install": "^1.0.0"}, "dependencies": {"datatables.net-bs4": "1.10.22", "datatables.net-buttons-bs4": "^1.6.3", "@amcharts/amcharts4": "^4.10.18", "@ckeditor/ckeditor5-alignment": "^23.1.0", "@ckeditor/ckeditor5-build-balloon": "^23.1.0", "@ckeditor/ckeditor5-build-balloon-block": "^23.1.0", "@ckeditor/ckeditor5-build-classic": "^23.1.0", "@ckeditor/ckeditor5-build-decoupled-document": "^23.1.0", "@ckeditor/ckeditor5-build-inline": "^23.1.0", "@fortawesome/fontawesome-free": "^5.15.1", "@popperjs/core": "^2.9.1", "@shopify/draggable": "^1.0.0-beta.8", "@yaireo/tagify": "^3.23.1", "acorn": "^8.0.4", "apexcharts": "3.23.1", "autosize": "^4.0.2", "axios": "^0.21.1", "bootstrap": "5.0.1", "bootstrap-daterangepicker": "^3.1.0", "bootstrap-icons": "^1.2.1", "bootstrap-maxlength": "^1.10.0", "bootstrap-multiselectsplitter": "^1.0.4", "chalk": "^4.1.0", "chart.js": "^3.2.1", "clipboard": "^2.0.4", "countup.js": "^2.0.7", "datatables.net": "^1.10.22", "datatables.net-autofill-bs4": "^2.3.5", "datatables.net-colreorder-bs4": "^1.5.2", "datatables.net-datetime": "^1.0.2", "datatables.net-editor": "^2.0.2", "datatables.net-fixedcolumns-bs4": "^3.3.2", "datatables.net-fixedheader-bs4": "^3.1.7", "datatables.net-keytable-bs4": "^2.5.3", "datatables.net-responsive-bs4": "^2.2.6", "datatables.net-rowgroup-bs4": "^1.1.2", "datatables.net-rowreorder-bs4": "^1.2.7", "datatables.net-scroller-bs4": "^2.0.3", "datatables.net-select-bs4": "^1.3.1", "dropzone": "^5.7.2", "es6-promise": "^4.2.8", "es6-promise-polyfill": "^1.2.0", "es6-shim": "^0.35.5", "esri-leaflet": "^2.3.3", "esri-leaflet-geocoder": "^2.3.2", "flatpickr": "^4.6.6", "fslightbox": "^3.2.3", "inputmask": "^5.0.5", "jquery": "3.5.1", "jszip": "^3.5.0", "leaflet": "^1.7.1", "line-awesome": "^1.3.0", "moment": "^2.29.1", "nouislider": "^14.6.3", "npm": "^7.5.3", "pdfmake": "^0.1.68", "prism-themes": "^1.5.0", "prismjs": "^1.22.0", "quill": "^1.3.7", "select2": "^4.1.0-beta.1", "smooth-scroll": "^16.1.3", "sweetalert2": "^10.10.0", "tiny-slider": "^2.9.3", "tinymce": "^5.5.1", "toastr": "^2.1.4", "typed.js": "^2.0.12", "webpack-rtl-plugin": "^2.0.0", "wnumb": "^1.1.0"}}