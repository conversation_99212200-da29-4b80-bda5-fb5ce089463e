<?php

namespace App\Livewire\AIBot;

use App\Data\Attendee\BotChatMessageRequestData;
use App\Data\Attendee\CreateBotRequestData;
use App\Data\Attendee\Settings\RecordingSettingsData;
use App\Data\Attendee\Settings\Transcription\MeetingClosedCaptionsSettingsData;
use App\Data\Attendee\Settings\Transcription\TranscriptionSettingsData;
use App\Enums\AIBot\MeetingBotStatusEnum;
use App\Models\AI\ArtificialIntelligence;
use App\Models\Attendee\MeetBot;
use App\Services\AI\ArtificialIntelligenceService;
use App\Services\AI\AvalAIAPIClient;
use App\Services\Attendee\AttendeeBotService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Livewire\Attributes\Url;
use Livewire\Component;

class AIBotMain extends Component
{
    public $meetingLink = '';
    public $meetingLang = 'fa-IR';
    public $meetingList = [];
    public $messages = [];
    public $chatActive = true;
    public $canProcess = false;
    #[Url]
    public $selectedBot = null;
    public $selectedBotTitle = null;
    public $selectedBotPublicLink = null;
    public $selectedBotStatus = null;

    public $readyForAI = null;

    public $postProcessResult = null;

    public function render()
    {
        $this->postProcessResult = null;
        $this->getMeetingList();
        if (!empty($this->selectedBot)) {
            $this->updateMeetingData($this->selectedBot);
        }
        return view('livewire.a-i-bot.a-i-bot-main');
    }


    public function selectMeeting($attendeeServiceBotId)
    {
        $this->postProcessResult = null;
        $this->chatActive = true;
        $this->canProcess = false;
        $this->updateMeetingData($attendeeServiceBotId);
        $this->selectedBot = $attendeeServiceBotId;
    }

    public function refreshMessages()
    {
        $this->updateMeetingData($this->selectedBot);
    }


    public function createMeeting()
    {
        $user = auth()->user();
        $botName = 'IROOM.LIVE AI BOT';
        $ulid = Str::ulid();
        $route = route('bot.public.show', ['token' => $ulid]);
        $link = $this->meetingLink;
        $startMessage = 'سلام. من هوش مصنوعی آی روم هستم و اینجام تا جلسه شمارا یادداشت برداری و خلاصه کنم. جهت مشاهده محتوای جلسه و نتیجه پردازش میتوانید از لینک زیر استفاده کنید:' . ' ' . $route;
        $persianLang = MeetingClosedCaptionsSettingsData::from([
            'google_meet_language' => 'fa-IR',
            'merge_consecutive_captions' => true,
        ])->toArrayWithoutNull();

        $transcriptSetting = TranscriptionSettingsData::from([
            'meeting_closed_captions' => $persianLang,
        ])->toArrayWithoutNull();

        $data = CreateBotRequestData::from([
            'meeting_url' => $link,
            'bot_name' => $botName,
            'transcription_settings' => $transcriptSetting,
            'bot_chat_message' => BotChatMessageRequestData::from([
                'message' => $startMessage,
            ])->toArrayWithoutNull(),
            'recording_settings' => RecordingSettingsData::from([
                'format' => 'none',
            ])->toArrayWithoutNull(),
        ]);

        $attendeeBotService = app(AttendeeBotService::class);
        $bot = $attendeeBotService->createBot($data);
        $iroomBot = MeetBot::updateOrCreate([
            'user_id' => $user->id,
            'unique_id' => $ulid,
        ], [
            'user_id' => $user->id,
            'unique_id' => $ulid,
            'access_link' => $link,
            'status' => 10,
            'attendee_service_bot_id' => $bot->id,
        ]);
        $this->chatActive = true;
        $this->selectedBot = $iroomBot->attendee_service_bot_id;
        $this->dispatch('close_modal');

        $this->render();
    }

    private function getMeetingList()
    {
        $user = auth()->user();
        $this->meetingList = MeetBot::where('user_id', $user->id)
            ->with(['artificialIntelligence'])
            ->select(['id', 'attendee_service_bot_id', 'title', 'description', 'status', 'created_at'])
            ->orderByDesc('created_at')
            ->get();
    }

    private function updateMeetingData($attendeeServiceBotId)
    {
        $user = auth()->user();
        $meetingBot = MeetBot::where('attendee_service_bot_id', $attendeeServiceBotId)
            ->where('user_id', $user->id)
            ->firstOrFail();

        $this->selectedBotStatus = $meetingBot->status;
        if ($meetingBot->status == MeetingBotStatusEnum::ENDED->value) {
            $this->canProcess = true;
        }
        $this->selectedBotTitle = $meetingBot->title;
        $this->selectedBotPublicLink = route('bot.public.show', ['token' => $meetingBot->unique_id]);

        /** @var AttendeeBotService $attendeeBotService */
        $attendeeBotService = app(AttendeeBotService::class);
        $messages = $attendeeBotService->getTranscriptAndSave($meetingBot);
        if (in_array($meetingBot->status, MeetingBotStatusEnum::endedStatus())) {
            $this->chatActive = false;
        }

        $messages = $attendeeBotService->mergeSameChats($messages);
        $aiService = ArtificialIntelligenceService::toText(collect($messages));

        $postProcess = ArtificialIntelligence::where('meet_bot_id', $meetingBot->id)->get();
        if (!empty($postProcess)) {
            foreach ($postProcess as $result) {
                $this->postProcessResult[] = $result->message_json;
            }
        }

        $this->messages = $messages;
        $this->readyForAI = $aiService;
        $this->dispatch('chat-updated');
    }

    public function postProcess()
    {
        $user = auth()->user();
        $meetingBot = MeetBot::where('attendee_service_bot_id', $this->selectedBot)
            ->where('user_id', $user->id)
            ->firstOrFail();
        $avalAI = app(AvalAIAPIClient::class);
        $model = 'gpt-4o';
        $model = 'o3';
        $model = 'gemini-2.5-flash';
        /** @var ArtificialIntelligenceService $artificialIntelligenceService */
        $artificialIntelligenceService = app(ArtificialIntelligenceService::class);
        try {
            $result = Cache::rememberForever('post_process_' . $model . '_' . $meetingBot->id, function () use ($avalAI, $model) {
                $prompt = ArtificialIntelligenceService::buildPrompt(collect($this->messages));
                $result = $avalAI->chat($prompt, $model);

                if (!($result['success'] ?? false)) {
                    throw new \Exception($result['error'] ?? 'خطا در فراخوانی سرویس هوش مصنوعی.');
                }

                return $result;
            });
        } catch (\Throwable $e) {
            if (!($result['success'] ?? false)) {
                $this->dispatch('alert', ['type' => 'error', 'message' => 'خطا در فراخوانی سرویس هوش مصنوعی.']);
                return;
            }
        }

        $AIResponse = $artificialIntelligenceService->saveAIResponse($user, $meetingBot, $result);

        $this->postProcessResult = $AIResponse->message_json;
    }
}
