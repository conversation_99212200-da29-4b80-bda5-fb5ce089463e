{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "ext-json": "*", "ext-soap": "*", "algolia/algoliasearch-client-php": "*", "archtechx/enums": "^0.3.2", "bavix/laravel-wallet": "^10.1", "chefhasteeth/pipeline": "^1.0", "coderello/nova-login-as": "^0.1.1", "cybercog/laravel-optimus": "^3.8", "danharrin/livewire-rate-limiting": "*", "doctrine/dbal": "*", "google/apps-meet": "^0.4.0", "guzzlehttp/guzzle": "^7.7", "hamed/laravel-google-calendar": "*", "http-interop/http-factory-guzzle": "^1.0", "intervention/image": "^3.0", "jeffreyvr/dropblockeditor": "^0.4.0", "jenssegers/agent": "*", "ladumor/laravel-pwa": "^0.0.4", "laravel/framework": "^10.48", "laravel/horizon": "^5.22", "laravel/nova": "^4.0", "laravel/octane": "^2.12", "laravel/scout": "^9.1", "laravel/socialite": "^5.8", "laravel/telescope": "^5.2", "laravel/tinker": "^2.5", "league/flysystem": "*", "league/flysystem-aws-s3-v3": "^3.15", "livewire/livewire": "^v3.6", "maatwebsite/excel": "^3.1", "masbug/flysystem-google-drive-ext": "^2.2", "meilisearch/meilisearch-php": "^0.18.2", "morilog/jalali": "3.*", "oneduo/nova-file-manager": "^0.12.3", "propaganistas/laravel-phone": "^5.1", "rappasoft/laravel-livewire-tables": "^3.7", "sadegh19b/laravel-persian-validation": "^1.2", "sentry/sentry-laravel": "^4.15", "shetabit/payment": "^6.2", "simshaun/recurr": "*", "spatie/laravel-data": "*", "spatie/laravel-slack-alerts": "^1.3", "spiral/roadrunner-cli": "^2.6.0", "spiral/roadrunner-http": "^3.3.0", "vinkla/hashids": "^11.0", "whitecube/nova-flexible-content": "^1.0.6", "yajra/laravel-datatables-oracle": "*"}, "require-dev": {"fakerphp/faker": "^1.9.1", "kavenegar/laravel": "*", "laravel/pint": "^1.17", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "platform-check": false, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "path", "url": "./packages/laravel-google-calendar"}, {"type": "path", "url": "./packages/nova"}]}